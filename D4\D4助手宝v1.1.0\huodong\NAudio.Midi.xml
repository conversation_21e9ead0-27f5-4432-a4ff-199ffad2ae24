<?xml version="1.0"?>
<doc>
    <assembly>
        <name>NAudio.Midi</name>
    </assembly>
    <members>
        <member name="M:NAudio.Utils.MergeSort.Sort``1(System.Collections.Generic.IList{``0},System.Int32,System.Int32,System.Collections.Generic.IComparer{``0})">
            <summary>
            In-place and stable implementation of MergeSort
            </summary>
        </member>
        <member name="M:NAudio.Utils.MergeSort.Sort``1(System.Collections.Generic.IList{``0})">
            <summary>
            MergeSort a list of comparable items
            </summary>
        </member>
        <member name="M:NAudio.Utils.MergeSort.Sort``1(System.Collections.Generic.IList{``0},System.Collections.Generic.IComparer{``0})">
            <summary>
            MergeSort a list 
            </summary>
        </member>
        <member name="T:NAudio.Midi.ChannelAfterTouchEvent">
            <summary>
            Represents a MIDI Channel AfterTouch Event.
            </summary>
        </member>
        <member name="M:NAudio.Midi.ChannelAfterTouchEvent.#ctor(System.IO.BinaryReader)">
            <summary>
            Creates a new ChannelAfterTouchEvent from raw MIDI data
            </summary>
            <param name="br">A binary reader</param>
        </member>
        <member name="M:NAudio.Midi.ChannelAfterTouchEvent.#ctor(System.Int64,System.Int32,System.Int32)">
            <summary>
            Creates a new Channel After-Touch Event
            </summary>
            <param name="absoluteTime">Absolute time</param>
            <param name="channel">Channel</param>
            <param name="afterTouchPressure">After-touch pressure</param>
        </member>
        <member name="M:NAudio.Midi.ChannelAfterTouchEvent.Export(System.Int64@,System.IO.BinaryWriter)">
            <summary>
            Calls base class export first, then exports the data 
            specific to this event
            <seealso cref="M:NAudio.Midi.MidiEvent.Export(System.Int64@,System.IO.BinaryWriter)">MidiEvent.Export</seealso>
            </summary>
        </member>
        <member name="P:NAudio.Midi.ChannelAfterTouchEvent.AfterTouchPressure">
            <summary>
            The aftertouch pressure value
            </summary>
        </member>
        <member name="M:NAudio.Midi.ChannelAfterTouchEvent.GetAsShortMessage">
            <summary>
            <see cref="M:NAudio.Midi.MidiEvent.GetAsShortMessage" />
            </summary>
        </member>
        <member name="M:NAudio.Midi.ChannelAfterTouchEvent.ToString">
            <summary>
            Describes this channel after-touch event
            </summary>
        </member>
        <member name="T:NAudio.Midi.ControlChangeEvent">
            <summary>
            Represents a MIDI control change event
            </summary>
        </member>
        <member name="M:NAudio.Midi.ControlChangeEvent.#ctor(System.IO.BinaryReader)">
            <summary>
            Reads a control change event from a MIDI stream
            </summary>
            <param name="br">Binary reader on the MIDI stream</param>
        </member>
        <member name="M:NAudio.Midi.ControlChangeEvent.#ctor(System.Int64,System.Int32,NAudio.Midi.MidiController,System.Int32)">
            <summary>
            Creates a control change event
            </summary>
            <param name="absoluteTime">Time</param>
            <param name="channel">MIDI Channel Number</param>
            <param name="controller">The MIDI Controller</param>
            <param name="controllerValue">Controller value</param>
        </member>
        <member name="M:NAudio.Midi.ControlChangeEvent.ToString">
            <summary>
            Describes this control change event
            </summary>
            <returns>A string describing this event</returns>
        </member>
        <member name="M:NAudio.Midi.ControlChangeEvent.GetAsShortMessage">
            <summary>
            <see cref="M:NAudio.Midi.MidiEvent.GetAsShortMessage" />
            </summary>
        </member>
        <member name="M:NAudio.Midi.ControlChangeEvent.Export(System.Int64@,System.IO.BinaryWriter)">
            <summary>
            Calls base class export first, then exports the data 
            specific to this event
            <seealso cref="M:NAudio.Midi.MidiEvent.Export(System.Int64@,System.IO.BinaryWriter)">MidiEvent.Export</seealso>
            </summary>
        </member>
        <member name="P:NAudio.Midi.ControlChangeEvent.Controller">
            <summary>
            The controller number
            </summary>
        </member>
        <member name="P:NAudio.Midi.ControlChangeEvent.ControllerValue">
            <summary>
            The controller value
            </summary>
        </member>
        <member name="T:NAudio.Midi.KeySignatureEvent">
            <summary>
            Represents a MIDI key signature event event
            </summary>
        </member>
        <member name="M:NAudio.Midi.KeySignatureEvent.#ctor(System.IO.BinaryReader,System.Int32)">
            <summary>
            Reads a new track sequence number event from a MIDI stream
            </summary>
            <param name="br">The MIDI stream</param>
            <param name="length">the data length</param>
        </member>
        <member name="M:NAudio.Midi.KeySignatureEvent.#ctor(System.Int32,System.Int32,System.Int64)">
            <summary>
            Creates a new Key signature event with the specified data
            </summary>
        </member>
        <member name="M:NAudio.Midi.KeySignatureEvent.Clone">
            <summary>
            Creates a deep clone of this MIDI event.
            </summary>
        </member>
        <member name="P:NAudio.Midi.KeySignatureEvent.SharpsFlats">
            <summary>
            Number of sharps or flats
            </summary>
        </member>
        <member name="P:NAudio.Midi.KeySignatureEvent.MajorMinor">
            <summary>
            Major or Minor key
            </summary>
        </member>
        <member name="M:NAudio.Midi.KeySignatureEvent.ToString">
            <summary>
            Describes this event
            </summary>
            <returns>String describing the event</returns>
        </member>
        <member name="M:NAudio.Midi.KeySignatureEvent.Export(System.Int64@,System.IO.BinaryWriter)">
            <summary>
            Calls base class export first, then exports the data 
            specific to this event
            <seealso cref="M:NAudio.Midi.MidiEvent.Export(System.Int64@,System.IO.BinaryWriter)">MidiEvent.Export</seealso>
            </summary>
        </member>
        <member name="T:NAudio.Midi.MetaEvent">
            <summary>
            Represents a MIDI meta event
            </summary>
        </member>
        <member name="P:NAudio.Midi.MetaEvent.MetaEventType">
            <summary>
            Gets the type of this meta event
            </summary>
        </member>
        <member name="M:NAudio.Midi.MetaEvent.#ctor">
            <summary>
            Empty constructor
            </summary>
        </member>
        <member name="M:NAudio.Midi.MetaEvent.#ctor(NAudio.Midi.MetaEventType,System.Int32,System.Int64)">
            <summary>
            Custom constructor for use by derived types, who will manage the data themselves
            </summary>
            <param name="metaEventType">Meta event type</param>
            <param name="metaDataLength">Meta data length</param>
            <param name="absoluteTime">Absolute time</param>
        </member>
        <member name="M:NAudio.Midi.MetaEvent.Clone">
            <summary>
            Creates a deep clone of this MIDI event.
            </summary>
        </member>
        <member name="M:NAudio.Midi.MetaEvent.ReadMetaEvent(System.IO.BinaryReader)">
            <summary>
            Reads a meta-event from a stream
            </summary>
            <param name="br">A binary reader based on the stream of MIDI data</param>
            <returns>A new MetaEvent object</returns>
        </member>
        <member name="M:NAudio.Midi.MetaEvent.ToString">
            <summary>
            Describes this meta event
            </summary>
        </member>
        <member name="M:NAudio.Midi.MetaEvent.Export(System.Int64@,System.IO.BinaryWriter)">
            <summary>
            <see cref="M:NAudio.Midi.MidiEvent.Export(System.Int64@,System.IO.BinaryWriter)"/>
            </summary>
        </member>
        <member name="T:NAudio.Midi.MetaEventType">
            <summary>
            MIDI MetaEvent Type
            </summary>
        </member>
        <member name="F:NAudio.Midi.MetaEventType.TrackSequenceNumber">
            <summary>Track sequence number</summary>
        </member>
        <member name="F:NAudio.Midi.MetaEventType.TextEvent">
            <summary>Text event</summary>
        </member>
        <member name="F:NAudio.Midi.MetaEventType.Copyright">
            <summary>Copyright</summary>
        </member>
        <member name="F:NAudio.Midi.MetaEventType.SequenceTrackName">
            <summary>Sequence track name</summary>
        </member>
        <member name="F:NAudio.Midi.MetaEventType.TrackInstrumentName">
            <summary>Track instrument name</summary>
        </member>
        <member name="F:NAudio.Midi.MetaEventType.Lyric">
            <summary>Lyric</summary>
        </member>
        <member name="F:NAudio.Midi.MetaEventType.Marker">
            <summary>Marker</summary>
        </member>
        <member name="F:NAudio.Midi.MetaEventType.CuePoint">
            <summary>Cue point</summary>
        </member>
        <member name="F:NAudio.Midi.MetaEventType.ProgramName">
            <summary>Program (patch) name</summary>
        </member>
        <member name="F:NAudio.Midi.MetaEventType.DeviceName">
            <summary>Device (port) name</summary>
        </member>
        <member name="F:NAudio.Midi.MetaEventType.MidiChannel">
            <summary>MIDI Channel (not official?)</summary>
        </member>
        <member name="F:NAudio.Midi.MetaEventType.MidiPort">
            <summary>MIDI Port (not official?)</summary>
        </member>
        <member name="F:NAudio.Midi.MetaEventType.EndTrack">
            <summary>End track</summary>
        </member>
        <member name="F:NAudio.Midi.MetaEventType.SetTempo">
            <summary>Set tempo</summary>
        </member>
        <member name="F:NAudio.Midi.MetaEventType.SmpteOffset">
            <summary>SMPTE offset</summary>
        </member>
        <member name="F:NAudio.Midi.MetaEventType.TimeSignature">
            <summary>Time signature</summary>
        </member>
        <member name="F:NAudio.Midi.MetaEventType.KeySignature">
            <summary>Key signature</summary>
        </member>
        <member name="F:NAudio.Midi.MetaEventType.SequencerSpecific">
            <summary>Sequencer specific</summary>
        </member>
        <member name="T:NAudio.Midi.MidiCommandCode">
            <summary>
            MIDI command codes
            </summary>
        </member>
        <member name="F:NAudio.Midi.MidiCommandCode.NoteOff">
            <summary>Note Off</summary>
        </member>
        <member name="F:NAudio.Midi.MidiCommandCode.NoteOn">
            <summary>Note On</summary>
        </member>
        <member name="F:NAudio.Midi.MidiCommandCode.KeyAfterTouch">
            <summary>Key After-touch</summary>
        </member>
        <member name="F:NAudio.Midi.MidiCommandCode.ControlChange">
            <summary>Control change</summary>
        </member>
        <member name="F:NAudio.Midi.MidiCommandCode.PatchChange">
            <summary>Patch change</summary>
        </member>
        <member name="F:NAudio.Midi.MidiCommandCode.ChannelAfterTouch">
            <summary>Channel after-touch</summary>
        </member>
        <member name="F:NAudio.Midi.MidiCommandCode.PitchWheelChange">
            <summary>Pitch wheel change</summary>
        </member>
        <member name="F:NAudio.Midi.MidiCommandCode.Sysex">
            <summary>Sysex message</summary>
        </member>
        <member name="F:NAudio.Midi.MidiCommandCode.Eox">
            <summary>Eox (comes at end of a sysex message)</summary>
        </member>
        <member name="F:NAudio.Midi.MidiCommandCode.TimingClock">
            <summary>Timing clock (used when synchronization is required)</summary>
        </member>
        <member name="F:NAudio.Midi.MidiCommandCode.StartSequence">
            <summary>Start sequence</summary>
        </member>
        <member name="F:NAudio.Midi.MidiCommandCode.ContinueSequence">
            <summary>Continue sequence</summary>
        </member>
        <member name="F:NAudio.Midi.MidiCommandCode.StopSequence">
            <summary>Stop sequence</summary>
        </member>
        <member name="F:NAudio.Midi.MidiCommandCode.AutoSensing">
            <summary>Auto-Sensing</summary>
        </member>
        <member name="F:NAudio.Midi.MidiCommandCode.MetaEvent">
            <summary>Meta-event</summary>
        </member>
        <member name="T:NAudio.Midi.MidiController">
            <summary>
            MidiController enumeration
            http://www.midi.org/techspecs/midimessages.php#3
            </summary>
        </member>
        <member name="F:NAudio.Midi.MidiController.BankSelect">
            <summary>Bank Select (MSB)</summary>
        </member>
        <member name="F:NAudio.Midi.MidiController.Modulation">
            <summary>Modulation (MSB)</summary>
        </member>
        <member name="F:NAudio.Midi.MidiController.BreathController">
            <summary>Breath Controller</summary>
        </member>
        <member name="F:NAudio.Midi.MidiController.FootController">
            <summary>Foot controller (MSB)</summary>
        </member>
        <member name="F:NAudio.Midi.MidiController.MainVolume">
            <summary>Main volume</summary>
        </member>
        <member name="F:NAudio.Midi.MidiController.Pan">
            <summary>Pan</summary>
        </member>
        <member name="F:NAudio.Midi.MidiController.Expression">
            <summary>Expression</summary>
        </member>
        <member name="F:NAudio.Midi.MidiController.BankSelectLsb">
            <summary>Bank Select LSB</summary>
        </member>
        <member name="F:NAudio.Midi.MidiController.Sustain">
            <summary>Sustain</summary>
        </member>
        <member name="F:NAudio.Midi.MidiController.Portamento">
            <summary>Portamento On/Off</summary>
        </member>
        <member name="F:NAudio.Midi.MidiController.Sostenuto">
            <summary>Sostenuto On/Off</summary>
        </member>
        <member name="F:NAudio.Midi.MidiController.SoftPedal">
            <summary>Soft Pedal On/Off</summary>
        </member>
        <member name="F:NAudio.Midi.MidiController.LegatoFootswitch">
            <summary>Legato Footswitch</summary>
        </member>
        <member name="F:NAudio.Midi.MidiController.ResetAllControllers">
            <summary>Reset all controllers</summary>
        </member>
        <member name="F:NAudio.Midi.MidiController.AllNotesOff">
            <summary>All notes off</summary>
        </member>
        <member name="T:NAudio.Midi.MidiEvent">
            <summary>
            Represents an individual MIDI event
            </summary>
        </member>
        <member name="F:NAudio.Midi.MidiEvent.commandCode">
            <summary>The MIDI command code</summary>
        </member>
        <member name="M:NAudio.Midi.MidiEvent.FromRawMessage(System.Int32)">
            <summary>
            Creates a MidiEvent from a raw message received using
            the MME MIDI In APIs
            </summary>
            <param name="rawMessage">The short MIDI message</param>
            <returns>A new MIDI Event</returns>
        </member>
        <member name="M:NAudio.Midi.MidiEvent.ReadNextEvent(System.IO.BinaryReader,NAudio.Midi.MidiEvent)">
            <summary>
            Constructs a MidiEvent from a BinaryStream
            </summary>
            <param name="br">The binary stream of MIDI data</param>
            <param name="previous">The previous MIDI event (pass null for first event)</param>
            <returns>A new MidiEvent</returns>
        </member>
        <member name="M:NAudio.Midi.MidiEvent.GetAsShortMessage">
            <summary>
            Converts this MIDI event to a short message (32 bit integer) that
            can be sent by the Windows MIDI out short message APIs
            Cannot be implemented for all MIDI messages
            </summary>
            <returns>A short message</returns>
        </member>
        <member name="M:NAudio.Midi.MidiEvent.#ctor">
            <summary>
            Default constructor
            </summary>
        </member>
        <member name="M:NAudio.Midi.MidiEvent.#ctor(System.Int64,System.Int32,NAudio.Midi.MidiCommandCode)">
            <summary>
            Creates a MIDI event with specified parameters
            </summary>
            <param name="absoluteTime">Absolute time of this event</param>
            <param name="channel">MIDI channel number</param>
            <param name="commandCode">MIDI command code</param>
        </member>
        <member name="M:NAudio.Midi.MidiEvent.Clone">
            <summary>
            Creates a deep clone of this MIDI event.
            </summary>
        </member>
        <member name="P:NAudio.Midi.MidiEvent.Channel">
            <summary>
            The MIDI Channel Number for this event (1-16)
            </summary>
        </member>
        <member name="P:NAudio.Midi.MidiEvent.DeltaTime">
            <summary>
            The Delta time for this event
            </summary>
        </member>
        <member name="P:NAudio.Midi.MidiEvent.AbsoluteTime">
            <summary>
            The absolute time for this event
            </summary>
        </member>
        <member name="P:NAudio.Midi.MidiEvent.CommandCode">
            <summary>
            The command code for this event
            </summary>
        </member>
        <member name="M:NAudio.Midi.MidiEvent.IsNoteOff(NAudio.Midi.MidiEvent)">
            <summary>
            Whether this is a note off event
            </summary>
        </member>
        <member name="M:NAudio.Midi.MidiEvent.IsNoteOn(NAudio.Midi.MidiEvent)">
            <summary>
            Whether this is a note on event
            </summary>
        </member>
        <member name="M:NAudio.Midi.MidiEvent.IsEndTrack(NAudio.Midi.MidiEvent)">
            <summary>
            Determines if this is an end track event
            </summary>
        </member>
        <member name="M:NAudio.Midi.MidiEvent.ToString">
            <summary>
            Displays a summary of the MIDI event
            </summary>
            <returns>A string containing a brief description of this MIDI event</returns>
        </member>
        <member name="M:NAudio.Midi.MidiEvent.ReadVarInt(System.IO.BinaryReader)">
            <summary>
            Utility function that can read a variable length integer from a binary stream
            </summary>
            <param name="br">The binary stream</param>
            <returns>The integer read</returns>
        </member>
        <member name="M:NAudio.Midi.MidiEvent.WriteVarInt(System.IO.BinaryWriter,System.Int32)">
            <summary>
            Writes a variable length integer to a binary stream
            </summary>
            <param name="writer">Binary stream</param>
            <param name="value">The value to write</param>
        </member>
        <member name="M:NAudio.Midi.MidiEvent.Export(System.Int64@,System.IO.BinaryWriter)">
            <summary>
            Exports this MIDI event's data
            Overriden in derived classes, but they should call this version
            </summary>
            <param name="absoluteTime">Absolute time used to calculate delta. 
            Is updated ready for the next delta calculation</param>
            <param name="writer">Stream to write to</param>
        </member>
        <member name="T:NAudio.Midi.MidiEventCollection">
            <summary>
            A helper class to manage collection of MIDI events
            It has the ability to organise them in tracks
            </summary>
        </member>
        <member name="M:NAudio.Midi.MidiEventCollection.#ctor(System.Int32,System.Int32)">
            <summary>
            Creates a new Midi Event collection
            </summary>
            <param name="midiFileType">Initial file type</param>
            <param name="deltaTicksPerQuarterNote">Delta Ticks Per Quarter Note</param>
        </member>
        <member name="P:NAudio.Midi.MidiEventCollection.Tracks">
            <summary>
            The number of tracks
            </summary>
        </member>
        <member name="P:NAudio.Midi.MidiEventCollection.StartAbsoluteTime">
            <summary>
            The absolute time that should be considered as time zero
            Not directly used here, but useful for timeshifting applications
            </summary>
        </member>
        <member name="P:NAudio.Midi.MidiEventCollection.DeltaTicksPerQuarterNote">
            <summary>
            The number of ticks per quarter note
            </summary>
        </member>
        <member name="M:NAudio.Midi.MidiEventCollection.GetTrackEvents(System.Int32)">
            <summary>
            Gets events on a specified track
            </summary>
            <param name="trackNumber">Track number</param>
            <returns>The list of events</returns>
        </member>
        <member name="P:NAudio.Midi.MidiEventCollection.Item(System.Int32)">
            <summary>
            Gets events on a specific track
            </summary>
            <param name="trackNumber">Track number</param>
            <returns>The list of events</returns>
        </member>
        <member name="M:NAudio.Midi.MidiEventCollection.AddTrack">
            <summary>
            Adds a new track
            </summary>
            <returns>The new track event list</returns>
        </member>
        <member name="M:NAudio.Midi.MidiEventCollection.AddTrack(System.Collections.Generic.IList{NAudio.Midi.MidiEvent})">
            <summary>
            Adds a new track
            </summary>
            <param name="initialEvents">Initial events to add to the new track</param>
            <returns>The new track event list</returns>
        </member>
        <member name="M:NAudio.Midi.MidiEventCollection.RemoveTrack(System.Int32)">
            <summary>
            Removes a track
            </summary>
            <param name="track">Track number to remove</param>
        </member>
        <member name="M:NAudio.Midi.MidiEventCollection.Clear">
            <summary>
            Clears all events
            </summary>
        </member>
        <member name="P:NAudio.Midi.MidiEventCollection.MidiFileType">
            <summary>
            The MIDI file type
            </summary>
        </member>
        <member name="M:NAudio.Midi.MidiEventCollection.AddEvent(NAudio.Midi.MidiEvent,System.Int32)">
            <summary>
            Adds an event to the appropriate track depending on file type
            </summary>
            <param name="midiEvent">The event to be added</param>
            <param name="originalTrack">The original (or desired) track number</param>
            <remarks>When adding events in type 0 mode, the originalTrack parameter
            is ignored. If in type 1 mode, it will use the original track number to
            store the new events. If the original track was 0 and this is a channel based
            event, it will create new tracks if necessary and put it on the track corresponding
            to its channel number</remarks>
        </member>
        <member name="M:NAudio.Midi.MidiEventCollection.PrepareForExport">
            <summary>
            Sorts, removes empty tracks and adds end track markers
            </summary>
        </member>
        <member name="M:NAudio.Midi.MidiEventCollection.GetEnumerator">
            <summary>
            Gets an enumerator for the lists of track events
            </summary>
        </member>
        <member name="M:NAudio.Midi.MidiEventCollection.System#Collections#IEnumerable#GetEnumerator">
            <summary>
            Gets an enumerator for the lists of track events
            </summary>
        </member>
        <member name="T:NAudio.Midi.MidiEventComparer">
            <summary>
            Utility class for comparing MidiEvent objects
            </summary>
        </member>
        <member name="M:NAudio.Midi.MidiEventComparer.Compare(NAudio.Midi.MidiEvent,NAudio.Midi.MidiEvent)">
            <summary>
            Compares two MidiEvents
            Sorts by time, with EndTrack always sorted to the end
            </summary>
        </member>
        <member name="T:NAudio.Midi.MidiFile">
            <summary>
            Class able to read a MIDI file
            </summary>
        </member>
        <member name="M:NAudio.Midi.MidiFile.#ctor(System.String)">
            <summary>
            Opens a MIDI file for reading
            </summary>
            <param name="filename">Name of MIDI file</param>
        </member>
        <member name="P:NAudio.Midi.MidiFile.FileFormat">
            <summary>
            MIDI File format
            </summary>
        </member>
        <member name="M:NAudio.Midi.MidiFile.#ctor(System.String,System.Boolean)">
            <summary>
            Opens a MIDI file for reading
            </summary>
            <param name="filename">Name of MIDI file</param>
            <param name="strictChecking">If true will error on non-paired note events</param>
        </member>
        <member name="M:NAudio.Midi.MidiFile.#ctor(System.IO.Stream,System.Boolean)">
            <summary>
            Opens a MIDI file stream for reading
            </summary>
            <param name="inputStream">The input stream containing a MIDI file</param>
            <param name="strictChecking">If true will error on non-paired note events</param>
        </member>
        <member name="P:NAudio.Midi.MidiFile.Events">
            <summary>
            The collection of events in this MIDI file
            </summary>
        </member>
        <member name="P:NAudio.Midi.MidiFile.Tracks">
            <summary>
            Number of tracks in this MIDI file
            </summary>
        </member>
        <member name="P:NAudio.Midi.MidiFile.DeltaTicksPerQuarterNote">
            <summary>
            Delta Ticks Per Quarter Note
            </summary>
        </member>
        <member name="M:NAudio.Midi.MidiFile.ToString">
            <summary>
            Describes the MIDI file
            </summary>
            <returns>A string describing the MIDI file and its events</returns>
        </member>
        <member name="M:NAudio.Midi.MidiFile.Export(System.String,NAudio.Midi.MidiEventCollection)">
            <summary>
            Exports a MIDI file
            </summary>
            <param name="filename">Filename to export to</param>
            <param name="events">Events to export</param>
        </member>
        <member name="T:NAudio.Midi.MidiIn">
            <summary>
            Represents a MIDI in device
            </summary>
        </member>
        <member name="E:NAudio.Midi.MidiIn.MessageReceived">
            <summary>
            Called when a MIDI message is received
            </summary>
        </member>
        <member name="E:NAudio.Midi.MidiIn.ErrorReceived">
            <summary>
            An invalid MIDI message
            </summary>
        </member>
        <member name="E:NAudio.Midi.MidiIn.SysexMessageReceived">
            <summary>
            Called when a Sysex MIDI message is received
            </summary>
        </member>
        <member name="P:NAudio.Midi.MidiIn.NumberOfDevices">
            <summary>
            Gets the number of MIDI input devices available in the system
            </summary>
        </member>
        <member name="M:NAudio.Midi.MidiIn.#ctor(System.Int32)">
            <summary>
            Opens a specified MIDI in device
            </summary>
            <param name="deviceNo">The device number</param>
        </member>
        <member name="M:NAudio.Midi.MidiIn.Close">
            <summary>
            Closes this MIDI in device
            </summary>
        </member>
        <member name="M:NAudio.Midi.MidiIn.Dispose">
            <summary>
            Closes this MIDI in device
            </summary>
        </member>
        <member name="M:NAudio.Midi.MidiIn.Start">
            <summary>
            Start the MIDI in device
            </summary>
        </member>
        <member name="M:NAudio.Midi.MidiIn.Stop">
            <summary>
            Stop the MIDI in device
            </summary>
        </member>
        <member name="M:NAudio.Midi.MidiIn.Reset">
            <summary>
            Reset the MIDI in device
            </summary>
        </member>
        <member name="M:NAudio.Midi.MidiIn.CreateSysexBuffers(System.Int32,System.Int32)">
            <summary>
            Create a number of buffers and make them available to receive incoming Sysex messages
            </summary>
            <param name="bufferSize">The size of each buffer, ideally large enough to hold a complete message from the device</param>
            <param name="numberOfBuffers">The number of buffers needed to handle incoming Midi while busy</param>
        </member>
        <member name="M:NAudio.Midi.MidiIn.DeviceInfo(System.Int32)">
            <summary>
            Gets the MIDI in device info
            </summary>
        </member>
        <member name="M:NAudio.Midi.MidiIn.Dispose(System.Boolean)">
            <summary>
            Closes the MIDI in device
            </summary>
            <param name="disposing">True if called from Dispose</param>
        </member>
        <member name="M:NAudio.Midi.MidiIn.Finalize">
            <summary>
            Cleanup
            </summary>
        </member>
        <member name="T:NAudio.Midi.MidiInCapabilities">
            <summary>
            MIDI In Device Capabilities
            </summary>
        </member>
        <member name="F:NAudio.Midi.MidiInCapabilities.manufacturerId">
            <summary>
            wMid
            </summary>
        </member>
        <member name="F:NAudio.Midi.MidiInCapabilities.productId">
            <summary>
            wPid
            </summary>
        </member>
        <member name="F:NAudio.Midi.MidiInCapabilities.driverVersion">
            <summary>
            vDriverVersion
            </summary>
        </member>
        <member name="F:NAudio.Midi.MidiInCapabilities.productName">
            <summary>
            Product Name
            </summary>
        </member>
        <member name="F:NAudio.Midi.MidiInCapabilities.support">
            <summary>
            Support - Reserved
            </summary>
        </member>
        <member name="P:NAudio.Midi.MidiInCapabilities.Manufacturer">
            <summary>
            Gets the manufacturer of this device
            </summary>
        </member>
        <member name="P:NAudio.Midi.MidiInCapabilities.ProductId">
            <summary>
            Gets the product identifier (manufacturer specific)
            </summary>
        </member>
        <member name="P:NAudio.Midi.MidiInCapabilities.ProductName">
            <summary>
            Gets the product name
            </summary>
        </member>
        <member name="T:NAudio.Midi.MidiInMessageEventArgs">
            <summary>
            MIDI In Message Information
            </summary>
        </member>
        <member name="M:NAudio.Midi.MidiInMessageEventArgs.#ctor(System.Int32,System.Int32)">
            <summary>
            Create a new MIDI In Message EventArgs
            </summary>
            <param name="message"></param>
            <param name="timestamp"></param>
        </member>
        <member name="P:NAudio.Midi.MidiInMessageEventArgs.RawMessage">
            <summary>
            The Raw message received from the MIDI In API
            </summary>
        </member>
        <member name="P:NAudio.Midi.MidiInMessageEventArgs.MidiEvent">
            <summary>
            The raw message interpreted as a MidiEvent
            </summary>
        </member>
        <member name="P:NAudio.Midi.MidiInMessageEventArgs.Timestamp">
            <summary>
            The timestamp in milliseconds for this message
            </summary>
        </member>
        <member name="T:NAudio.Midi.MidiInSysexMessageEventArgs">
            <summary>
            MIDI In Sysex Message Information
            </summary>
        </member>
        <member name="M:NAudio.Midi.MidiInSysexMessageEventArgs.#ctor(System.Byte[],System.Int32)">
            <summary>
            Create a new Sysex MIDI In Message EventArgs
            </summary>
            <param name="sysexBytes">The Sysex byte array received</param>
            <param name="timestamp">Milliseconds since MidiInStart</param>
        </member>
        <member name="P:NAudio.Midi.MidiInSysexMessageEventArgs.SysexBytes">
            <summary>
            The Raw Sysex bytes received in a long MIDI message
            </summary>
        </member>
        <member name="P:NAudio.Midi.MidiInSysexMessageEventArgs.Timestamp">
            <summary>
            The timestamp in milliseconds (since MidiInStart) for this message
            </summary>
        </member>
        <member name="F:NAudio.Midi.MidiInterop.MidiInMessage.Open">
            <summary>
            MIM_OPEN
            </summary>
        </member>
        <member name="F:NAudio.Midi.MidiInterop.MidiInMessage.Close">
            <summary>
            MIM_CLOSE
            </summary>
        </member>
        <member name="F:NAudio.Midi.MidiInterop.MidiInMessage.Data">
            <summary>
            MIM_DATA
            </summary>
        </member>
        <member name="F:NAudio.Midi.MidiInterop.MidiInMessage.LongData">
            <summary>
            MIM_LONGDATA
            </summary>
        </member>
        <member name="F:NAudio.Midi.MidiInterop.MidiInMessage.Error">
            <summary>
            MIM_ERROR
            </summary>
        </member>
        <member name="F:NAudio.Midi.MidiInterop.MidiInMessage.LongError">
            <summary>
            MIM_LONGERROR
            </summary>
        </member>
        <member name="F:NAudio.Midi.MidiInterop.MidiInMessage.MoreData">
            <summary>
            MIM_MOREDATA
            </summary>
        </member>
        <member name="F:NAudio.Midi.MidiInterop.MidiOutMessage.Open">
            <summary>
            MOM_OPEN
            </summary>
        </member>
        <member name="F:NAudio.Midi.MidiInterop.MidiOutMessage.Close">
            <summary>
            MOM_CLOSE
            </summary>
        </member>
        <member name="F:NAudio.Midi.MidiInterop.MidiOutMessage.Done">
            <summary>
            MOM_DONE
            </summary>
        </member>
        <member name="T:NAudio.Midi.MidiMessage">
            <summary>
            Represents a MIDI message
            </summary>
        </member>
        <member name="M:NAudio.Midi.MidiMessage.#ctor(System.Int32,System.Int32,System.Int32)">
            <summary>
            Creates a new MIDI message
            </summary>
            <param name="status">Status</param>
            <param name="data1">Data parameter 1</param>
            <param name="data2">Data parameter 2</param>
        </member>
        <member name="M:NAudio.Midi.MidiMessage.#ctor(System.Int32)">
            <summary>
            Creates a new MIDI message from a raw message
            </summary>
            <param name="rawData">A packed MIDI message from an MMIO function</param>
        </member>
        <member name="M:NAudio.Midi.MidiMessage.StartNote(System.Int32,System.Int32,System.Int32)">
            <summary>
            Creates a Note On message
            </summary>
            <param name="note">Note number (0 to 127)</param>
            <param name="volume">Volume (0 to 127)</param>
            <param name="channel">MIDI channel (1 to 16)</param>
            <returns>A new MidiMessage object</returns>
        </member>
        <member name="M:NAudio.Midi.MidiMessage.StopNote(System.Int32,System.Int32,System.Int32)">
            <summary>
            Creates a Note Off message
            </summary>
            <param name="note">Note number</param>
            <param name="volume">Volume </param>
            <param name="channel">MIDI channel (1-16)</param>
            <returns>A new MidiMessage object</returns>
        </member>
        <member name="M:NAudio.Midi.MidiMessage.ChangePatch(System.Int32,System.Int32)">
            <summary>
            Creates a patch change message
            </summary>
            <param name="patch">The patch number</param>
            <param name="channel">The MIDI channel number (1-16)</param>
            <returns>A new MidiMessageObject</returns>
        </member>
        <member name="M:NAudio.Midi.MidiMessage.ChangeControl(System.Int32,System.Int32,System.Int32)">
            <summary>
            Creates a Control Change message
            </summary>
            <param name="controller">The controller number to change</param>
            <param name="value">The value to set the controller to</param>
            <param name="channel">The MIDI channel number (1-16)</param>
            <returns>A new MidiMessageObject</returns>
        </member>
        <member name="P:NAudio.Midi.MidiMessage.RawData">
            <summary>
            Returns the raw MIDI message data
            </summary>
        </member>
        <member name="T:NAudio.Midi.MidiOut">
            <summary>
            Represents a MIDI out device
            </summary>
        </member>
        <member name="P:NAudio.Midi.MidiOut.NumberOfDevices">
            <summary>
            Gets the number of MIDI devices available in the system
            </summary>
        </member>
        <member name="M:NAudio.Midi.MidiOut.DeviceInfo(System.Int32)">
            <summary>
            Gets the MIDI Out device info
            </summary>
        </member>
        <member name="M:NAudio.Midi.MidiOut.#ctor(System.Int32)">
            <summary>
            Opens a specified MIDI out device
            </summary>
            <param name="deviceNo">The device number</param>
        </member>
        <member name="M:NAudio.Midi.MidiOut.Close">
            <summary>
            Closes this MIDI out device
            </summary>
        </member>
        <member name="M:NAudio.Midi.MidiOut.Dispose">
            <summary>
            Closes this MIDI out device
            </summary>
        </member>
        <member name="P:NAudio.Midi.MidiOut.Volume">
            <summary>
            Gets or sets the volume for this MIDI out device
            </summary>
        </member>
        <member name="M:NAudio.Midi.MidiOut.Reset">
            <summary>
            Resets the MIDI out device
            </summary>
        </member>
        <member name="M:NAudio.Midi.MidiOut.SendDriverMessage(System.Int32,System.Int32,System.Int32)">
            <summary>
            Sends a MIDI out message
            </summary>
            <param name="message">Message</param>
            <param name="param1">Parameter 1</param>
            <param name="param2">Parameter 2</param>
        </member>
        <member name="M:NAudio.Midi.MidiOut.Send(System.Int32)">
            <summary>
            Sends a MIDI message to the MIDI out device
            </summary>
            <param name="message">The message to send</param>
        </member>
        <member name="M:NAudio.Midi.MidiOut.Dispose(System.Boolean)">
            <summary>
            Closes the MIDI out device
            </summary>
            <param name="disposing">True if called from Dispose</param>
        </member>
        <member name="M:NAudio.Midi.MidiOut.SendBuffer(System.Byte[])">
            <summary>
            Send a long message, for example sysex.
            </summary>
            <param name="byteBuffer">The bytes to send.</param>
        </member>
        <member name="M:NAudio.Midi.MidiOut.Finalize">
            <summary>
            Cleanup
            </summary>
        </member>
        <member name="T:NAudio.Midi.MidiOutCapabilities">
            <summary>
            class representing the capabilities of a MIDI out device
            MIDIOUTCAPS: http://msdn.microsoft.com/en-us/library/dd798467%28VS.85%29.aspx
            </summary>
        </member>
        <member name="F:NAudio.Midi.MidiOutCapabilities.MidiOutCapabilityFlags.Volume">
            <summary>
            MIDICAPS_VOLUME
            </summary>
        </member>
        <member name="F:NAudio.Midi.MidiOutCapabilities.MidiOutCapabilityFlags.LeftRightVolume">
            <summary>
            separate left-right volume control
            MIDICAPS_LRVOLUME
            </summary>
        </member>
        <member name="F:NAudio.Midi.MidiOutCapabilities.MidiOutCapabilityFlags.PatchCaching">
            <summary>
            MIDICAPS_CACHE
            </summary>
        </member>
        <member name="F:NAudio.Midi.MidiOutCapabilities.MidiOutCapabilityFlags.Stream">
            <summary>
            MIDICAPS_STREAM
            driver supports midiStreamOut directly
            </summary>
        </member>
        <member name="P:NAudio.Midi.MidiOutCapabilities.Manufacturer">
            <summary>
            Gets the manufacturer of this device
            </summary>
        </member>
        <member name="P:NAudio.Midi.MidiOutCapabilities.ProductId">
            <summary>
            Gets the product identifier (manufacturer specific)
            </summary>
        </member>
        <member name="P:NAudio.Midi.MidiOutCapabilities.ProductName">
            <summary>
            Gets the product name
            </summary>
        </member>
        <member name="P:NAudio.Midi.MidiOutCapabilities.Voices">
            <summary>
            Returns the number of supported voices
            </summary>
        </member>
        <member name="P:NAudio.Midi.MidiOutCapabilities.Notes">
            <summary>
            Gets the polyphony of the device
            </summary>
        </member>
        <member name="P:NAudio.Midi.MidiOutCapabilities.SupportsAllChannels">
            <summary>
            Returns true if the device supports all channels
            </summary>
        </member>
        <member name="M:NAudio.Midi.MidiOutCapabilities.SupportsChannel(System.Int32)">
            <summary>
            Queries whether a particular channel is supported
            </summary>
            <param name="channel">Channel number to test</param>
            <returns>True if the channel is supported</returns>
        </member>
        <member name="P:NAudio.Midi.MidiOutCapabilities.SupportsPatchCaching">
            <summary>
            Returns true if the device supports patch caching
            </summary>
        </member>
        <member name="P:NAudio.Midi.MidiOutCapabilities.SupportsSeparateLeftAndRightVolume">
            <summary>
            Returns true if the device supports separate left and right volume
            </summary>
        </member>
        <member name="P:NAudio.Midi.MidiOutCapabilities.SupportsMidiStreamOut">
            <summary>
            Returns true if the device supports MIDI stream out
            </summary>
        </member>
        <member name="P:NAudio.Midi.MidiOutCapabilities.SupportsVolumeControl">
            <summary>
            Returns true if the device supports volume control
            </summary>
        </member>
        <member name="P:NAudio.Midi.MidiOutCapabilities.Technology">
            <summary>
            Returns the type of technology used by this MIDI out device
            </summary>
        </member>
        <member name="T:NAudio.Midi.MidiOutTechnology">
            <summary>
            Represents the different types of technology used by a MIDI out device
            </summary>
            <remarks>from mmsystem.h</remarks>
        </member>
        <member name="F:NAudio.Midi.MidiOutTechnology.MidiPort">
            <summary>The device is a MIDI port</summary>
        </member>
        <member name="F:NAudio.Midi.MidiOutTechnology.Synth">
            <summary>The device is a MIDI synth</summary>
        </member>
        <member name="F:NAudio.Midi.MidiOutTechnology.SquareWaveSynth">
            <summary>The device is a square wave synth</summary>
        </member>
        <member name="F:NAudio.Midi.MidiOutTechnology.FMSynth">
            <summary>The device is an FM synth</summary>
        </member>
        <member name="F:NAudio.Midi.MidiOutTechnology.MidiMapper">
            <summary>The device is a MIDI mapper</summary>
        </member>
        <member name="F:NAudio.Midi.MidiOutTechnology.WaveTableSynth">
            <summary>The device is a WaveTable synth</summary>
        </member>
        <member name="F:NAudio.Midi.MidiOutTechnology.SoftwareSynth">
            <summary>The device is a software synth</summary>
        </member>
        <member name="T:NAudio.Midi.NoteEvent">
            <summary>
            Represents a note MIDI event
            </summary>
        </member>
        <member name="M:NAudio.Midi.NoteEvent.#ctor(System.IO.BinaryReader)">
            <summary>
            Reads a NoteEvent from a stream of MIDI data
            </summary>
            <param name="br">Binary Reader for the stream</param>
        </member>
        <member name="M:NAudio.Midi.NoteEvent.#ctor(System.Int64,System.Int32,NAudio.Midi.MidiCommandCode,System.Int32,System.Int32)">
            <summary>
            Creates a MIDI Note Event with specified parameters
            </summary>
            <param name="absoluteTime">Absolute time of this event</param>
            <param name="channel">MIDI channel number</param>
            <param name="commandCode">MIDI command code</param>
            <param name="noteNumber">MIDI Note Number</param>
            <param name="velocity">MIDI Note Velocity</param>
        </member>
        <member name="M:NAudio.Midi.NoteEvent.GetAsShortMessage">
            <summary>
            <see cref="M:NAudio.Midi.MidiEvent.GetAsShortMessage" />
            </summary>
        </member>
        <member name="P:NAudio.Midi.NoteEvent.NoteNumber">
            <summary>
            The MIDI note number
            </summary>
        </member>
        <member name="P:NAudio.Midi.NoteEvent.Velocity">
            <summary>
            The note velocity
            </summary>
        </member>
        <member name="P:NAudio.Midi.NoteEvent.NoteName">
            <summary>
            The note name
            </summary>
        </member>
        <member name="M:NAudio.Midi.NoteEvent.ToString">
            <summary>
            Describes the Note Event
            </summary>
            <returns>Note event as a string</returns>
        </member>
        <member name="M:NAudio.Midi.NoteEvent.Export(System.Int64@,System.IO.BinaryWriter)">
            <summary>
            <see cref="M:NAudio.Midi.MidiEvent.Export(System.Int64@,System.IO.BinaryWriter)"/>
            </summary>
        </member>
        <member name="T:NAudio.Midi.NoteOnEvent">
            <summary>
            Represents a MIDI note on event
            </summary>
        </member>
        <member name="M:NAudio.Midi.NoteOnEvent.#ctor(System.IO.BinaryReader)">
            <summary>
            Reads a new Note On event from a stream of MIDI data
            </summary>
            <param name="br">Binary reader on the MIDI data stream</param>
        </member>
        <member name="M:NAudio.Midi.NoteOnEvent.#ctor(System.Int64,System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            Creates a NoteOn event with specified parameters
            </summary>
            <param name="absoluteTime">Absolute time of this event</param>
            <param name="channel">MIDI channel number</param>
            <param name="noteNumber">MIDI note number</param>
            <param name="velocity">MIDI note velocity</param>
            <param name="duration">MIDI note duration</param>
        </member>
        <member name="M:NAudio.Midi.NoteOnEvent.Clone">
            <summary>
            Creates a deep clone of this MIDI event.
            </summary>
        </member>
        <member name="P:NAudio.Midi.NoteOnEvent.OffEvent">
            <summary>
            The associated Note off event
            </summary>
        </member>
        <member name="P:NAudio.Midi.NoteOnEvent.NoteNumber">
            <summary>
            Get or set the Note Number, updating the off event at the same time
            </summary>
        </member>
        <member name="P:NAudio.Midi.NoteOnEvent.Channel">
            <summary>
            Get or set the channel, updating the off event at the same time
            </summary>
        </member>
        <member name="P:NAudio.Midi.NoteOnEvent.NoteLength">
            <summary>
            The duration of this note
            </summary>
            <remarks>
            There must be a note off event
            </remarks>
        </member>
        <member name="M:NAudio.Midi.NoteOnEvent.ToString">
            <summary>
            Calls base class export first, then exports the data 
            specific to this event
            <seealso cref="M:NAudio.Midi.MidiEvent.Export(System.Int64@,System.IO.BinaryWriter)">MidiEvent.Export</seealso>
            </summary>
        </member>
        <member name="T:NAudio.Midi.PatchChangeEvent">
            <summary>
            Represents a MIDI patch change event
            </summary>
        </member>
        <member name="M:NAudio.Midi.PatchChangeEvent.GetPatchName(System.Int32)">
            <summary>
            Gets the default MIDI instrument names
            </summary>
        </member>
        <member name="M:NAudio.Midi.PatchChangeEvent.#ctor(System.IO.BinaryReader)">
            <summary>
            Reads a new patch change event from a MIDI stream
            </summary>
            <param name="br">Binary reader for the MIDI stream</param>
        </member>
        <member name="M:NAudio.Midi.PatchChangeEvent.#ctor(System.Int64,System.Int32,System.Int32)">
            <summary>
            Creates a new patch change event
            </summary>
            <param name="absoluteTime">Time of the event</param>
            <param name="channel">Channel number</param>
            <param name="patchNumber">Patch number</param>
        </member>
        <member name="P:NAudio.Midi.PatchChangeEvent.Patch">
            <summary>
            The Patch Number
            </summary>
        </member>
        <member name="M:NAudio.Midi.PatchChangeEvent.ToString">
            <summary>
            Describes this patch change event
            </summary>
            <returns>String describing the patch change event</returns>
        </member>
        <member name="M:NAudio.Midi.PatchChangeEvent.GetAsShortMessage">
            <summary>
            Gets as a short message for sending with the midiOutShortMsg API
            </summary>
            <returns>short message</returns>
        </member>
        <member name="M:NAudio.Midi.PatchChangeEvent.Export(System.Int64@,System.IO.BinaryWriter)">
            <summary>
            Calls base class export first, then exports the data 
            specific to this event
            <seealso cref="M:NAudio.Midi.MidiEvent.Export(System.Int64@,System.IO.BinaryWriter)">MidiEvent.Export</seealso>
            </summary>
        </member>
        <member name="T:NAudio.Midi.PitchWheelChangeEvent">
            <summary>
            Represents a MIDI pitch wheel change event
            </summary>
        </member>
        <member name="M:NAudio.Midi.PitchWheelChangeEvent.#ctor(System.IO.BinaryReader)">
            <summary>
            Reads a pitch wheel change event from a MIDI stream
            </summary>
            <param name="br">The MIDI stream to read from</param>
        </member>
        <member name="M:NAudio.Midi.PitchWheelChangeEvent.#ctor(System.Int64,System.Int32,System.Int32)">
            <summary>
            Creates a new pitch wheel change event
            </summary>
            <param name="absoluteTime">Absolute event time</param>
            <param name="channel">Channel</param>
            <param name="pitchWheel">Pitch wheel value</param>
        </member>
        <member name="M:NAudio.Midi.PitchWheelChangeEvent.ToString">
            <summary>
            Describes this pitch wheel change event
            </summary>
            <returns>String describing this pitch wheel change event</returns>
        </member>
        <member name="P:NAudio.Midi.PitchWheelChangeEvent.Pitch">
            <summary>
            Pitch Wheel Value 0 is minimum, 0x2000 (8192) is default, 0x3FFF (16383) is maximum
            </summary>
        </member>
        <member name="M:NAudio.Midi.PitchWheelChangeEvent.GetAsShortMessage">
            <summary>
            Gets a short message
            </summary>
            <returns>Integer to sent as short message</returns>
        </member>
        <member name="M:NAudio.Midi.PitchWheelChangeEvent.Export(System.Int64@,System.IO.BinaryWriter)">
            <summary>
            Calls base class export first, then exports the data 
            specific to this event
            <seealso cref="M:NAudio.Midi.MidiEvent.Export(System.Int64@,System.IO.BinaryWriter)">MidiEvent.Export</seealso>
            </summary>
        </member>
        <member name="T:NAudio.Midi.RawMetaEvent">
            <summary>
            Represents a MIDI meta event with raw data
            </summary>
        </member>
        <member name="P:NAudio.Midi.RawMetaEvent.Data">
            <summary>
            Raw data contained in the meta event
            </summary>
        </member>
        <member name="M:NAudio.Midi.RawMetaEvent.#ctor(NAudio.Midi.MetaEventType,System.Int64,System.Byte[])">
            <summary>
             Creates a meta event with raw data
            </summary>
        </member>
        <member name="M:NAudio.Midi.RawMetaEvent.Clone">
            <summary>
            Creates a deep clone of this MIDI event.
            </summary>
        </member>
        <member name="M:NAudio.Midi.RawMetaEvent.ToString">
            <summary>
            Describes this meta event
            </summary>
        </member>
        <member name="M:NAudio.Midi.RawMetaEvent.Export(System.Int64@,System.IO.BinaryWriter)">
            <summary>
            <see cref="M:NAudio.Midi.MidiEvent.Export(System.Int64@,System.IO.BinaryWriter)"/>
            </summary>
        </member>
        <member name="T:NAudio.Midi.SequencerSpecificEvent">
            <summary>
            Represents a Sequencer Specific event
            </summary>
        </member>
        <member name="M:NAudio.Midi.SequencerSpecificEvent.#ctor(System.IO.BinaryReader,System.Int32)">
            <summary>
            Reads a new sequencer specific event from a MIDI stream
            </summary>
            <param name="br">The MIDI stream</param>
            <param name="length">The data length</param>
        </member>
        <member name="M:NAudio.Midi.SequencerSpecificEvent.#ctor(System.Byte[],System.Int64)">
            <summary>
            Creates a new Sequencer Specific event
            </summary>
            <param name="data">The sequencer specific data</param>
            <param name="absoluteTime">Absolute time of this event</param>
        </member>
        <member name="M:NAudio.Midi.SequencerSpecificEvent.Clone">
            <summary>
            Creates a deep clone of this MIDI event.
            </summary>
        </member>
        <member name="P:NAudio.Midi.SequencerSpecificEvent.Data">
            <summary>
            The contents of this sequencer specific
            </summary>
        </member>
        <member name="M:NAudio.Midi.SequencerSpecificEvent.ToString">
            <summary>
            Describes this MIDI text event
            </summary>
            <returns>A string describing this event</returns>
        </member>
        <member name="M:NAudio.Midi.SequencerSpecificEvent.Export(System.Int64@,System.IO.BinaryWriter)">
            <summary>
            Calls base class export first, then exports the data 
            specific to this event
            <seealso cref="M:NAudio.Midi.MidiEvent.Export(System.Int64@,System.IO.BinaryWriter)">MidiEvent.Export</seealso>
            </summary>
        </member>
        <member name="T:NAudio.Midi.SmpteOffsetEvent">
            <summary>
            SMPTE Offset Event
            </summary>
        </member>
        <member name="M:NAudio.Midi.SmpteOffsetEvent.#ctor(System.Byte,System.Byte,System.Byte,System.Byte,System.Byte)">
            <summary>
            Creates a new time signature event
            </summary>
        </member>
        <member name="M:NAudio.Midi.SmpteOffsetEvent.#ctor(System.IO.BinaryReader,System.Int32)">
            <summary>
            Reads a new time signature event from a MIDI stream
            </summary>
            <param name="br">The MIDI stream</param>
            <param name="length">The data length</param>
        </member>
        <member name="M:NAudio.Midi.SmpteOffsetEvent.Clone">
            <summary>
            Creates a deep clone of this MIDI event.
            </summary>
        </member>
        <member name="P:NAudio.Midi.SmpteOffsetEvent.Hours">
            <summary>
            Hours
            </summary>
        </member>
        <member name="P:NAudio.Midi.SmpteOffsetEvent.Minutes">
            <summary>
            Minutes
            </summary>
        </member>
        <member name="P:NAudio.Midi.SmpteOffsetEvent.Seconds">
            <summary>
            Seconds
            </summary>
        </member>
        <member name="P:NAudio.Midi.SmpteOffsetEvent.Frames">
            <summary>
            Frames
            </summary>
        </member>
        <member name="P:NAudio.Midi.SmpteOffsetEvent.SubFrames">
            <summary>
            SubFrames
            </summary>
        </member>
        <member name="M:NAudio.Midi.SmpteOffsetEvent.ToString">
            <summary>
            Describes this time signature event
            </summary>
            <returns>A string describing this event</returns>
        </member>
        <member name="M:NAudio.Midi.SmpteOffsetEvent.Export(System.Int64@,System.IO.BinaryWriter)">
            <summary>
            Calls base class export first, then exports the data 
            specific to this event
            <seealso cref="M:NAudio.Midi.MidiEvent.Export(System.Int64@,System.IO.BinaryWriter)">MidiEvent.Export</seealso>
            </summary>
        </member>
        <member name="T:NAudio.Midi.SysexEvent">
            <summary>
            Represents a MIDI sysex message
            </summary>
        </member>
        <member name="M:NAudio.Midi.SysexEvent.ReadSysexEvent(System.IO.BinaryReader)">
            <summary>
            Reads a sysex message from a MIDI stream
            </summary>
            <param name="br">Stream of MIDI data</param>
            <returns>a new sysex message</returns>
        </member>
        <member name="M:NAudio.Midi.SysexEvent.Clone">
            <summary>
            Creates a deep clone of this MIDI event.
            </summary>
        </member>
        <member name="M:NAudio.Midi.SysexEvent.ToString">
            <summary>
            Describes this sysex message
            </summary>
            <returns>A string describing the sysex message</returns>
        </member>
        <member name="M:NAudio.Midi.SysexEvent.Export(System.Int64@,System.IO.BinaryWriter)">
            <summary>
            Calls base class export first, then exports the data 
            specific to this event
            <seealso cref="M:NAudio.Midi.MidiEvent.Export(System.Int64@,System.IO.BinaryWriter)">MidiEvent.Export</seealso>
            </summary>
        </member>
        <member name="T:NAudio.Midi.TempoEvent">
            <summary>
            Represents a MIDI tempo event
            </summary>
        </member>
        <member name="M:NAudio.Midi.TempoEvent.#ctor(System.IO.BinaryReader,System.Int32)">
            <summary>
            Reads a new tempo event from a MIDI stream
            </summary>
            <param name="br">The MIDI stream</param>
            <param name="length">the data length</param>
        </member>
        <member name="M:NAudio.Midi.TempoEvent.#ctor(System.Int32,System.Int64)">
            <summary>
            Creates a new tempo event with specified settings
            </summary>
            <param name="microsecondsPerQuarterNote">Microseconds per quarter note</param>
            <param name="absoluteTime">Absolute time</param>
        </member>
        <member name="M:NAudio.Midi.TempoEvent.Clone">
            <summary>
            Creates a deep clone of this MIDI event.
            </summary>
        </member>
        <member name="M:NAudio.Midi.TempoEvent.ToString">
            <summary>
            Describes this tempo event
            </summary>
            <returns>String describing the tempo event</returns>
        </member>
        <member name="P:NAudio.Midi.TempoEvent.MicrosecondsPerQuarterNote">
            <summary>
            Microseconds per quarter note
            </summary>
        </member>
        <member name="P:NAudio.Midi.TempoEvent.Tempo">
            <summary>
            Tempo
            </summary>
        </member>
        <member name="M:NAudio.Midi.TempoEvent.Export(System.Int64@,System.IO.BinaryWriter)">
            <summary>
            Calls base class export first, then exports the data 
            specific to this event
            <seealso cref="M:NAudio.Midi.MidiEvent.Export(System.Int64@,System.IO.BinaryWriter)">MidiEvent.Export</seealso>
            </summary>
        </member>
        <member name="T:NAudio.Midi.TextEvent">
            <summary>
            Represents a MIDI text event
            </summary>
        </member>
        <member name="M:NAudio.Midi.TextEvent.#ctor(System.IO.BinaryReader,System.Int32)">
            <summary>
            Reads a new text event from a MIDI stream
            </summary>
            <param name="br">The MIDI stream</param>
            <param name="length">The data length</param>
        </member>
        <member name="M:NAudio.Midi.TextEvent.#ctor(System.String,NAudio.Midi.MetaEventType,System.Int64)">
            <summary>
            Creates a new TextEvent
            </summary>
            <param name="text">The text in this type</param>
            <param name="metaEventType">MetaEvent type (must be one that is
            associated with text data)</param>
            <param name="absoluteTime">Absolute time of this event</param>
        </member>
        <member name="M:NAudio.Midi.TextEvent.Clone">
            <summary>
            Creates a deep clone of this MIDI event.
            </summary>
        </member>
        <member name="P:NAudio.Midi.TextEvent.Text">
            <summary>
            The contents of this text event
            </summary>
        </member>
        <member name="P:NAudio.Midi.TextEvent.Data">
            <summary>
            The raw contents of this text event
            </summary>
        </member>
        <member name="M:NAudio.Midi.TextEvent.ToString">
            <summary>
            Describes this MIDI text event
            </summary>
            <returns>A string describing this event</returns>
        </member>
        <member name="M:NAudio.Midi.TextEvent.Export(System.Int64@,System.IO.BinaryWriter)">
            <summary>
            Calls base class export first, then exports the data 
            specific to this event
            <seealso cref="M:NAudio.Midi.MidiEvent.Export(System.Int64@,System.IO.BinaryWriter)">MidiEvent.Export</seealso>
            </summary>
        </member>
        <member name="T:NAudio.Midi.TimeSignatureEvent">
            <summary>
            Represents a MIDI time signature event
            </summary>
        </member>
        <member name="M:NAudio.Midi.TimeSignatureEvent.#ctor(System.IO.BinaryReader,System.Int32)">
            <summary>
            Reads a new time signature event from a MIDI stream
            </summary>
            <param name="br">The MIDI stream</param>
            <param name="length">The data length</param>
        </member>
        <member name="M:NAudio.Midi.TimeSignatureEvent.#ctor(System.Int64,System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            Creates a new TimeSignatureEvent
            </summary>
            <param name="absoluteTime">Time at which to create this event</param>
            <param name="numerator">Numerator</param>
            <param name="denominator">Denominator</param>
            <param name="ticksInMetronomeClick">Ticks in Metronome Click</param>
            <param name="no32ndNotesInQuarterNote">No of 32nd Notes in Quarter Click</param>
        </member>
        <member name="M:NAudio.Midi.TimeSignatureEvent.Clone">
            <summary>
            Creates a deep clone of this MIDI event.
            </summary>
        </member>
        <member name="P:NAudio.Midi.TimeSignatureEvent.Numerator">
            <summary>
            Numerator (number of beats in a bar)
            </summary>
        </member>
        <member name="P:NAudio.Midi.TimeSignatureEvent.Denominator">
            <summary>
            Denominator (Beat unit),
            1 means 2, 2 means 4 (crochet), 3 means 8 (quaver), 4 means 16 and 5 means 32
            </summary>
        </member>
        <member name="P:NAudio.Midi.TimeSignatureEvent.TicksInMetronomeClick">
            <summary>
            Ticks in a metronome click
            </summary>
        </member>
        <member name="P:NAudio.Midi.TimeSignatureEvent.No32ndNotesInQuarterNote">
            <summary>
            Number of 32nd notes in a quarter note
            </summary>
        </member>
        <member name="P:NAudio.Midi.TimeSignatureEvent.TimeSignature">
            <summary>
            The time signature
            </summary>
        </member>
        <member name="M:NAudio.Midi.TimeSignatureEvent.ToString">
            <summary>
            Describes this time signature event
            </summary>
            <returns>A string describing this event</returns>
        </member>
        <member name="M:NAudio.Midi.TimeSignatureEvent.Export(System.Int64@,System.IO.BinaryWriter)">
            <summary>
            Calls base class export first, then exports the data 
            specific to this event
            <seealso cref="M:NAudio.Midi.MidiEvent.Export(System.Int64@,System.IO.BinaryWriter)">MidiEvent.Export</seealso>
            </summary>
        </member>
        <member name="T:NAudio.Midi.TrackSequenceNumberEvent">
            <summary>
            Represents a MIDI track sequence number event event
            </summary>
        </member>
        <member name="M:NAudio.Midi.TrackSequenceNumberEvent.#ctor(System.UInt16)">
            <summary>
            Creates a new track sequence number event
            </summary>
        </member>
        <member name="M:NAudio.Midi.TrackSequenceNumberEvent.#ctor(System.IO.BinaryReader,System.Int32)">
            <summary>
            Reads a new track sequence number event from a MIDI stream
            </summary>
            <param name="br">The MIDI stream</param>
            <param name="length">the data length</param>
        </member>
        <member name="M:NAudio.Midi.TrackSequenceNumberEvent.Clone">
            <summary>
            Creates a deep clone of this MIDI event.
            </summary>
        </member>
        <member name="M:NAudio.Midi.TrackSequenceNumberEvent.ToString">
            <summary>
            Describes this event
            </summary>
            <returns>String describing the event</returns>
        </member>
        <member name="M:NAudio.Midi.TrackSequenceNumberEvent.Export(System.Int64@,System.IO.BinaryWriter)">
            <summary>
            Calls base class export first, then exports the data 
            specific to this event
            <seealso cref="M:NAudio.Midi.MidiEvent.Export(System.Int64@,System.IO.BinaryWriter)">MidiEvent.Export</seealso>
            </summary>
        </member>
    </members>
</doc>
