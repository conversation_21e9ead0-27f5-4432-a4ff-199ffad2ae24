<?xml version="1.0"?>
<doc>
    <assembly>
        <name>ControlzEx</name>
    </assembly>
    <members>
        <member name="T:ControlzEx.Automation.Peers.TabControlExAutomationPeer">
            <summary>
                Automation-Peer for <see cref="T:ControlzEx.Controls.TabControlEx" />.
            </summary>
        </member>
        <member name="M:ControlzEx.Automation.Peers.TabControlExAutomationPeer.#ctor(System.Windows.Controls.TabControl)">
            <summary>
                Initializes a new instance.
            </summary>
        </member>
        <member name="M:ControlzEx.Automation.Peers.TabControlExAutomationPeer.CreateItemAutomationPeer(System.Object)">
            <inheritdoc />
        </member>
        <member name="T:ControlzEx.Automation.Peers.TabItemExAutomationPeer">
            <summary>
                Automation-Peer for <see cref="T:System.Windows.Controls.TabItem" /> in <see cref="T:ControlzEx.Controls.TabControlEx" />.
            </summary>
        </member>
        <member name="M:ControlzEx.Automation.Peers.TabItemExAutomationPeer.#ctor(System.Object,System.Windows.Automation.Peers.TabControlAutomationPeer)">
            <summary>
                Initializes a new instance.
            </summary>
        </member>
        <member name="M:ControlzEx.Automation.Peers.TabItemExAutomationPeer.GetChildrenCore">
            <inheritdoc />
        </member>
        <member name="M:ControlzEx.Automation.Peers.TabItemExAutomationPeer.GetWrapper">
            <summary>
                Gets the real tab item.
            </summary>
        </member>
        <member name="F:ControlzEx.BadgedEx.BadgeProperty">
            <summary>Identifies the <see cref="P:ControlzEx.BadgedEx.Badge"/> dependency property.</summary>
        </member>
        <member name="P:ControlzEx.BadgedEx.Badge">
            <summary>
            Gets or sets the Badge content to display.
            </summary>
        </member>
        <member name="F:ControlzEx.BadgedEx.BadgeFontFamilyProperty">
            <summary>Identifies the <see cref="P:ControlzEx.BadgedEx.BadgeFontFamily"/> dependency property.</summary>
        </member>
        <member name="P:ControlzEx.BadgedEx.BadgeFontFamily">
            <summary>
            The BadgeFontFamily property specifies the name of font family.
            </summary>
        </member>
        <member name="F:ControlzEx.BadgedEx.BadgeFontStyleProperty">
            <summary>Identifies the <see cref="P:ControlzEx.BadgedEx.BadgeFontStyle"/> dependency property.</summary>
        </member>
        <member name="P:ControlzEx.BadgedEx.BadgeFontStyle">
            <summary>
            The BadgeFontStyle property requests normal, italic, and oblique faces within a font family.
            </summary>
        </member>
        <member name="F:ControlzEx.BadgedEx.BadgeFontWeightProperty">
            <summary>Identifies the <see cref="P:ControlzEx.BadgedEx.BadgeFontWeight"/> dependency property.</summary>
        </member>
        <member name="P:ControlzEx.BadgedEx.BadgeFontWeight">
            <summary>
            The BadgeFontWeight property specifies the weight of the font.
            </summary>
        </member>
        <member name="F:ControlzEx.BadgedEx.BadgeFontStretchProperty">
            <summary>Identifies the <see cref="P:ControlzEx.BadgedEx.BadgeFontStretch"/> dependency property.</summary>
        </member>
        <member name="P:ControlzEx.BadgedEx.BadgeFontStretch">
            <summary>
            The BadgeFontStretch property selects a normal, condensed, or extended face from a font family.
            </summary>
        </member>
        <member name="F:ControlzEx.BadgedEx.BadgeFontSizeProperty">
            <summary>Identifies the <see cref="P:ControlzEx.BadgedEx.BadgeFontSize"/> dependency property.</summary>
        </member>
        <member name="P:ControlzEx.BadgedEx.BadgeFontSize">
            <summary>
            The BadgeFontSize property specifies the size of the font.
            </summary>
        </member>
        <member name="F:ControlzEx.BadgedEx.BadgeBackgroundProperty">
            <summary>Identifies the <see cref="P:ControlzEx.BadgedEx.BadgeBackground"/> dependency property.</summary>
        </member>
        <member name="P:ControlzEx.BadgedEx.BadgeBackground">
            <summary>
            Gets or sets the background brush for the Badge.
            </summary>
        </member>
        <member name="F:ControlzEx.BadgedEx.BadgeForegroundProperty">
            <summary>Identifies the <see cref="P:ControlzEx.BadgedEx.BadgeForeground"/> dependency property.</summary>
        </member>
        <member name="P:ControlzEx.BadgedEx.BadgeForeground">
            <summary>
            Gets or sets the foreground brush for the Badge.
            </summary>
        </member>
        <member name="F:ControlzEx.BadgedEx.BadgeBorderBrushProperty">
            <summary>Identifies the <see cref="P:ControlzEx.BadgedEx.BadgeBorderBrush"/> dependency property.</summary>
        </member>
        <member name="P:ControlzEx.BadgedEx.BadgeBorderBrush">
            <summary>
            Gets or sets the border brush for the Badge.
            </summary>
        </member>
        <member name="F:ControlzEx.BadgedEx.BadgeBorderThicknessProperty">
            <summary>Identifies the <see cref="P:ControlzEx.BadgedEx.BadgeBorderThickness"/> dependency property.</summary>
        </member>
        <member name="P:ControlzEx.BadgedEx.BadgeBorderThickness">
            <summary>
            Gets or sets the border thickness for the Badge.
            </summary>
        </member>
        <member name="F:ControlzEx.BadgedEx.BadgePlacementModeProperty">
            <summary>Identifies the <see cref="P:ControlzEx.BadgedEx.BadgePlacementMode"/> dependency property.</summary>
        </member>
        <member name="P:ControlzEx.BadgedEx.BadgePlacementMode">
            <summary>
            Gets or sets the placement of the Badge relative to its content.
            </summary>
        </member>
        <member name="F:ControlzEx.BadgedEx.BadgeMarginProperty">
            <summary>Identifies the <see cref="P:ControlzEx.BadgedEx.BadgeMargin"/> dependency property.</summary>
        </member>
        <member name="P:ControlzEx.BadgedEx.BadgeMargin">
            <summary>
            Gets or sets a margin which can be used to make minor adjustments to the placement of the Badge.
            </summary>
        </member>
        <member name="F:ControlzEx.BadgedEx.BadgeTemplateProperty">
            <summary>Identifies the <see cref="P:ControlzEx.BadgedEx.BadgeTemplate"/> dependency property.</summary>
        </member>
        <member name="P:ControlzEx.BadgedEx.BadgeTemplate">
            <summary>
            Gets or sets the <see cref="T:System.Windows.DataTemplate"/> for the Badge
            </summary>
        </member>
        <member name="F:ControlzEx.BadgedEx.BadgeTemplateSelectorProperty">
            <summary>Identifies the <see cref="P:ControlzEx.BadgedEx.BadgeTemplateSelector"/> dependency property.</summary>
        </member>
        <member name="P:ControlzEx.BadgedEx.BadgeTemplateSelector">
            <summary>
            Gets or sets the <see cref="T:System.Windows.Controls.DataTemplateSelector"/> for the Badge
            </summary>
        </member>
        <member name="F:ControlzEx.BadgedEx.BadgeStringFormatProperty">
            <summary>Identifies the <see cref="P:ControlzEx.BadgedEx.BadgeStringFormat"/> dependency property.</summary>
        </member>
        <member name="P:ControlzEx.BadgedEx.BadgeStringFormat">
            <summary>
            Gets or sets a composite string that specifies how to format the Badge property if it is displayed as a string.
            </summary>
            <remarks> 
            This property is ignored if <seealso cref="P:ControlzEx.BadgedEx.BadgeTemplate"/> is set.
            </remarks>
        </member>
        <member name="F:ControlzEx.BadgedEx.IsBadgeSetProperty">
            <summary>Identifies the <see cref="P:ControlzEx.BadgedEx.IsBadgeSet"/> dependency property.</summary>
        </member>
        <member name="P:ControlzEx.BadgedEx.IsBadgeSet">
            <summary>
            Indicates whether the Badge has content to display.
            </summary>
        </member>
        <member name="F:ControlzEx.Behaviors.GlowWindowBehavior.GlowColorProperty">
            <summary>
            <see cref="T:System.Windows.DependencyProperty"/> for <see cref="P:ControlzEx.Behaviors.GlowWindowBehavior.GlowColor"/>.
            </summary>
        </member>
        <member name="P:ControlzEx.Behaviors.GlowWindowBehavior.GlowColor">
            <summary>
            Gets or sets a brush which is used as the glow when the window is active.
            </summary>
        </member>
        <member name="F:ControlzEx.Behaviors.GlowWindowBehavior.NonActiveGlowColorProperty">
            <summary>
            <see cref="T:System.Windows.DependencyProperty"/> for <see cref="P:ControlzEx.Behaviors.GlowWindowBehavior.NonActiveGlowColor"/>.
            </summary>
        </member>
        <member name="P:ControlzEx.Behaviors.GlowWindowBehavior.NonActiveGlowColor">
            <summary>
            Gets or sets a brush which is used as the glow when the window is not active.
            </summary>
        </member>
        <member name="F:ControlzEx.Behaviors.GlowWindowBehavior.IsGlowTransitionEnabledProperty">
            <summary>
            <see cref="T:System.Windows.DependencyProperty"/> for <see cref="P:ControlzEx.Behaviors.GlowWindowBehavior.IsGlowTransitionEnabled"/>.
            </summary>
        </member>
        <member name="P:ControlzEx.Behaviors.GlowWindowBehavior.IsGlowTransitionEnabled">
            <summary>
            Defines whether glow transitions should be used or not.
            </summary>
        </member>
        <member name="F:ControlzEx.Behaviors.GlowWindowBehavior.GlowDepthProperty">
            <summary>
            <see cref="T:System.Windows.DependencyProperty"/> for <see cref="P:ControlzEx.Behaviors.GlowWindowBehavior.GlowDepth"/>.
            </summary>
        </member>
        <member name="P:ControlzEx.Behaviors.GlowWindowBehavior.GlowDepth">
            <summary>
            Gets or sets the glow depth.
            </summary>
        </member>
        <member name="F:ControlzEx.Behaviors.GlowWindowBehavior.UseRadialGradientForCornersProperty">
            <summary>Identifies the <see cref="P:ControlzEx.Behaviors.GlowWindowBehavior.UseRadialGradientForCorners"/> dependency property.</summary>
        </member>
        <member name="P:ControlzEx.Behaviors.GlowWindowBehavior.UseRadialGradientForCorners">
            <summary>
            Gets or sets whether to use a radial gradient for the corners or not.
            </summary>
        </member>
        <member name="F:ControlzEx.Behaviors.GlowWindowBehavior.PreferDWMBorderColorProperty">
            <summary>Identifies the <see cref="P:ControlzEx.Behaviors.GlowWindowBehavior.PreferDWMBorderColor"/> dependency property.</summary>
        </member>
        <member name="P:ControlzEx.Behaviors.GlowWindowBehavior.PreferDWMBorderColor">
            <summary>
            Gets or sets whether the DWM border should be preferred instead of showing glow windows.
            </summary>
        </member>
        <member name="F:ControlzEx.Behaviors.GlowWindowBehavior.DWMSupportsBorderColorPropertyKey">
            <summary>Identifies the <see cref="P:ControlzEx.Behaviors.GlowWindowBehavior.DWMSupportsBorderColor"/> dependency property key.</summary>
        </member>
        <member name="F:ControlzEx.Behaviors.GlowWindowBehavior.DWMSupportsBorderColorProperty">
            <summary>Identifies the <see cref="P:ControlzEx.Behaviors.GlowWindowBehavior.DWMSupportsBorderColor"/> dependency property.</summary>
        </member>
        <member name="P:ControlzEx.Behaviors.GlowWindowBehavior.DWMSupportsBorderColor">
            <summary>
            Gets whether DWM supports a border color or not.
            </summary>
        </member>
        <member name="M:ControlzEx.Behaviors.GlowWindowBehavior.OnDetaching">
            <inheritdoc />
        </member>
        <member name="T:ControlzEx.Behaviors.TextBoxInputMaskBehavior">
            <summary>
            Enables an InputMask for <see cref="T:System.Windows.Controls.TextBox"/> with 2 Properties: <see cref="P:ControlzEx.Behaviors.TextBoxInputMaskBehavior.InputMask"/>, <see cref="P:ControlzEx.Behaviors.TextBoxInputMaskBehavior.PromptChar"/>.
            </summary>
        </member>
        <member name="F:ControlzEx.Behaviors.TextBoxInputMaskBehavior.InputMaskProperty">
            <summary>Identifies the <see cref="P:ControlzEx.Behaviors.TextBoxInputMaskBehavior.InputMask"/> dependency property.</summary>
        </member>
        <member name="F:ControlzEx.Behaviors.TextBoxInputMaskBehavior.PromptCharProperty">
            <summary>Identifies the <see cref="P:ControlzEx.Behaviors.TextBoxInputMaskBehavior.PromptChar"/> dependency property.</summary>
        </member>
        <member name="F:ControlzEx.Behaviors.TextBoxInputMaskBehavior.ResetOnSpaceProperty">
            <summary>Identifies the <see cref="P:ControlzEx.Behaviors.TextBoxInputMaskBehavior.ResetOnSpace"/> dependency property.</summary>
        </member>
        <member name="F:ControlzEx.Behaviors.TextBoxInputMaskBehavior.IgnoreSpaceProperty">
            <summary>Identifies the <see cref="P:ControlzEx.Behaviors.TextBoxInputMaskBehavior.IgnoreSpace"/> dependency property.</summary>
        </member>
        <member name="M:ControlzEx.Behaviors.TextBoxInputMaskBehavior.Pasting(System.Object,System.Windows.DataObjectPastingEventArgs)">
            <summary>
            Pasting prüft ob korrekte Daten reingepastet werden
            </summary>
        </member>
        <member name="M:ControlzEx.Behaviors.TextBoxInputMaskBehavior.TreatSelectedText">
            <summary>
            Falls eine Textauswahl vorliegt wird diese entsprechend behandelt.
            </summary>
        </member>
        <member name="F:ControlzEx.Behaviors.NonClientControlClickStrategy.None">
            <summary>
            No click.
            </summary>
        </member>
        <member name="F:ControlzEx.Behaviors.NonClientControlClickStrategy.MouseEvent">
            <summary>
            Uses simulated mouse events to click.
            </summary>
        </member>
        <member name="F:ControlzEx.Behaviors.NonClientControlClickStrategy.AutomationPeer">
            <summary>
            Uses an <see cref="T:System.Windows.Automation.Provider.IInvokeProvider"/> from an <see cref="T:System.Windows.Automation.Peers.AutomationPeer"/> to click.
            </summary>
        </member>
        <member name="F:ControlzEx.Behaviors.WindowCornerPreference.Default">
            <summary>
            Use the windows default.
            </summary>
        </member>
        <member name="F:ControlzEx.Behaviors.WindowCornerPreference.DoNotRound">
            <summary>
            Do NOT round window corners.
            </summary>
        </member>
        <member name="F:ControlzEx.Behaviors.WindowCornerPreference.Round">
            <summary>
            Round window corners.
            </summary>
        </member>
        <member name="F:ControlzEx.Behaviors.WindowCornerPreference.RoundSmall">
            <summary>
            Round window corners with small radius.
            </summary>
        </member>
        <member name="T:ControlzEx.Behaviors.WindowChromeBehavior">
            <summary>
            With this class we can make custom window styles.
            </summary>
        </member>
        <member name="F:ControlzEx.Behaviors.WindowChromeBehavior.windowHandle">
            <summary>Underlying HWND for the _window.</summary>
            <SecurityNote>
              Critical : Critical member
            </SecurityNote>
        </member>
        <member name="F:ControlzEx.Behaviors.WindowChromeBehavior.hwndSource">
            <summary>Underlying HWND for the _window.</summary>
            <SecurityNote>
              Critical : Critical member provides access to HWND's window messages which are critical
            </SecurityNote>
        </member>
        <member name="P:ControlzEx.Behaviors.WindowChromeBehavior.ResizeBorderThickness">
            <summary>
            Mirror property for <see cref="P:ControlzEx.Behaviors.WindowChromeBehavior.ResizeBorderThickness"/>.
            </summary>
        </member>
        <member name="F:ControlzEx.Behaviors.WindowChromeBehavior.ResizeBorderThicknessProperty">
            <summary>Identifies the <see cref="P:ControlzEx.Behaviors.WindowChromeBehavior.ResizeBorderThickness"/> dependency property.</summary>
        </member>
        <member name="P:ControlzEx.Behaviors.WindowChromeBehavior.IgnoreTaskbarOnMaximize">
            <summary>
            Defines if the Taskbar should be ignored when maximizing a Window.
            This only works with WindowStyle = None.
            </summary>
        </member>
        <member name="F:ControlzEx.Behaviors.WindowChromeBehavior.IgnoreTaskbarOnMaximizeProperty">
            <summary>
            <see cref="T:System.Windows.DependencyProperty"/> for <see cref="P:ControlzEx.Behaviors.WindowChromeBehavior.IgnoreTaskbarOnMaximize"/>.
            </summary>
        </member>
        <member name="P:ControlzEx.Behaviors.WindowChromeBehavior.KeepBorderOnMaximize">
            <summary>
            Gets/sets if the border thickness value should be kept on maximize
            if the MaxHeight/MaxWidth of the window is less than the monitor resolution.
            </summary>
        </member>
        <member name="F:ControlzEx.Behaviors.WindowChromeBehavior.KeepBorderOnMaximizeProperty">
            <summary>
            <see cref="T:System.Windows.DependencyProperty"/> for <see cref="P:ControlzEx.Behaviors.WindowChromeBehavior.KeepBorderOnMaximize"/>.
            </summary>
        </member>
        <member name="F:ControlzEx.Behaviors.WindowChromeBehavior.IsNCActiveProperty">
            <summary>
            <see cref="T:System.Windows.DependencyProperty"/> for <see cref="P:ControlzEx.Behaviors.WindowChromeBehavior.IsNCActive"/>.
            </summary>
        </member>
        <member name="P:ControlzEx.Behaviors.WindowChromeBehavior.IsNCActive">
            <summary>
            Gets whether the non-client area is active or not.
            </summary>
        </member>
        <member name="P:ControlzEx.Behaviors.WindowChromeBehavior.EnableMinimize">
            <summary>
            Gets or sets whether if the minimize button is visible and the minimize system menu is enabled.
            </summary>
        </member>
        <member name="P:ControlzEx.Behaviors.WindowChromeBehavior.EnableMaxRestore">
            <summary>
            Gets or sets whether if the maximize/restore button is visible and the maximize/restore system menu is enabled.
            </summary>
        </member>
        <member name="M:ControlzEx.Behaviors.WindowChromeBehavior.OnAttached">
            <inheritdoc />
        </member>
        <member name="M:ControlzEx.Behaviors.WindowChromeBehavior.GetDefaultResizeBorderThickness">
            <summary>
            Gets the default resize border thickness from the system parameters.
            </summary>
        </member>
        <member name="M:ControlzEx.Behaviors.WindowChromeBehavior.OnCleanup">
            <summary>
            Occurs during the cleanup of this behavior.
            </summary>
        </member>
        <member name="M:ControlzEx.Behaviors.WindowChromeBehavior.OnDetaching">
            <inheritdoc />
        </member>
        <member name="M:ControlzEx.Behaviors.WindowChromeBehavior.HandleBorderThicknessDuringMaximize">
            <summary>
            This fix is needed because style triggers don't work if someone sets the value locally/directly on the window.
            </summary>
        </member>
        <member name="M:ControlzEx.Behaviors.WindowChromeBehavior.#ctor">
            <summary>Create a new instance.</summary>
            <SecurityNote>
              Critical : Store critical methods in critical callback table
              Safe     : Demands full trust permissions
            </SecurityNote>
        </member>
        <member name="M:ControlzEx.Behaviors.WindowChromeBehavior._OnChromePropertyChangedThatRequiresRepaint">
            <SecurityNote>
              Critical : Calls critical methods
              Safe     : Demands full trust permissions
            </SecurityNote>
        </member>
        <member name="M:ControlzEx.Behaviors.WindowChromeBehavior._ApplyNewCustomChrome">
            <SecurityNote>
              Critical : Calls critical methods
            </SecurityNote>
        </member>
        <member name="M:ControlzEx.Behaviors.WindowChromeBehavior.WindowProc(System.IntPtr,System.Int32,System.UIntPtr,System.IntPtr,System.Boolean@)">
            <SecurityNote>
              Critical : Accesses critical _hwnd
            </SecurityNote>
        </member>
        <member name="M:ControlzEx.Behaviors.WindowChromeBehavior._HandleNCUAHDRAWCAPTION(Windows.Win32.WM,System.UIntPtr,System.IntPtr,System.Boolean@)">
            <SecurityNote>
              Critical : Calls critical methods
            </SecurityNote>
        </member>
        <member name="M:ControlzEx.Behaviors.WindowChromeBehavior._HandleSYSCOMMAND(Windows.Win32.WM,System.UIntPtr,System.IntPtr,System.Boolean@)">
            <SecurityNote>
              Critical : Calls critical methods
            </SecurityNote>
        </member>
        <member name="M:ControlzEx.Behaviors.WindowChromeBehavior._HandleNCACTIVATE(Windows.Win32.WM,System.UIntPtr,System.IntPtr,System.Boolean@)">
            <SecurityNote>
              Critical : Calls critical methods
            </SecurityNote>
        </member>
        <member name="M:ControlzEx.Behaviors.WindowChromeBehavior.AdjustWorkingAreaForAutoHide(System.IntPtr,Windows.Win32.Foundation.RECT)">
            <summary>
            This method handles the window size if the taskbar is set to auto-hide.
            </summary>
            <SecurityNote>
              Critical : Calls critical methods
            </SecurityNote>
        </member>
        <member name="M:ControlzEx.Behaviors.WindowChromeBehavior._HandleNCPAINT(Windows.Win32.WM,System.UIntPtr,System.IntPtr,System.Boolean@)">
            <SecurityNote>
              Critical : Calls critical methods
            </SecurityNote>
        </member>
        <member name="M:ControlzEx.Behaviors.WindowChromeBehavior._HandleNCHITTEST(Windows.Win32.WM,System.UIntPtr,System.IntPtr,System.Boolean@)">
            <SecurityNote>
              Critical : Calls critical methods
            </SecurityNote>
        </member>
        <member name="M:ControlzEx.Behaviors.WindowChromeBehavior._HandleNCRBUTTONUP(Windows.Win32.WM,System.UIntPtr,System.IntPtr,System.Boolean@)">
            <SecurityNote>
              Critical : Calls critical method
            </SecurityNote>
        </member>
        <member name="M:ControlzEx.Behaviors.WindowChromeBehavior._HandleSIZE(Windows.Win32.WM,System.UIntPtr,System.IntPtr,System.Boolean@)">
            <SecurityNote>
              Critical : Calls critical method
            </SecurityNote>
        </member>
        <member name="M:ControlzEx.Behaviors.WindowChromeBehavior._HandleWINDOWPOSCHANGING(Windows.Win32.WM,System.UIntPtr,System.IntPtr,System.Boolean@)">
            <SecurityNote>
              Critical : Calls critical Marshal.PtrToStructure
            </SecurityNote>
        </member>
        <member name="M:ControlzEx.Behaviors.WindowChromeBehavior._HandleWINDOWPOSCHANGED(Windows.Win32.WM,System.UIntPtr,System.IntPtr,System.Boolean@)">
            <SecurityNote>
              Critical : Calls critical Marshal.PtrToStructure
            </SecurityNote>
        </member>
        <member name="M:ControlzEx.Behaviors.WindowChromeBehavior._HandleGETMINMAXINFO(Windows.Win32.WM,System.UIntPtr,System.IntPtr,System.Boolean@)">
            <SecurityNote>
              Critical : Calls critical methods
            </SecurityNote>
        </member>
        <member name="M:ControlzEx.Behaviors.WindowChromeBehavior._HandleMOVEForRealSize(Windows.Win32.WM,System.UIntPtr,System.IntPtr,System.Boolean@)">
            <SecurityNote>
              Critical : Calls critical methods
            </SecurityNote>
        </member>
        <member name="M:ControlzEx.Behaviors.WindowChromeBehavior._ModifyStyle(Windows.Win32.UI.WindowsAndMessaging.WINDOW_STYLE,Windows.Win32.UI.WindowsAndMessaging.WINDOW_STYLE)">
            <summary>Add and remove a native WindowStyle from the HWND.</summary>
            <param name="removeStyle">The styles to be removed.  These can be bitwise combined.</param>
            <param name="addStyle">The styles to be added.  These can be bitwise combined.</param>
            <returns>Whether the styles of the HWND were modified as a result of this call.</returns>
            <SecurityNote>
              Critical : Calls critical methods
            </SecurityNote>
        </member>
        <member name="M:ControlzEx.Behaviors.WindowChromeBehavior._GetHwndState">
            <summary>
            Get the WindowState as the native HWND knows it to be.  This isn't necessarily the same as what Window thinks.
            </summary>
            <SecurityNote>
              Critical : Calls critical methods
            </SecurityNote>
        </member>
        <member name="M:ControlzEx.Behaviors.WindowChromeBehavior._GetWindowRect">
            <summary>
            Get the bounding rectangle for the window in physical coordinates.
            </summary>
            <returns>The bounding rectangle for the window.</returns>
            <SecurityNote>
              Critical : Calls critical methods
            </SecurityNote>
        </member>
        <member name="M:ControlzEx.Behaviors.WindowChromeBehavior._UpdateSystemMenu(System.Nullable{System.Windows.WindowState})">
            <summary>
            Update the items in the system menu based on the current, or assumed, WindowState.
            </summary>
            <param name="assumeState">
            The state to assume that the Window is in.  This can be null to query the Window's state.
            </param>
            <remarks>
            We want to update the menu while we have some control over whether the caption will be repainted.
            </remarks>
            <SecurityNote>
              Critical : Calls critical methods
            </SecurityNote>
        </member>
        <member name="F:ControlzEx.Behaviors.WindowChromeBehavior.hitTestBorders">
            <summary>
            Matrix of the HT values to return when responding to NC window messages.
            </summary>
        </member>
        <member name="T:ControlzEx.Controls.TabControlEx">
             <summary>
             The standard WPF TabControl is quite bad in the fact that it only
             even contains the current TabItem in the VisualTree, so if you
             have complex views it takes a while to re-create the view each tab
             selection change.Which makes the standard TabControl very sticky to
             work with. This class along with its associated ControlTemplate
             allow all TabItems to remain in the VisualTree without it being Sticky.
             It does this by keeping all TabItem content in the VisualTree but
             hides all inactive TabItem content, and only keeps the active TabItem
             content shown.
             
             Acknowledgement
                 Eric Burke
                     http://eric.burke.name/dotnetmania/2009/04/26/22.09.28
                 Sacha Barber: https://sachabarbs.wordpress.com/about-me/
                     http://stackoverflow.com/a/10210889/920384
                 http://stackoverflow.com/a/7838955/920384
             </summary>
             <remarks>
             We use two attached properties to later recognize the content presenters we generated.
             We need the OwningItem because the TabItem associated with an item can later change.
            
             We need the OwningTabItem to reduce the amount of lookups we have to do.
             </remarks>
        </member>
        <member name="F:ControlzEx.Controls.TabControlEx.ChildContentVisibilityProperty">
            <summary>Identifies the <see cref="P:ControlzEx.Controls.TabControlEx.ChildContentVisibility"/> dependency property.</summary>
        </member>
        <member name="F:ControlzEx.Controls.TabControlEx.TabPanelVisibilityProperty">
            <summary>Identifies the <see cref="P:ControlzEx.Controls.TabControlEx.TabPanelVisibility"/> dependency property.</summary>
        </member>
        <member name="M:ControlzEx.Controls.TabControlEx.GetOwningTabItem(System.Windows.DependencyObject)">
            <summary>Helper for getting <see cref="F:ControlzEx.Controls.TabControlEx.OwningTabItemProperty"/> from <paramref name="element"/>.</summary>
            <param name="element"><see cref="T:System.Windows.DependencyObject"/> to read <see cref="F:ControlzEx.Controls.TabControlEx.OwningTabItemProperty"/> from.</param>
            <returns>OwningTabItem property value.</returns>
        </member>
        <member name="M:ControlzEx.Controls.TabControlEx.SetOwningTabItem(System.Windows.DependencyObject,System.Windows.Controls.TabItem)">
            <summary>Helper for setting <see cref="F:ControlzEx.Controls.TabControlEx.OwningTabItemProperty"/> on <paramref name="element"/>.</summary>
            <param name="element"><see cref="T:System.Windows.DependencyObject"/> to set <see cref="F:ControlzEx.Controls.TabControlEx.OwningTabItemProperty"/> on.</param>
            <param name="value">OwningTabItem property value.</param>
        </member>
        <member name="M:ControlzEx.Controls.TabControlEx.SetOwningItem(System.Windows.DependencyObject,System.Object)">
            <summary>Helper for setting <see cref="F:ControlzEx.Controls.TabControlEx.OwningItemProperty"/> on <paramref name="element"/>.</summary>
            <param name="element"><see cref="T:System.Windows.DependencyObject"/> to set <see cref="F:ControlzEx.Controls.TabControlEx.OwningItemProperty"/> on.</param>
            <param name="value">OwningItem property value.</param>
        </member>
        <member name="M:ControlzEx.Controls.TabControlEx.GetOwningItem(System.Windows.DependencyObject)">
            <summary>Helper for getting <see cref="F:ControlzEx.Controls.TabControlEx.OwningItemProperty"/> from <paramref name="element"/>.</summary>
            <param name="element"><see cref="T:System.Windows.DependencyObject"/> to read <see cref="F:ControlzEx.Controls.TabControlEx.OwningItemProperty"/> from.</param>
            <returns>OwningItem property value.</returns>
        </member>
        <member name="F:ControlzEx.Controls.TabControlEx.MoveFocusToContentWhenSelectionChangesProperty">
            <summary>Identifies the <see cref="P:ControlzEx.Controls.TabControlEx.MoveFocusToContentWhenSelectionChanges"/> dependency property.</summary>
        </member>
        <member name="P:ControlzEx.Controls.TabControlEx.MoveFocusToContentWhenSelectionChanges">
            <summary>
            Gets or sets whether keyboard focus should be moved to the content area when the selected item changes.
            </summary>
        </member>
        <member name="M:ControlzEx.Controls.TabControlEx.#ctor">
            <summary>
            Initializes a new instance.
            </summary>
        </member>
        <member name="P:ControlzEx.Controls.TabControlEx.TabPanelVisibility">
            <summary>
            Defines if the TabPanel (Tab-Header) are visible.
            </summary>
        </member>
        <member name="P:ControlzEx.Controls.TabControlEx.ChildContentVisibility">
            <summary>
            Gets or sets the child content visibility.
            </summary>
            <value>
            The child content visibility.
            </value>
        </member>
        <member name="M:ControlzEx.Controls.TabControlEx.OnApplyTemplate">
            <inheritdoc />
        </member>
        <member name="M:ControlzEx.Controls.TabControlEx.OnInitialized(System.EventArgs)">
            <inheritdoc />
        </member>
        <member name="M:ControlzEx.Controls.TabControlEx.OnItemsChanged(System.Collections.Specialized.NotifyCollectionChangedEventArgs)">
            <inheritdoc />
            <summary>
            When the items change we remove any generated panel children and add any new ones as necessary.
            </summary>
        </member>
        <member name="M:ControlzEx.Controls.TabControlEx.OnSelectionChanged(System.Windows.Controls.SelectionChangedEventArgs)">
            <inheritdoc />
        </member>
        <member name="M:ControlzEx.Controls.TabControlEx.OnKeyDown(System.Windows.Input.KeyEventArgs)">
            <inheritdoc />
        </member>
        <member name="M:ControlzEx.Controls.TabControlEx.OnCreateAutomationPeer">
            <inheritdoc />
        </member>
        <member name="M:ControlzEx.Controls.TabControlEx.GetSelectedTabItem">
            <summary>
            Copied from <see cref="T:System.Windows.Controls.TabControl"/>. wish it were protected in that class instead of private.
            </summary>
        </member>
        <member name="M:ControlzEx.Controls.TabControlEx.RefreshItemsHolder">
            <summary>
            Clears all current children by calling <see cref="M:ControlzEx.Controls.TabControlEx.ClearItemsHolder"/> and calls <see cref="M:ControlzEx.Controls.TabControlEx.UpdateSelectedContent"/> afterwards.
            </summary>
        </member>
        <member name="M:ControlzEx.Controls.TabControlEx.UpdateSelectedContent">
            <summary>
            Generate a ContentPresenter for the selected item and control the visibility of already created presenters.
            </summary>
        </member>
        <member name="M:ControlzEx.Controls.TabControlEx.CreateChildContentPresenterIfRequired(System.Object,System.Windows.Controls.TabItem)">
            <summary>
            Create the child ContentPresenter for the given item (could be data or a TabItem) if none exists.
            </summary>
        </member>
        <member name="M:ControlzEx.Controls.TabControlEx.FindChildContentPresenter(System.Object,System.Windows.Controls.TabItem)">
            <summary>
            Find the <see cref="T:System.Windows.Controls.ContentPresenter"/> for the given object. Data could be a TabItem or a piece of data.
            </summary>
        </member>
        <member name="P:ControlzEx.Helpers.OSVersionHelper.IsWindowsNT">
            <summary>
            Windows NT
            </summary>
        </member>
        <member name="P:ControlzEx.Helpers.OSVersionHelper.IsWindows10_OrGreater">
            <summary>
            Windows 10 or greater.
            </summary>
        </member>
        <member name="P:ControlzEx.Helpers.OSVersionHelper.IsWindows10_1903_OrGreater">
            <summary>
            Windows 10 19H1 Version 1903 Build 18362 or greater (May 2019 Update)-
            </summary>
        </member>
        <member name="P:ControlzEx.Helpers.OSVersionHelper.IsWindows11_OrGreater">
            <summary>
            Windows 11 or greater.
            </summary>
        </member>
        <member name="P:ControlzEx.Helpers.OSVersionHelper.IsWindows11_22H2_OrGreater">
            <summary>
            Windows 11 22H2 or greater.
            </summary>
        </member>
        <member name="T:ControlzEx.Internal.DoubleUtilities">
            <summary>
            DoubleUtil uses fixed eps to provide fuzzy comparison functionality for doubles.
            Note that FP noise is a big problem and using any of these compare 
            methods is not a complete solution, but rather the way to reduce 
            the probability of repeating unnecessary work.
            </summary>
        </member>
        <member name="F:ControlzEx.Internal.DoubleUtilities.Epsilon">
            <summary>
            Epsilon - more or less random, more or less small number.
            </summary>
        </member>
        <member name="M:ControlzEx.Internal.DoubleUtilities.AreClose(System.Double,System.Double)">
            <summary>
            AreClose returns whether or not two doubles are "close".  That is, whether or 
            not they are within epsilon of each other.
            There are plenty of ways for this to return false even for numbers which
            are theoretically identical, so no code calling this should fail to work if this 
            returns false. 
            </summary>
            <param name="value1">The first double to compare.</param>
            <param name="value2">The second double to compare.</param>
            <returns>The result of the AreClose comparision.</returns>
        </member>
        <member name="M:ControlzEx.Internal.DoubleUtilities.IsStrictlyLessThan(System.Double,System.Double)">
            <summary>
            LessThan returns whether or not the first double is less than the second double.
            That is, whether or not the first is strictly less than *and* not within epsilon of
            the other number.
            There are plenty of ways for this to return false even for numbers which
            are theoretically identical, so no code calling this should fail to work if this 
            returns false.
            </summary>
            <param name="value1">The first double to compare.</param>
            <param name="value2">The second double to compare.</param>
            <returns>The result of the LessThan comparision.</returns>
        </member>
        <member name="M:ControlzEx.Internal.DoubleUtilities.IsStrictlyGreaterThan(System.Double,System.Double)">
            <summary>
            GreaterThan returns whether or not the first double is greater than the second double.
            That is, whether or not the first is strictly greater than *and* not within epsilon of
            the other number.
            There are plenty of ways for this to return false even for numbers which
            are theoretically identical, so no code calling this should fail to work if this 
            returns false.
            </summary>
            <param name="value1">The first double to compare.</param>
            <param name="value2">The second double to compare.</param>
            <returns>The result of the GreaterThan comparision.</returns>
        </member>
        <member name="M:ControlzEx.Internal.DoubleUtilities.IsLessThanOrCloseTo(System.Double,System.Double)">
            <summary>
            LessThanOrClose returns whether or not the first double is less than or close to
            the second double.  That is, whether or not the first is strictly less than or within
            epsilon of the other number.
            There are plenty of ways for this to return false even for numbers which
            are theoretically identical, so no code calling this should fail to work if this 
            returns false.
            </summary>
            <param name="value1">The first double to compare.</param>
            <param name="value2">The second double to compare.</param>
            <returns>The result of the LessThanOrClose comparision.</returns>
        </member>
        <member name="M:ControlzEx.Internal.DoubleUtilities.IsGreaterThanOrCloseTo(System.Double,System.Double)">
            <summary>
            GreaterThanOrClose returns whether or not the first double is greater than or close to
            the second double.  That is, whether or not the first is strictly greater than or within
            epsilon of the other number.
            There are plenty of ways for this to return false even for numbers which
            are theoretically identical, so no code calling this should fail to work if this 
            returns false.
            </summary>
            <param name="value1">The first double to compare.</param>
            <param name="value2">The second double to compare.</param>
            <returns>The result of the GreaterThanOrClose comparision.</returns>
        </member>
        <member name="M:ControlzEx.Internal.DoubleUtilities.IsFinite(System.Double)">
            <summary>
            Test to see if a double is a finite number (is not NaN or Infinity).
            </summary>
            <param name='value'>The value to test.</param>
            <returns>Whether or not the value is a finite number.</returns>
        </member>
        <member name="M:ControlzEx.Internal.DoubleUtilities.IsValidSize(System.Double)">
            <summary>
            Test to see if a double a valid size value (is finite and > 0).
            </summary>
            <param name='value'>The value to test.</param>
            <returns>Whether or not the value is a valid size value.</returns>
        </member>
        <member name="M:ControlzEx.Internal.DpiHelper.LogicalPixelsToDevice(System.Windows.Point,System.Double,System.Double)">
            <summary>
                Convert a point in device independent pixels (1/96") to a point in the system coordinates.
            </summary>
            <param name="logicalPoint">A point in the logical coordinate system.</param>
            <returns>Returns the parameter converted to the system's coordinates.</returns>
        </member>
        <member name="M:ControlzEx.Internal.DpiHelper.DevicePixelsToLogical(System.Windows.Point,System.Double,System.Double)">
            <summary>
                Convert a point in system coordinates to a point in device independent pixels (1/96").
            </summary>
            <param name="devicePoint">A point in the physical coordinate system.</param>
            <returns>Returns the parameter converted to the device independent coordinate system.</returns>
        </member>
        <member name="T:ControlzEx.Internal.KnownBoxes.BooleanBoxes">
            <summary>
            Class containing boxed values for <see cref="T:System.Boolean"/>.
            </summary>
        </member>
        <member name="F:ControlzEx.Internal.KnownBoxes.BooleanBoxes.TrueBox">
            <summary>
            Gets a boxed value for <c>true</c>.
            </summary>
        </member>
        <member name="F:ControlzEx.Internal.KnownBoxes.BooleanBoxes.FalseBox">
            <summary>
            Gets a boxed value for <c>true</c>.
            </summary>
        </member>
        <member name="M:ControlzEx.Internal.KnownBoxes.BooleanBoxes.Box(System.Boolean)">
            <summary>
            Gets a boxed value for <paramref name="value"/>.
            </summary>
            <returns>A boxed <see cref="T:System.Boolean"/> value.</returns>
        </member>
        <member name="M:ControlzEx.Internal.KnownBoxes.BooleanBoxes.Box(System.Nullable{System.Boolean})">
            <summary>
            Gets a boxed value for <paramref name="value"/>.
            </summary>
            <returns>A boxed nullable <see cref="T:System.Boolean"/> value.</returns>
        </member>
        <member name="T:ControlzEx.Internal.KnownBoxes.DoubleBoxes">
            <summary>
            Class containing boxed values for <see cref="T:System.Double"/>.
            </summary>
        </member>
        <member name="F:ControlzEx.Internal.KnownBoxes.DoubleBoxes.Zero">
            <summary>
            Gets a boxed value for <c>0D</c>.
            </summary>
        </member>
        <member name="F:ControlzEx.Internal.KnownBoxes.DoubleBoxes.NaN">
            <summary>
            Gets a boxed value for <see cref="F:System.Double.NaN"/>.
            </summary>
        </member>
        <member name="F:ControlzEx.Internal.KnownBoxes.DoubleBoxes.MaxValue">
            <summary>
            Gets a boxed value for <see cref="F:System.Double.MaxValue"/>.
            </summary>
        </member>
        <member name="F:ControlzEx.Internal.KnownBoxes.DoubleBoxes.One">
            <summary>
            Gets a boxed value for <c>1D</c>.
            </summary>
        </member>
        <member name="T:ControlzEx.Internal.KnownBoxes.IntBoxes">
            <summary>
            Class containing boxed values for <see cref="T:System.Int32"/>.
            </summary>
        </member>
        <member name="F:ControlzEx.Internal.KnownBoxes.IntBoxes.Zero">
            <summary>
            Gets a boxed value for <c>0</c>.
            </summary>
        </member>
        <member name="F:ControlzEx.Internal.KnownBoxes.IntBoxes.One">
            <summary>
            Gets a boxed value for <c>1</c>.
            </summary>
        </member>
        <member name="F:ControlzEx.Internal.KnownBoxes.IntBoxes.MaxValue">
            <summary>
            Gets a boxed value for <see cref="F:System.Int32.MaxValue"/>.
            </summary>
        </member>
        <member name="T:ControlzEx.Internal.KnownBoxes.StringBoxes">
            <summary>
            Class containing boxed values for <see cref="T:System.String"/>.
            </summary>
        </member>
        <member name="F:ControlzEx.Internal.KnownBoxes.StringBoxes.Empty">
            <summary>
            Gets a boxed value for <see cref="F:System.String.Empty"/>.
            </summary>
        </member>
        <member name="T:ControlzEx.Internal.KnownBoxes.VisibilityBoxes">
            <summary>
            Class containing boxed values for <see cref="T:System.Windows.Visibility"/>.
            </summary>
        </member>
        <member name="F:ControlzEx.Internal.KnownBoxes.VisibilityBoxes.Visible">
            <summary>
            Gets a boxed value for <see cref="F:System.Windows.Visibility.Visible"/>.
            </summary>
        </member>
        <member name="F:ControlzEx.Internal.KnownBoxes.VisibilityBoxes.Hidden">
            <summary>
            Gets a boxed value for <see cref="F:System.Windows.Visibility.Hidden"/>.
            </summary>
        </member>
        <member name="F:ControlzEx.Internal.KnownBoxes.VisibilityBoxes.Collapsed">
            <summary>
            Gets a boxed value for <see cref="F:System.Windows.Visibility.Collapsed"/>.
            </summary>
        </member>
        <member name="M:ControlzEx.Internal.KnownBoxes.VisibilityBoxes.Box(System.Windows.Visibility)">
            <summary>
            Gets a boxed value for <paramref name="value"/>.
            </summary>
            <returns>A boxed <see cref="T:System.Windows.Visibility"/> value.</returns>
        </member>
        <member name="M:ControlzEx.Internal.MonitorHelper.TryGetMonitorInfoFromPoint(Windows.Win32.Graphics.Gdi.MONITORINFO@)">
            <summary>
            Gets the monitor information from the current cursor position.
            </summary>
            <returns>True when getting the monitor information was successful.</returns>
        </member>
        <member name="M:ControlzEx.Internal.ResourceDictionaryHelper.GetValueFromKey(System.Windows.ResourceDictionary,System.Object)">
            <summary>
            Gets the value associated with <paramref name="key"/> directly from <paramref name="resourceDictionary"/>.
            </summary>
        </member>
        <member name="M:ControlzEx.Internal.ResourceDictionaryHelper.ContainsKey(System.Windows.ResourceDictionary,System.Object)">
            <summary>
            Checks if <paramref name="resourceDictionary"/> directly contains <paramref name="key"/>.
            </summary>
        </member>
        <member name="M:ControlzEx.Internal.Utility.ColorFromArgbDword(System.UInt32)">
            <summary>Convert a native integer that represent a color with an alpha channel into a Color struct.</summary>
            <param name="color">The integer that represents the color.  Its bits are of the format 0xAARRGGBB.</param>
            <returns>A Color representation of the parameter.</returns>
        </member>
        <member name="T:ControlzEx.KeyboardNavigationEx">
            <summary>
            Helper class for a common focusing problem.
            The focus itself isn't the problem. If we use the common focusing methods the control get the focus
            but it doesn't get the focus visual style.
            The KeyboardNavigation class handles the visual style only if the control get the focus from a keyboard
            device or if the SystemParameters.KeyboardCues is true.
            </summary>
        </member>
        <member name="P:ControlzEx.KeyboardNavigationEx.Instance">
            <summary>
            Gets the KeyboardNavigationEx singleton instance.
            </summary>
        </member>
        <member name="M:ControlzEx.KeyboardNavigationEx.ShowFocusVisualInternal">
            <summary>
            Shows the focus visual of the current focused UI element.
            Works only together with AlwaysShowFocusVisual property.
            </summary>
        </member>
        <member name="M:ControlzEx.KeyboardNavigationEx.Focus(System.Windows.UIElement)">
            <summary>
            Focuses the specified element and shows the focus visual style.
            </summary>
            <param name="element">The element which will be focused.</param>
        </member>
        <member name="F:ControlzEx.KeyboardNavigationEx.AlwaysShowFocusVisualProperty">
            <summary>
            Attached DependencyProperty for setting AlwaysShowFocusVisual for a UI element.
            </summary>
        </member>
        <member name="M:ControlzEx.KeyboardNavigationEx.GetAlwaysShowFocusVisual(System.Windows.UIElement)">
            <summary>
            Gets a the value which indicates if the UI element always show the focus visual style.
            </summary>
        </member>
        <member name="M:ControlzEx.KeyboardNavigationEx.SetAlwaysShowFocusVisual(System.Windows.UIElement,System.Boolean)">
            <summary>
            Sets a the value which indicates if the UI element always show the focus visual style.
            </summary>
        </member>
        <member name="F:ControlzEx.Native.DWMSBT.DWMSBT_AUTO">
            <summary>
            Automatically selects backdrop effect.
            </summary>
        </member>
        <member name="F:ControlzEx.Native.DWMSBT.DWMSBT_DISABLE">
            <summary>
            Turns off the backdrop effect.
            </summary>
        </member>
        <member name="F:ControlzEx.Native.DWMSBT.DWMSBT_MAINWINDOW">
            <summary>
            Sets Mica effect with generated wallpaper tint.
            </summary>
        </member>
        <member name="F:ControlzEx.Native.DWMSBT.DWMSBT_TRANSIENTWINDOW">
            <summary>
            Sets acrlic effect.
            </summary>
        </member>
        <member name="F:ControlzEx.Native.DWMSBT.DWMSBT_TABBEDWINDOW">
            <summary>
            Sets blurred wallpaper effect, like Mica without tint.
            </summary>
        </member>
        <member name="T:ControlzEx.Native.HT">
            <summary>
            Non-client hit test values, HT*
            </summary>
        </member>
        <member name="T:ControlzEx.PackIconBase`1">
            <summary>
            Base class for creating an icon control for icon packs.
            </summary>
        </member>
        <member name="M:ControlzEx.PackIconBase`1.#ctor(System.Func{System.Collections.Generic.IDictionary{`0,System.String}})">
            <summary>Creates a new instance.</summary>
            <param name="dataIndexFactory">
            Inheritors should provide a factory for setting up the path data index (per icon kind).
            The factory will only be utilized once, across all closed instances (first instantiation wins).
            </param>
        </member>
        <member name="F:ControlzEx.PackIconBase`1.KindProperty">
            <summary>Identifies the <see cref="P:ControlzEx.PackIconBase`1.Kind"/> dependency property.</summary>
        </member>
        <member name="P:ControlzEx.PackIconBase`1.Kind">
            <summary>
            Gets or sets the icon to display.
            </summary>
        </member>
        <member name="F:ControlzEx.PackIconBase`1.DataProperty">
            <summary>Identifies the <see cref="P:ControlzEx.PackIconBase`1.Data"/> dependency property.</summary>
        </member>
        <member name="P:ControlzEx.PackIconBase`1.Data">
            <summary>
            Gets the icon path data for the current <see cref="P:ControlzEx.PackIconBase`1.Kind"/>.
            </summary>
        </member>
        <member name="T:ControlzEx.PopupEx">
            <summary>
            This custom popup can be used by validation error templates or something else.
            It provides some additional nice features:
                - repositioning if host-window size or location changed
                - repositioning if host-window gets maximized and vice versa
                - it's only topmost if the host-window is activated
            </summary>
        </member>
        <member name="F:ControlzEx.PopupEx.CloseOnMouseLeftButtonDownProperty">
            <summary>Identifies the <see cref="P:ControlzEx.PopupEx.CloseOnMouseLeftButtonDown"/> dependency property.</summary>
        </member>
        <member name="P:ControlzEx.PopupEx.CloseOnMouseLeftButtonDown">
            <summary>
            Gets or sets if the popup can be closed by left mouse button down.
            </summary>
        </member>
        <member name="F:ControlzEx.PopupEx.AllowTopMostProperty">
            <summary>Identifies the <see cref="P:ControlzEx.PopupEx.AllowTopMost"/> dependency property.</summary>
        </member>
        <member name="P:ControlzEx.PopupEx.AllowTopMost">
            <summary>
            Gets or sets whether if the Popup should be always on top.
            </summary>
        </member>
        <member name="M:ControlzEx.PopupEx.RefreshPosition">
            <summary>
            Causes the popup to update it's position according to it's current settings.
            </summary>
        </member>
        <member name="T:ControlzEx.PropertyChangeNotifier">
            <summary>
            AddValueChanged of dependency property descriptor results in memory leak as you already know.
            So, as described here, you can create custom class PropertyChangeNotifier to listen
            to any dependency property changes.
            
            This class takes advantage of the fact that bindings use weak references to manage associations
            so the class will not root the object who property changes it is watching. It also uses a WeakReference
            to maintain a reference to the object whose property it is watching without rooting that object.
            In this way, you can maintain a collection of these objects so that you can unhook the property
            change later without worrying about that collection rooting the object whose values you are watching.
            
            Complete implementation can be found here: http://agsmith.wordpress.com/2008/04/07/propertydescriptor-addvaluechanged-alternative/
            </summary>
        </member>
        <member name="F:ControlzEx.PropertyChangeNotifier.ValueProperty">
            <summary>Identifies the <see cref="P:ControlzEx.PropertyChangeNotifier.Value"/> dependency property.</summary>
        </member>
        <member name="P:ControlzEx.PropertyChangeNotifier.Value">
            <summary>
            Gets or sets the value of the watched property.
            </summary>
            <seealso cref="F:ControlzEx.PropertyChangeNotifier.ValueProperty"/>
        </member>
        <member name="M:ControlzEx.SystemCommands.ShowSystemMenu(System.Windows.Window,System.Windows.Input.MouseButtonEventArgs)">
            <summary>
            Shows the system menu at the current mouse position.
            </summary>
            <param name="window">The window for which the system menu should be shown.</param>
            <param name="e">The mouse event args.</param>
        </member>
        <member name="M:ControlzEx.SystemCommands.ShowSystemMenu(System.Windows.Media.Visual,System.Windows.Point)">
            <summary>Display the system menu at a specified location.</summary>
            <param name="visual">The visual for which the system menu should be displayed.</param>
            <param name="elementPoint">The location to display the system menu, in logical screen coordinates.</param>
        </member>
        <member name="M:ControlzEx.SystemCommands.ShowSystemMenuPhysicalCoordinates(System.Windows.Media.Visual,System.Windows.Point)">
            <summary>Display the system menu at a specified location.</summary>
            <param name="visual">The visual for which the system menu should be displayed.</param>
            <param name="physicalScreenLocation">The location to display the system menu, in physical screen coordinates.</param>
            <remarks>
            The dpi of <paramref name="visual"/> is NOT used to calculate the final coordinates.
            So you have to pass the final coordinates.
            </remarks>
        </member>
        <member name="M:ControlzEx.SystemCommands.ShowSystemMenuPhysicalCoordinates(System.Windows.Interop.HwndSource,System.Windows.Point)">
            <summary>Display the system menu at a specified location.</summary>
            <param name="source">The source/hwnd for which the system menu should be displayed.</param>
            <param name="physicalScreenLocation">The location to display the system menu, in physical screen coordinates.</param>
            <remarks>
            The dpi of <paramref name="source"/> is NOT used to calculate the final coordinates.
            So you have to pass the final coordinates.
            </remarks>
        </member>
        <member name="T:ControlzEx.Theming.HSLColor">
            <summary>
            This struct represent a Color in HSL (Hue, Saturation, Luminance)
            
            Idea taken from here http://ciintelligence.blogspot.com/2012/02/converting-excel-theme-color-and-tint.html
            and here: https://en.wikipedia.org/wiki/HSL_and_HSV
            </summary>
        </member>
        <member name="M:ControlzEx.Theming.HSLColor.#ctor(System.Windows.Media.Color)">
            <summary>
            Creates a new HSL Color
            </summary>
            <param name="color">Any System.Windows.Media.Color</param>
        </member>
        <member name="M:ControlzEx.Theming.HSLColor.#ctor(System.Double,System.Double,System.Double,System.Double)">
            <summary>
            Creates a new HSL Color
            </summary>
            <param name="a">Alpha Channel [0;1]</param>
            <param name="h">Hue Channel [0;360]</param>
            <param name="s">Saturation Channel [0;1]</param>
            <param name="l">Luminance Channel [0;1]</param>
        </member>
        <member name="P:ControlzEx.Theming.HSLColor.A">
            <summary>
            Gets or sets the Alpha channel.
            </summary>
        </member>
        <member name="P:ControlzEx.Theming.HSLColor.H">
            <summary>
            Gets or sets the Hue channel.
            </summary>
        </member>
        <member name="P:ControlzEx.Theming.HSLColor.S">
            <summary>
            Gets or sets the Saturation channel.
            </summary>
        </member>
        <member name="P:ControlzEx.Theming.HSLColor.L">
            <summary>
            Gets or sets the Luminance channel.
            </summary>
        </member>
        <member name="M:ControlzEx.Theming.HSLColor.ToColor">
            <summary>
            Gets the ARGB-Color for this HSL-Color
            </summary>
            <returns>System.Windows.Media.Color</returns>
        </member>
        <member name="M:ControlzEx.Theming.HSLColor.GetTintedColor(System.Double)">
            <summary>
            Gets a lighter / darker color based on a tint value. If <paramref name="tint"/> is > 0 then the returned color is darker, otherwise it will be lighter.
            </summary>
            <param name="tint">Tint Value in the Range [-1;1].</param>
            <returns>a new <see cref="T:System.Windows.Media.Color"/> which is lighter or darker.</returns>
        </member>
        <member name="M:ControlzEx.Theming.HSLColor.GetTintedColor(System.Windows.Media.Color,System.Double)">
            <summary>
            Gets a lighter / darker color based on a tint value. If <paramref name="tint"/> is > 0 then the returned color is darker, otherwise it will be lighter.
            </summary>
            <param name="color">The input color which should be tinted.</param>
            <param name="tint">Tint Value in the Range [-1;1].</param>
            <returns>a new <see cref="T:System.Windows.Media.Color"/> which is lighter or darker.</returns>
        </member>
        <member name="T:ControlzEx.Theming.LibraryTheme">
            <summary>
            Represents a theme.
            </summary>
        </member>
        <member name="F:ControlzEx.Theming.LibraryTheme.LibraryThemeInstanceKey">
            <summary>
            Gets the key for the library theme instance.
            </summary>
        </member>
        <member name="F:ControlzEx.Theming.LibraryTheme.LibraryThemeAlternativeColorSchemeKey">
            <summary>
            Gets the key for the theme color scheme.
            </summary>
        </member>
        <member name="F:ControlzEx.Theming.LibraryTheme.RuntimeThemeColorValuesKey">
            <summary>
            Gets the key for the color values being used to generate a runtime theme.
            </summary>
        </member>
        <member name="M:ControlzEx.Theming.LibraryTheme.#ctor(System.Uri,ControlzEx.Theming.LibraryThemeProvider)">
            <summary>
            Initializes a new instance.
            </summary>
            <param name="resourceAddress">The URI of the theme ResourceDictionary.</param>
            <param name="libraryThemeProvider">The <see cref="T:ControlzEx.Theming.LibraryThemeProvider"/> which created this instance.</param>
        </member>
        <member name="M:ControlzEx.Theming.LibraryTheme.#ctor(System.Windows.ResourceDictionary,ControlzEx.Theming.LibraryThemeProvider)">
            <summary>
            Initializes a new instance.
            </summary>
            <param name="resourceDictionary">The ResourceDictionary of the theme.</param>
            <param name="libraryThemeProvider">The <see cref="T:ControlzEx.Theming.LibraryThemeProvider"/> which created this instance.</param>
        </member>
        <member name="P:ControlzEx.Theming.LibraryTheme.IsRuntimeGenerated">
            <inheritdoc cref="P:ControlzEx.Theming.Theme.IsRuntimeGenerated"/>
        </member>
        <member name="P:ControlzEx.Theming.LibraryTheme.IsHighContrast">
            <inheritdoc cref="P:ControlzEx.Theming.Theme.IsHighContrast"/>
        </member>
        <member name="P:ControlzEx.Theming.LibraryTheme.Name">
            <inheritdoc cref="P:ControlzEx.Theming.Theme.Name"/>
        </member>
        <member name="P:ControlzEx.Theming.LibraryTheme.Origin">
            <summary>
            Get the origin of the theme.
            </summary>
        </member>
        <member name="P:ControlzEx.Theming.LibraryTheme.DisplayName">
            <inheritdoc cref="P:ControlzEx.Theming.Theme.DisplayName"/>
        </member>
        <member name="P:ControlzEx.Theming.LibraryTheme.BaseColorScheme">
            <inheritdoc cref="P:ControlzEx.Theming.Theme.BaseColorScheme"/>
        </member>
        <member name="P:ControlzEx.Theming.LibraryTheme.ColorScheme">
            <inheritdoc cref="P:ControlzEx.Theming.Theme.ColorScheme"/>
        </member>
        <member name="P:ControlzEx.Theming.LibraryTheme.PrimaryAccentColor">
            <inheritdoc cref="P:ControlzEx.Theming.Theme.PrimaryAccentColor"/>
        </member>
        <member name="P:ControlzEx.Theming.LibraryTheme.ShowcaseBrush">
            <inheritdoc cref="P:ControlzEx.Theming.Theme.ShowcaseBrush"/>
        </member>
        <member name="P:ControlzEx.Theming.LibraryTheme.Resources">
            <summary>
            The root <see cref="T:System.Windows.ResourceDictionary"/> containing all resource dictionaries belonging to this instance as <see cref="P:System.Windows.ResourceDictionary.MergedDictionaries"/>
            </summary>
        </member>
        <member name="P:ControlzEx.Theming.LibraryTheme.AlternativeColorScheme">
            <summary>
            Gets the alternative color scheme for this theme.
            </summary>
        </member>
        <member name="F:ControlzEx.Theming.PopupBackdropType.None">
            <summary>
            None
            </summary>
        </member>
        <member name="F:ControlzEx.Theming.PopupBackdropType.Gradient">
            <summary>
            todo
            </summary>
        </member>
        <member name="F:ControlzEx.Theming.PopupBackdropType.TransparentGradient">
            <summary>
            todo
            </summary>
        </member>
        <member name="F:ControlzEx.Theming.PopupBackdropType.Blurbehind">
            <summary>
            todo
            </summary>
        </member>
        <member name="F:ControlzEx.Theming.PopupBackdropType.AcrylicBlurbehind">
            <summary>
            todo
            </summary>
        </member>
        <member name="M:ControlzEx.Theming.RuntimeThemeGenerator.GetIdealTextColor(System.Windows.Media.Color)">
            <summary>
                Determining Ideal Text Color Based on Specified Background Color
                http://www.codeproject.com/KB/GDI-plus/IdealTextColor.aspx
            </summary>
            <param name="color">The background color.</param>
            <returns></returns>
        </member>
        <member name="T:ControlzEx.Theming.RuntimeThemeGeneratorOptions">
            <summary>
            Global options for <see cref="T:ControlzEx.Theming.RuntimeThemeGenerator"/>.
            </summary>
        </member>
        <member name="M:ControlzEx.Theming.RuntimeThemeGeneratorOptions.CreateRuntimeThemeOptions(System.Boolean,ControlzEx.Theming.ThemeGenerator.ThemeGeneratorParameters,ControlzEx.Theming.ThemeGenerator.ThemeGeneratorBaseColorScheme)">
            <summary>
            Used to create the options being used to generate a single <see cref="T:ControlzEx.Theming.LibraryTheme"/>.
            </summary>
        </member>
        <member name="T:ControlzEx.Theming.RuntimeThemeOptions">
            <summary>
            Options being used to generate one single <see cref="T:ControlzEx.Theming.LibraryTheme"/>.
            </summary>
        </member>
        <member name="T:ControlzEx.Theming.Theme">
            <summary>
            Represents a theme.
            </summary>
        </member>
        <member name="F:ControlzEx.Theming.Theme.ThemeNameKey">
            <summary>
            Gets the key for the themes name.
            </summary>
        </member>
        <member name="F:ControlzEx.Theming.Theme.ThemeOriginKey">
            <summary>
            Gets the key for the themes origin.
            </summary>
        </member>
        <member name="F:ControlzEx.Theming.Theme.ThemeDisplayNameKey">
            <summary>
            Gets the key for the themes display name.
            </summary>
        </member>
        <member name="F:ControlzEx.Theming.Theme.ThemeBaseColorSchemeKey">
            <summary>
            Gets the key for the themes base color scheme.
            </summary>
        </member>
        <member name="F:ControlzEx.Theming.Theme.ThemeColorSchemeKey">
            <summary>
            Gets the key for the themes color scheme.
            </summary>
        </member>
        <member name="F:ControlzEx.Theming.Theme.ThemePrimaryAccentColorKey">
            <summary>
            Gets the key for the themes primary accent color.
            </summary>
        </member>
        <member name="F:ControlzEx.Theming.Theme.ThemeShowcaseBrushKey">
            <summary>
            Gets the key for the themes showcase brush.
            </summary>
        </member>
        <member name="F:ControlzEx.Theming.Theme.ThemeIsRuntimeGeneratedKey">
            <summary>
            Gets the key for the themes runtime generation flag.
            </summary>
        </member>
        <member name="F:ControlzEx.Theming.Theme.ThemeIsHighContrastKey">
            <summary>
            Gets the key for the themes high contrast flag.
            </summary>
        </member>
        <member name="F:ControlzEx.Theming.Theme.ThemeInstanceKey">
            <summary>
            Gets the key for the theme instance.
            </summary>
        </member>
        <member name="M:ControlzEx.Theming.Theme.#ctor(ControlzEx.Theming.LibraryTheme)">
            <summary>
            Initializes a new instance.
            </summary>
            <param name="libraryTheme">The first <see cref="T:ControlzEx.Theming.LibraryTheme"/> of the theme.</param>
        </member>
        <member name="P:ControlzEx.Theming.Theme.IsRuntimeGenerated">
            <summary>
            Gets whether this theme was generated at runtime.
            </summary>
        </member>
        <member name="P:ControlzEx.Theming.Theme.IsHighContrast">
            <summary>
            Gets whether this theme is for high contrast mode.
            </summary>
        </member>
        <member name="P:ControlzEx.Theming.Theme.Name">
            <summary>
            Gets the name of the theme.
            </summary>
        </member>
        <member name="P:ControlzEx.Theming.Theme.DisplayName">
            <summary>
            Gets the display name of the theme.
            </summary>
        </member>
        <member name="P:ControlzEx.Theming.Theme.BaseColorScheme">
            <summary>
            Get the base color scheme for this theme.
            </summary>
        </member>
        <member name="P:ControlzEx.Theming.Theme.PrimaryAccentColor">
            <summary>
            Gets the primary accent color for this theme.
            </summary>
        </member>
        <member name="P:ControlzEx.Theming.Theme.ColorScheme">
            <summary>
            Gets the color scheme for this theme.
            </summary>
        </member>
        <member name="P:ControlzEx.Theming.Theme.ShowcaseBrush">
            <summary>
            Gets a brush which can be used to showcase this theme.
            </summary>
        </member>
        <member name="P:ControlzEx.Theming.Theme.Resources">
            <summary>
            The root <see cref="T:System.Windows.ResourceDictionary"/> containing all resource dictionaries of all <see cref="T:ControlzEx.Theming.LibraryTheme"/> belonging to this instance as <see cref="P:System.Windows.ResourceDictionary.MergedDictionaries"/>
            </summary>
        </member>
        <member name="P:ControlzEx.Theming.Theme.LibraryThemes">
            <summary>
            The ResourceDictionaries that represent this theme.
            </summary>
        </member>
        <member name="P:ControlzEx.Theming.Theme.LibraryThemesInternal">
            <summary>
            The ResourceDictionaries that represent this theme.
            </summary>
        </member>
        <member name="M:ControlzEx.Theming.Theme.EnsureAllLibraryThemeProvidersProvided">
            <summary>
            Ensures that all <see cref="T:ControlzEx.Theming.LibraryThemeProvider"/> from <see cref="P:ControlzEx.Theming.ThemeManager.LibraryThemeProviders"/> provided a <see cref="T:ControlzEx.Theming.LibraryTheme"/> for this <see cref="T:ControlzEx.Theming.Theme"/>.
            </summary>
            <returns>This instance for fluent call usage.</returns>
        </member>
        <member name="M:ControlzEx.Theming.Theme.GetAllResources">
            <summary>
            Gets a flat list of all <see cref="T:System.Windows.ResourceDictionary"/> from all library themes.
            </summary>
            <returns>A flat list of all <see cref="T:System.Windows.ResourceDictionary"/> from all library themes.</returns>
        </member>
        <member name="M:ControlzEx.Theming.Theme.AddLibraryTheme(ControlzEx.Theming.LibraryTheme)">
            <summary>
            Adds a new <see cref="T:ControlzEx.Theming.LibraryTheme"/> to this <see cref="T:ControlzEx.Theming.Theme"/>.
            </summary>
            <param name="libraryTheme">The <see cref="T:ControlzEx.Theming.LibraryTheme"/> to add.</param>
            <returns>This instance for fluent call usage.</returns>
        </member>
        <member name="M:ControlzEx.Theming.Theme.ToString">
            <inheritdoc />
        </member>
        <member name="T:ControlzEx.Theming.ThemeChangedEventArgs">
            <summary>
            Class which is used as argument for an event to signal theme changes.
            </summary>
        </member>
        <member name="M:ControlzEx.Theming.ThemeChangedEventArgs.#ctor(System.Object,System.Windows.ResourceDictionary,ControlzEx.Theming.Theme,ControlzEx.Theming.Theme)">
            <summary>
            Creates a new instance of this class.
            </summary>
        </member>
        <member name="P:ControlzEx.Theming.ThemeChangedEventArgs.Target">
            <summary>
            The target object for which was targeted by the theme change.
            </summary>
        </member>
        <member name="P:ControlzEx.Theming.ThemeChangedEventArgs.TargetResourceDictionary">
            <summary>
            The <see cref="T:System.Windows.ResourceDictionary"/> for which was targeted by the theme change.
            </summary>
        </member>
        <member name="P:ControlzEx.Theming.ThemeChangedEventArgs.OldTheme">
            <summary>
            The old theme.
            </summary>
        </member>
        <member name="P:ControlzEx.Theming.ThemeChangedEventArgs.NewTheme">
            <summary>
            The new theme.
            </summary>
        </member>
        <member name="T:ControlzEx.Theming.ThemeManager">
            <summary>
            A class that allows for the detection and alteration of a theme.
            </summary>
        </member>
        <member name="F:ControlzEx.Theming.ThemeManager.BaseColorLight">
            <summary>
            Gets the name for the light base color.
            </summary>
        </member>
        <member name="F:ControlzEx.Theming.ThemeManager.BaseColorLightConst">
            <summary>
            Gets the name for the light base color.
            </summary>
        </member>
        <member name="F:ControlzEx.Theming.ThemeManager.BaseColorDark">
            <summary>
            Gets the name for the dark base color.
            </summary>
        </member>
        <member name="F:ControlzEx.Theming.ThemeManager.BaseColorDarkConst">
            <summary>
            Gets the name for the dark base color.
            </summary>
        </member>
        <member name="P:ControlzEx.Theming.ThemeManager.LibraryThemeProviders">
            <summary>
            Gets a list of all library theme providers.
            </summary>
        </member>
        <member name="P:ControlzEx.Theming.ThemeManager.Themes">
            <summary>
            Gets a list of all themes.
            </summary>
        </member>
        <member name="P:ControlzEx.Theming.ThemeManager.BaseColors">
            <summary>
            Gets a list of all available base colors.
            </summary>
        </member>
        <member name="P:ControlzEx.Theming.ThemeManager.ColorSchemes">
            <summary>
            Gets a list of all available color schemes.
            </summary>
        </member>
        <member name="M:ControlzEx.Theming.ThemeManager.ClearThemes">
            <summary>
            Clears the internal themes list.
            </summary>
        </member>
        <member name="M:ControlzEx.Theming.ThemeManager.GetTheme(System.String,System.Boolean)">
            <summary>
            Gets the <see cref="T:ControlzEx.Theming.Theme"/> with the given name.
            </summary>
            <returns>The <see cref="T:ControlzEx.Theming.Theme"/> or <c>null</c>, if the theme wasn't found</returns>
        </member>
        <member name="M:ControlzEx.Theming.ThemeManager.GetTheme(System.String,System.String,System.Boolean)">
            <summary>
            Gets the <see cref="T:ControlzEx.Theming.Theme"/> with the given name.
            </summary>
            <returns>The <see cref="T:ControlzEx.Theming.Theme"/> or <c>null</c>, if the theme wasn't found</returns>
        </member>
        <member name="M:ControlzEx.Theming.ThemeManager.GetTheme(System.Windows.ResourceDictionary)">
            <summary>
            Gets the <see cref="T:ControlzEx.Theming.Theme"/> with the given resource dictionary.
            </summary>
            <param name="resourceDictionary"><see cref="T:System.Windows.ResourceDictionary"/> from which the theme should be retrieved.</param>
            <returns>The <see cref="T:ControlzEx.Theming.Theme"/> or <c>null</c>, if the theme wasn't found.</returns>
        </member>
        <member name="M:ControlzEx.Theming.ThemeManager.GetInverseTheme(ControlzEx.Theming.Theme)">
            <summary>
            Gets the inverse <see cref="T:ControlzEx.Theming.Theme" /> of the given <see cref="T:ControlzEx.Theming.Theme"/>.
            This method relies on the "Dark" or "Light" affix to be present.
            </summary>
            <param name="theme">The app theme.</param>
            <returns>The inverse <see cref="T:ControlzEx.Theming.Theme"/> or <c>null</c> if it couldn't be found.</returns>
            <remarks>
            Returns BaseLight, if BaseDark is given or vice versa.
            Custom Themes must end with "Dark" or "Light" for this to work, for example "CustomDark" and "CustomLight".
            </remarks>
        </member>
        <member name="M:ControlzEx.Theming.ThemeManager.IsThemeDictionary(System.Windows.ResourceDictionary)">
            <summary>
            Determines whether the specified resource dictionary represents a <see cref="T:ControlzEx.Theming.Theme"/>.
            <para />
            This might include runtime themes which do not have a resource uri.
            </summary>
            <param name="resourceDictionary">The resources.</param>
            <returns><c>true</c> if the resource dictionary is an <see cref="T:ControlzEx.Theming.Theme"/>; otherwise, <c>false</c>.</returns>
            <exception cref="T:System.ArgumentNullException">resources</exception>
        </member>
        <member name="M:ControlzEx.Theming.ThemeManager.IsRuntimeGeneratedThemeDictionary(System.Windows.ResourceDictionary)">
            <summary>
            Determines whether the specified resource dictionary represents a <see cref="T:ControlzEx.Theming.Theme"/> and was generated at runtime.
            <para />
            This might include runtime themes which do not have a resource uri.
            </summary>
            <param name="resourceDictionary">The resources.</param>
            <returns><c>true</c> if the resource dictionary is an <see cref="T:ControlzEx.Theming.Theme"/>; otherwise, <c>false</c>.</returns>
            <exception cref="T:System.ArgumentNullException">resources</exception>
        </member>
        <member name="M:ControlzEx.Theming.ThemeManager.ChangeTheme(System.Windows.Application,System.String,System.Boolean)">
            <summary>
            Change the theme for the whole application.
            </summary>
        </member>
        <member name="M:ControlzEx.Theming.ThemeManager.ChangeTheme(System.Windows.FrameworkElement,System.String,System.Boolean)">
            <summary>
            Change theme for the given window.
            </summary>
        </member>
        <member name="M:ControlzEx.Theming.ThemeManager.ChangeTheme(System.Windows.Application,ControlzEx.Theming.Theme)">
            <summary>
            Change theme for the whole application.
            </summary>
            <param name="app">The instance of Application to change.</param>
            <param name="newTheme">The theme to apply.</param>
        </member>
        <member name="M:ControlzEx.Theming.ThemeManager.ChangeTheme(System.Windows.FrameworkElement,ControlzEx.Theming.Theme)">
            <summary>
            Change theme for the given ResourceDictionary.
            </summary>
            <param name="frameworkElement">The FrameworkElement to change.</param>
            <param name="newTheme">The theme to apply.</param>
        </member>
        <member name="M:ControlzEx.Theming.ThemeManager.ChangeTheme(System.Object,System.Windows.ResourceDictionary,ControlzEx.Theming.Theme)">
            <summary>
            Change theme for the given ResourceDictionary.
            </summary>
            <param name="target">The target object for which the theme change should be made. This is optional an can be <c>null</c>.</param>
            <param name="resourceDictionary">The ResourceDictionary to change.</param>
            <param name="newTheme">The theme to apply.</param>
        </member>
        <member name="M:ControlzEx.Theming.ThemeManager.ChangeTheme(System.Windows.Application,System.String,System.String)">
            <summary>
            Change base color and color scheme of for the given application.
            </summary>
            <param name="app">The application to modify.</param>
            <param name="baseColorScheme">The base color to apply to the ResourceDictionary.</param>
            <param name="colorScheme">The color scheme to apply to the ResourceDictionary.</param>
        </member>
        <member name="M:ControlzEx.Theming.ThemeManager.ChangeTheme(System.Windows.FrameworkElement,System.String,System.String)">
            <summary>
            Change base color and color scheme of for the given window.
            </summary>
            <param name="frameworkElement">The FrameworkElement to modify.</param>
            <param name="baseColorScheme">The base color to apply to the ResourceDictionary.</param>
            <param name="colorScheme">The color scheme to apply to the ResourceDictionary.</param>
        </member>
        <member name="M:ControlzEx.Theming.ThemeManager.ChangeTheme(System.Object,System.Windows.ResourceDictionary,ControlzEx.Theming.Theme,System.String,System.String)">
            <summary>
            Change base color and color scheme of for the given ResourceDictionary.
            </summary>
            <param name="target">The target object for which the theme change should be made. This is optional an can be <c>null</c>.</param>
            <param name="resourceDictionary">The ResourceDictionary to modify.</param>
            <param name="oldTheme">The old/current theme.</param>
            <param name="baseColorScheme">The base color to apply to the ResourceDictionary.</param>
            <param name="colorScheme">The color scheme to apply to the ResourceDictionary.</param>
        </member>
        <member name="M:ControlzEx.Theming.ThemeManager.ChangeThemeBaseColor(System.Windows.Application,System.String)">
            <summary>
            Change base color for the given application.
            </summary>
            <param name="app">The application to change.</param>
            <param name="baseColorScheme">The base color to apply to the ResourceDictionary.</param>
        </member>
        <member name="M:ControlzEx.Theming.ThemeManager.ChangeThemeBaseColor(System.Windows.FrameworkElement,System.String)">
            <summary>
            Change base color for the given window.
            </summary>
            <param name="frameworkElement">The FrameworkElement to change.</param>
            <param name="baseColorScheme">The base color to apply to the ResourceDictionary.</param>
        </member>
        <member name="M:ControlzEx.Theming.ThemeManager.ChangeThemeBaseColor(System.Object,System.Windows.ResourceDictionary,ControlzEx.Theming.Theme,System.String)">
            <summary>
            Change base color of for the given ResourceDictionary.
            </summary>
            <param name="target">The target object for which the theme change should be made. This is optional an can be <c>null</c>.</param>
            <param name="resourceDictionary">The ResourceDictionary to modify.</param>
            <param name="oldTheme">The old/current theme.</param>
            <param name="baseColorScheme">The base color to apply to the ResourceDictionary.</param>
        </member>
        <member name="M:ControlzEx.Theming.ThemeManager.ChangeThemeColorScheme(System.Windows.Application,System.String)">
            <summary>
            Change color scheme for the given application.
            </summary>
            <param name="app">The application to change.</param>
            <param name="colorScheme">The color scheme to apply to the ResourceDictionary.</param>
        </member>
        <member name="M:ControlzEx.Theming.ThemeManager.ChangeThemeColorScheme(System.Windows.FrameworkElement,System.String)">
            <summary>
            Change color scheme for the given window.
            </summary>
            <param name="frameworkElement">The FrameworkElement to change.</param>
            <param name="colorScheme">The color scheme to apply to the ResourceDictionary.</param>
        </member>
        <member name="M:ControlzEx.Theming.ThemeManager.ChangeThemeColorScheme(System.Object,System.Windows.ResourceDictionary,ControlzEx.Theming.Theme,System.String)">
            <summary>
            Change color scheme for the given ResourceDictionary.
            </summary>
            <param name="target">The target object for which the theme change should be made. This is optional an can be <c>null</c>.</param>
            <param name="resourceDictionary">The ResourceDictionary to modify.</param>
            <param name="oldTheme">The old/current theme.</param>
            <param name="colorScheme">The color scheme to apply to the ResourceDictionary.</param>
        </member>
        <member name="M:ControlzEx.Theming.ThemeManager.ApplyThemeResourcesFromTheme(System.Windows.ResourceDictionary,ControlzEx.Theming.Theme)">
            <summary>
            Changes the theme of a ResourceDictionary directly.
            </summary>
            <param name="resourceDictionary">The ResourceDictionary to modify.</param>
            <param name="newTheme">The theme to apply to the ResourceDictionary.</param>
        </member>
        <member name="M:ControlzEx.Theming.ThemeManager.DetectTheme">
            <summary>
            Scans the resources and returns it's theme.
            </summary>
            <remarks>If the theme can't be detected from the <see cref="P:System.Windows.Application.MainWindow"/> we try to detect it from <see cref="P:System.Windows.Application.Current"/>.</remarks>
        </member>
        <member name="M:ControlzEx.Theming.ThemeManager.DetectTheme(System.Windows.Application)">
            <summary>
            Scans the application resources and returns it's theme.
            </summary>
            <param name="app">The Application instance to scan.</param>
        </member>
        <member name="M:ControlzEx.Theming.ThemeManager.DetectTheme(System.Windows.FrameworkElement)">
            <summary>
            Scans the resources and returns it's theme.
            </summary>
            <param name="frameworkElement">The FrameworkElement to scan.</param>
            <remarks>If the theme can't be detected from the <paramref name="frameworkElement"/> we try to detect it from <see cref="P:System.Windows.Application.Current"/>.</remarks>
        </member>
        <member name="M:ControlzEx.Theming.ThemeManager.DetectTheme(System.Windows.ResourceDictionary)">
            <summary>
            Scans a resources and returns it's theme.
            </summary>
            <param name="resourceDictionary">The ResourceDictionary to scan.</param>
        </member>
        <member name="E:ControlzEx.Theming.ThemeManager.ThemeChanged">
            <summary>
            This event fires if the theme was changed
            this should be using the weak event pattern, but for now it's enough
            </summary>
        </member>
        <member name="M:ControlzEx.Theming.ThemeManager.OnThemeChanged(System.Object,System.Windows.ResourceDictionary,ControlzEx.Theming.Theme,ControlzEx.Theming.Theme)">
            <summary>
            Invalidates global colors and resources.
            Sometimes the ContextMenu is not changing the colors, so this will fix it.
            </summary>
        </member>
        <member name="F:ControlzEx.Theming.ThemeSyncMode.DoNotSync">
            <summary>
            No synchronization will happen.
            </summary>
        </member>
        <member name="F:ControlzEx.Theming.ThemeSyncMode.SyncWithAppMode">
            <summary>
            Gets or sets whether changes to the "app mode" setting from windows should be detected at runtime and the current <see cref="T:ControlzEx.Theming.Theme"/> be changed accordingly.
            </summary>
        </member>
        <member name="F:ControlzEx.Theming.ThemeSyncMode.SyncWithAccent">
            <summary>
            Gets or sets whether changes to the accent color settings from windows should be detected at runtime and the current <see cref="T:ControlzEx.Theming.Theme"/> be changed accordingly.
            </summary>
        </member>
        <member name="F:ControlzEx.Theming.ThemeSyncMode.SyncWithHighContrast">
            <summary>
            Gets or sets whether changes to the high contrast setting from windows should be detected at runtime and the current <see cref="T:ControlzEx.Theming.Theme"/> be changed accordingly.
            </summary>
        </member>
        <member name="F:ControlzEx.Theming.ThemeSyncMode.SyncAll">
            <summary>
            All synchronizations are active.
            </summary>
        </member>
        <member name="T:ControlzEx.Theming.WindowBackdropType">
            <summary>
            Mirrors values of DWM_SYSTEMBACKDROP_TYPE
            </summary>
            <remarks>
            https://learn.microsoft.com/en-us/windows/win32/api/dwmapi/ne-dwmapi-dwm_systembackdrop_type
            </remarks>
        </member>
        <member name="F:ControlzEx.Theming.WindowBackdropType.None">
            <summary>
            No backdrop effect.
            </summary>
        </member>
        <member name="F:ControlzEx.Theming.WindowBackdropType.Auto">
            <summary>
            Sets <c>DWMWA_SYSTEMBACKDROP_TYPE</c> to <see langword="0"></see>.
            </summary>
        </member>
        <member name="F:ControlzEx.Theming.WindowBackdropType.Mica">
            <summary>
            Windows 11 Mica effect.
            </summary>
        </member>
        <member name="F:ControlzEx.Theming.WindowBackdropType.Acrylic">
            <summary>
            Windows Acrylic effect.
            </summary>
        </member>
        <member name="F:ControlzEx.Theming.WindowBackdropType.Tabbed">
            <summary>
            Windows 11 wallpaper blur effect.
            </summary>
        </member>
        <member name="M:ControlzEx.Theming.XamlThemeHelper.FixXamlReaderXmlNsIssue(System.String)">
            <summary>
            Works around an issue in the XamlReader.
            Without this fix the XamlReader would not be able to read the XAML we produced earlier because it does not know where to look for the types.
            The real issue is that we can't use the full namespace, with assembly hint, at compile time of the original project because said assembly does not yet exist and would cause a compile time error.
            Hence we have to use this workaround to enable both.
            The issue 
            </summary>
            <returns>The fixed version of <paramref name="xamlContent"/>.</returns>
            <example>
            If you have the following in your XAML file:
            xmlns:markup="clr-namespace:MahApps.Metro.Markup"
            xmlns:markupWithAssembly="clr-namespace:MahApps.Metro.Markup;assembly=MahApps.Metro"
            It get's converted to:
            xmlns:markup="clr-namespace:MahApps.Metro.Markup;assembly=MahApps.Metro"
            xmlns:markupWithAssembly="clr-namespace:MahApps.Metro.Markup;assembly=MahApps.Metro"
            </example>
        </member>
        <member name="M:ControlzEx.ToolTipAssist.GetAutoMove(System.Windows.Controls.ToolTip)">
            <summary>
            Indicates whether a tooltip should follow the mouse cursor.
            </summary>
        </member>
        <member name="M:ControlzEx.ToolTipAssist.SetAutoMove(System.Windows.Controls.ToolTip,System.Boolean)">
            <summary>
            Sets whether a tooltip should follow the mouse cursor.
            </summary>
        </member>
        <member name="M:ControlzEx.ToolTipAssist.GetAutoMoveHorizontalOffset(System.Windows.Controls.ToolTip)">
            <summary>
            Gets the horizontal offset for the relative placement of the Tooltip.
            </summary>
        </member>
        <member name="M:ControlzEx.ToolTipAssist.SetAutoMoveHorizontalOffset(System.Windows.Controls.ToolTip,System.Double)">
            <summary>
            Sets the horizontal offset for the relative placement of the Tooltip.
            </summary>
        </member>
        <member name="M:ControlzEx.ToolTipAssist.GetAutoMoveVerticalOffset(System.Windows.Controls.ToolTip)">
            <summary>
            Gets the vertical offset for the relative placement of the Tooltip.
            </summary>
        </member>
        <member name="M:ControlzEx.ToolTipAssist.SetAutoMoveVerticalOffset(System.Windows.Controls.ToolTip,System.Double)">
            <summary>
            Sets the vertical offset for the relative placement of the Tooltip.
            </summary>
        </member>
        <member name="M:ControlzEx.WindowChromeWindow.OnSourceInitialized(System.EventArgs)">
            <inheritdoc />
        </member>
        <member name="M:ControlzEx.WindowChromeWindow.InitializeBehaviors">
            <summary>
            Used to initialize the required behaviors.
            </summary>
        </member>
        <member name="M:ControlzEx.WindowChromeWindow.InitializeWindowChromeBehavior">
            <summary>
            Initializes the WindowChromeBehavior which is needed to render the custom WindowChrome.
            </summary>
        </member>
        <member name="M:ControlzEx.WindowChromeWindow.InitializeGlowWindowBehavior">
            <summary>
            Initializes the WindowChromeBehavior which is needed to render the custom WindowChrome.
            </summary>
        </member>
        <member name="P:ControlzEx.WindowChromeWindow.ResizeBorderThickness">
            <inheritdoc cref="P:ControlzEx.Behaviors.WindowChromeBehavior.ResizeBorderThickness"/>
        </member>
        <member name="F:ControlzEx.WindowChromeWindow.ResizeBorderThicknessProperty">
            <summary>Identifies the <see cref="P:ControlzEx.WindowChromeWindow.ResizeBorderThickness"/> dependency property.</summary>
        </member>
        <member name="P:ControlzEx.WindowChromeWindow.GlowDepth">
            <inheritdoc cref="P:ControlzEx.Behaviors.GlowWindowBehavior.GlowDepth"/>
            <remarks>
            Only relevant if the DWM border is not used.
            </remarks>
        </member>
        <member name="F:ControlzEx.WindowChromeWindow.GlowDepthProperty">
            <summary>Identifies the <see cref="P:ControlzEx.WindowChromeWindow.GlowDepth"/> dependency property.</summary>
        </member>
        <member name="F:ControlzEx.WindowChromeWindow.UseRadialGradientForCornersProperty">
            <summary>Identifies the <see cref="P:ControlzEx.WindowChromeWindow.UseRadialGradientForCorners"/> dependency property.</summary>
        </member>
        <member name="P:ControlzEx.WindowChromeWindow.UseRadialGradientForCorners">
            <inheritdoc cref="P:ControlzEx.Behaviors.GlowWindowBehavior.UseRadialGradientForCorners"/>
            <remarks>
            Only relevant if the DWM border is not used.
            </remarks>
        </member>
        <member name="F:ControlzEx.WindowChromeWindow.IsGlowTransitionEnabledProperty">
            <summary>Identifies the <see cref="P:ControlzEx.WindowChromeWindow.IsGlowTransitionEnabled"/> dependency property.</summary>
        </member>
        <member name="P:ControlzEx.WindowChromeWindow.IsGlowTransitionEnabled">
            <inheritdoc cref="P:ControlzEx.Behaviors.GlowWindowBehavior.IsGlowTransitionEnabled"/>
            <remarks>
            Only relevant if the DWM border is not used.
            </remarks>
        </member>
        <member name="F:ControlzEx.WindowChromeWindow.IgnoreTaskbarOnMaximizeProperty">
            <summary>Identifies the <see cref="P:ControlzEx.WindowChromeWindow.IgnoreTaskbarOnMaximize"/> dependency property.</summary>
        </member>
        <member name="P:ControlzEx.WindowChromeWindow.IgnoreTaskbarOnMaximize">
            <inheritdoc cref="P:ControlzEx.Behaviors.WindowChromeBehavior.IgnoreTaskbarOnMaximize"/>
        </member>
        <member name="P:ControlzEx.WindowChromeWindow.KeepBorderOnMaximize">
            <inheritdoc cref="P:ControlzEx.Behaviors.WindowChromeBehavior.KeepBorderOnMaximize"/>
        </member>
        <member name="F:ControlzEx.WindowChromeWindow.KeepBorderOnMaximizeProperty">
            <summary>Identifies the <see cref="P:ControlzEx.WindowChromeWindow.KeepBorderOnMaximize"/> dependency property.</summary>
        </member>
        <member name="F:ControlzEx.WindowChromeWindow.ShowMinButtonProperty">
            <summary>Identifies the <see cref="P:ControlzEx.WindowChromeWindow.ShowMinButton"/> dependency property.</summary>
        </member>
        <member name="P:ControlzEx.WindowChromeWindow.ShowMinButton">
            <inheritdoc cref="P:ControlzEx.Behaviors.WindowChromeBehavior.EnableMinimize"/>
        </member>
        <member name="F:ControlzEx.WindowChromeWindow.ShowMaxRestoreButtonProperty">
            <summary>Identifies the <see cref="P:ControlzEx.WindowChromeWindow.ShowMaxRestoreButton"/> dependency property.</summary>
        </member>
        <member name="P:ControlzEx.WindowChromeWindow.ShowMaxRestoreButton">
            <inheritdoc cref="P:ControlzEx.Behaviors.WindowChromeBehavior.EnableMaxRestore"/>
        </member>
        <member name="F:ControlzEx.WindowChromeWindow.GlowColorProperty">
            <summary>Identifies the <see cref="P:ControlzEx.WindowChromeWindow.GlowColor"/> dependency property.</summary>
        </member>
        <member name="P:ControlzEx.WindowChromeWindow.GlowColor">
            <inheritdoc cref="P:ControlzEx.Behaviors.GlowWindowBehavior.GlowColor"/>
        </member>
        <member name="F:ControlzEx.WindowChromeWindow.NonActiveGlowColorProperty">
            <summary>Identifies the <see cref="P:ControlzEx.WindowChromeWindow.NonActiveGlowColor"/> dependency property.</summary>
        </member>
        <member name="P:ControlzEx.WindowChromeWindow.NonActiveGlowColor">
            <inheritdoc cref="P:ControlzEx.Behaviors.GlowWindowBehavior.NonActiveGlowColor"/>
        </member>
        <member name="P:ControlzEx.WindowChromeWindow.CaptionColor">
            <summary>
            Gets or sets the native window caption color.
            </summary>
            <remarks>
            Only works on Windows 11 and later.
            </remarks>
        </member>
        <member name="F:ControlzEx.WindowChromeWindow.IsNCActiveProperty">
            <summary>Identifies the <see cref="P:ControlzEx.WindowChromeWindow.IsNCActive"/> dependency property.</summary>
        </member>
        <member name="P:ControlzEx.WindowChromeWindow.IsNCActive">
            <summary>
            Gets whether the non-client area is active or not.
            </summary>
        </member>
        <member name="F:ControlzEx.WindowChromeWindow.NCActiveBrushProperty">
            <summary>Identifies the <see cref="P:ControlzEx.WindowChromeWindow.NCActiveBrush"/> dependency property.</summary>
        </member>
        <member name="P:ControlzEx.WindowChromeWindow.NCActiveBrush">
            <summary>
            Defines the brush to use when the non-client area is active.
            </summary>
        </member>
        <member name="F:ControlzEx.WindowChromeWindow.NCNonActiveBrushProperty">
            <summary>Identifies the <see cref="P:ControlzEx.WindowChromeWindow.NCNonActiveBrush"/> dependency property.</summary>
        </member>
        <member name="P:ControlzEx.WindowChromeWindow.NCNonActiveBrush">
            <summary>
            Defines the brush to use when the non-client area is not active.
            </summary>
        </member>
        <member name="F:ControlzEx.WindowChromeWindow.NCCurrentBrushProperty">
            <summary>Identifies the <see cref="P:ControlzEx.WindowChromeWindow.NCCurrentBrush"/> dependency property.</summary>
        </member>
        <member name="P:ControlzEx.WindowChromeWindow.NCCurrentBrush">
            <summary>
            Defines the current non-client area brush (active or inactive).
            </summary>
            <remarks>
            This property should only be set through style triggers.
            </remarks>
        </member>
        <member name="F:ControlzEx.WindowChromeWindow.PreferDWMBorderColorProperty">
            <summary>Identifies the <see cref="P:ControlzEx.WindowChromeWindow.PreferDWMBorderColor"/> dependency property.</summary>
        </member>
        <member name="P:ControlzEx.WindowChromeWindow.PreferDWMBorderColor">
            <inheritdoc cref="P:ControlzEx.Behaviors.GlowWindowBehavior.PreferDWMBorderColor"/>
        </member>
        <member name="F:ControlzEx.WindowChromeWindow.DWMSupportsBorderColorProperty">
            <summary>Identifies the <see cref="P:ControlzEx.WindowChromeWindow.DWMSupportsBorderColor"/> dependency property.</summary>
        </member>
        <member name="P:ControlzEx.WindowChromeWindow.DWMSupportsBorderColor">
            <inheritdoc cref="P:ControlzEx.Behaviors.GlowWindowBehavior.DWMSupportsBorderColor"/>
        </member>
        <member name="F:ControlzEx.WindowChromeWindow.CornerPreferenceProperty">
            <summary>Identifies the <see cref="P:ControlzEx.WindowChromeWindow.CornerPreference"/> dependency property.</summary>
        </member>
        <member name="P:ControlzEx.WindowChromeWindow.CornerPreference">
            <inheritdoc cref="P:ControlzEx.Behaviors.WindowChromeBehavior.CornerPreference"/>
        </member>
        <member name="M:ControlzEx.WindowChromeWindow.OnActivated(System.EventArgs)">
            <inheritdoc />
        </member>
        <member name="M:ControlzEx.WindowChromeWindow.OnDeactivated(System.EventArgs)">
            <inheritdoc />
        </member>
        <member name="M:ControlzEx.WindowChromeWindow.OnStateChanged(System.EventArgs)">
            <inheritdoc />
        </member>
        <member name="M:ControlzEx.WindowChromeWindow.UpdatePadding">
            <summary>
            Updates the padding used for the window content.
            </summary>
        </member>
        <member name="M:ControlzEx.WindowChromeWindow.HandleERASEBKGND(Windows.Win32.WM,System.UIntPtr,System.IntPtr,System.Boolean@)">
            <SecurityNote>
                Critical : Calls critical methods
            </SecurityNote>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.AllowNullAttribute">
            <summary>Specifies that null is allowed as an input even if the corresponding type disallows it.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DisallowNullAttribute">
            <summary>Specifies that null is disallowed as an input even if the corresponding type allows it.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MaybeNullAttribute">
            <summary>Specifies that an output may be null even if the corresponding type disallows it.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.NotNullAttribute">
            <summary>Specifies that an output will not be null even if the corresponding type allows it. Specifies that an input argument was not null when the call returns.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute">
            <summary>Specifies that when a method returns <see cref="P:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute.ReturnValue"/>, the parameter may be null even if the corresponding type disallows it.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute.#ctor(System.Boolean)">
            <summary>Initializes the attribute with the specified return value condition.</summary>
            <param name="returnValue">
            The return value condition. If the method returns this value, the associated parameter may be null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MaybeNullWhenAttribute.ReturnValue">
            <summary>Gets the return value condition.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute">
            <summary>Specifies that when a method returns <see cref="P:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute.ReturnValue"/>, the parameter will not be null even if the corresponding type allows it.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute.#ctor(System.Boolean)">
            <summary>Initializes the attribute with the specified return value condition.</summary>
            <param name="returnValue">
            The return value condition. If the method returns this value, the associated parameter will not be null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.NotNullWhenAttribute.ReturnValue">
            <summary>Gets the return value condition.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.NotNullIfNotNullAttribute">
            <summary>Specifies that the output will be non-null if the named parameter is non-null.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.NotNullIfNotNullAttribute.#ctor(System.String)">
            <summary>Initializes the attribute with the associated parameter name.</summary>
            <param name="parameterName">
            The associated parameter name.  The output will be non-null if the argument to the parameter specified is non-null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.NotNullIfNotNullAttribute.ParameterName">
            <summary>Gets the associated parameter name.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DoesNotReturnAttribute">
            <summary>Applied to a method that will never return under any circumstance.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.DoesNotReturnIfAttribute">
            <summary>Specifies that the method will not return if the associated Boolean parameter is passed the specified value.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.DoesNotReturnIfAttribute.#ctor(System.Boolean)">
            <summary>Initializes the attribute with the specified parameter value.</summary>
            <param name="parameterValue">
            The condition parameter value. Code after the method will be considered unreachable by diagnostics if the argument to
            the associated parameter matches this value.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.DoesNotReturnIfAttribute.ParameterValue">
            <summary>Gets the condition parameter value.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute">
            <summary>Specifies that the method or property will ensure that the listed field and property members have not-null values.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute.#ctor(System.String)">
            <summary>Initializes the attribute with a field or property member.</summary>
            <param name="member">
            The field or property member that is promised to be not-null.
            </param>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute.#ctor(System.String[])">
            <summary>Initializes the attribute with the list of field and property members.</summary>
            <param name="members">
            The list of field and property members that are promised to be not-null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MemberNotNullAttribute.Members">
            <summary>Gets field or property member names.</summary>
        </member>
        <member name="T:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute">
            <summary>Specifies that the method or property will ensure that the listed field and property members have not-null values when returning with the specified return value condition.</summary>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.#ctor(System.Boolean,System.String)">
            <summary>Initializes the attribute with the specified return value condition and a field or property member.</summary>
            <param name="returnValue">
            The return value condition. If the method returns this value, the associated parameter will not be null.
            </param>
            <param name="member">
            The field or property member that is promised to be not-null.
            </param>
        </member>
        <member name="M:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.#ctor(System.Boolean,System.String[])">
            <summary>Initializes the attribute with the specified return value condition and list of field and property members.</summary>
            <param name="returnValue">
            The return value condition. If the method returns this value, the associated parameter will not be null.
            </param>
            <param name="members">
            The list of field and property members that are promised to be not-null.
            </param>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.ReturnValue">
            <summary>Gets the return value condition.</summary>
        </member>
        <member name="P:System.Diagnostics.CodeAnalysis.MemberNotNullWhenAttribute.Members">
            <summary>Gets field or property member names.</summary>
        </member>
        <member name="T:Windows.Win32.PInvoke">
            <content>
            Contains extern methods from "dwmapi.dll".
            </content>
            <content>
            Contains extern methods from "GDI32.dll".
            </content>
            <content>
            Contains extern methods from "KERNEL32.dll".
            </content>
            <content>
            Contains extern methods from "MSIMG32.dll".
            </content>
            <content>
            Contains extern methods from "USER32.dll".
            </content>
        </member>
        <member name="F:Windows.Win32.PInvoke.APPBARDATA.cbSize">
            <summary>
            initialize this field using: Marshal.SizeOf(typeof(APPBARDATA));
            </summary>
        </member>
        <member name="M:Windows.Win32.PInvoke.DwmDefWindowProc(Windows.Win32.Foundation.HWND,System.UInt32,Windows.Win32.Foundation.WPARAM,Windows.Win32.Foundation.LPARAM,Windows.Win32.Foundation.LRESULT*)">
            <summary>Default window procedure for Desktop Window Manager (DWM) hit testing within the non-client area.</summary>
            <param name="hWnd">A handle to the window procedure that received the message.</param>
            <param name="msg">The message.</param>
            <param name="wParam">Specifies additional message information. The content of this parameter depends on the value of the <i>msg</i> parameter.</param>
            <param name="lParam">Specifies additional message information. The content of this parameter depends on the value of the <i>msg</i> parameter.</param>
            <param name="plResult">A pointer to an <b>LRESULT</b> value that, when this method returns successfully,receives the result of the hit test.</param>
            <returns><b>TRUE</b> if <b>DwmDefWindowProc</b> handled the message; otherwise, <b>FALSE</b>.</returns>
            <remarks>
            <para><see href="https://docs.microsoft.com/windows/win32/api//dwmapi/nf-dwmapi-dwmdefwindowproc">Learn more about this API from docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="M:Windows.Win32.PInvoke.DwmExtendFrameIntoClientArea(Windows.Win32.Foundation.HWND,Windows.Win32.UI.Controls.MARGINS*)">
            <summary>Extends the window frame into the client area.</summary>
            <param name="hWnd">The handle to the window in which the frame will be extended into the client area.</param>
            <param name="pMarInset">A pointer to a <a href="https://docs.microsoft.com/windows/desktop/api/uxtheme/ns-uxtheme-margins">MARGINS</a> structure that describes the margins to use when extending the frame into the client area.</param>
            <returns>If this function succeeds, it returns <b xmlns:loc="http://microsoft.com/wdcml/l10n">S_OK</b>. Otherwise, it returns an <b xmlns:loc="http://microsoft.com/wdcml/l10n">HRESULT</b> error code.</returns>
            <remarks>
            <para><see href="https://docs.microsoft.com/windows/win32/api//dwmapi/nf-dwmapi-dwmextendframeintoclientarea">Learn more about this API from docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="M:Windows.Win32.PInvoke.DwmGetWindowAttribute(Windows.Win32.Foundation.HWND,Windows.Win32.Graphics.Dwm.DWMWINDOWATTRIBUTE,System.Void*,System.UInt32)">
            <summary>Retrieves the current value of a specified Desktop Window Manager (DWM) attribute applied to a window.</summary>
            <param name="hwnd">The handle to the window from which the attribute value is to be retrieved.</param>
            <param name="dwAttribute">A flag describing which value to retrieve, specified as a value of the [DWMWINDOWATTRIBUTE](/windows/desktop/api/dwmapi/ne-dwmapi-dwmwindowattribute) enumeration. This parameter specifies which attribute to retrieve, and the *pvAttribute* parameter points to an object into which the attribute value is retrieved.</param>
            <param name="pvAttribute">A pointer to a value which, when this function returns successfully, receives the current value of the attribute. The type of the retrieved value depends on the value of the *dwAttribute* parameter. The [**DWMWINDOWATTRIBUTE**](/windows/desktop/api/Dwmapi/ne-dwmapi-dwmwindowattribute) enumeration topic indicates, in the row for each flag, what type of value you should pass a pointer to in the *pvAttribute* parameter.</param>
            <param name="cbAttribute">The size, in bytes, of the attribute value being received via the *pvAttribute* parameter. The type of the retrieved value, and therefore its size in bytes, depends on the value of the *dwAttribute* parameter.</param>
            <returns>
            <para>Type: **[HRESULT](/windows/desktop/com/structure-of-com-error-codes)** If the function succeeds, it returns **S_OK**. Otherwise, it returns an [**HRESULT**](/windows/desktop/com/structure-of-com-error-codes) [error code](/windows/desktop/com/com-error-codes-10).</para>
            </returns>
            <remarks>
            <para><see href="https://docs.microsoft.com/windows/win32/api//dwmapi/nf-dwmapi-dwmgetwindowattribute">Learn more about this API from docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="M:Windows.Win32.PInvoke.DwmSetWindowAttribute(Windows.Win32.Foundation.HWND,Windows.Win32.Graphics.Dwm.DWMWINDOWATTRIBUTE,System.Void*,System.UInt32)">
            <summary>Sets the value of Desktop Window Manager (DWM) non-client rendering attributes for a window.</summary>
            <param name="hwnd">The handle to the window for which the attribute value is to be set.</param>
            <param name="dwAttribute">A flag describing which value to set, specified as a value of the [DWMWINDOWATTRIBUTE](/windows/desktop/api/dwmapi/ne-dwmapi-dwmwindowattribute) enumeration. This parameter specifies which attribute to set, and the *pvAttribute* parameter points to an object containing the attribute value.</param>
            <param name="pvAttribute">A pointer to an object containing the attribute value to set. The type of the value set depends on the value of the *dwAttribute* parameter. The [**DWMWINDOWATTRIBUTE**](/windows/desktop/api/Dwmapi/ne-dwmapi-dwmwindowattribute) enumeration topic indicates, in the row for each flag, what type of value you should pass a pointer to in the *pvAttribute* parameter.</param>
            <param name="cbAttribute">The size, in bytes, of the attribute value being set via the *pvAttribute* parameter. The type of the value set, and therefore its size in bytes, depends on the value of the *dwAttribute* parameter.</param>
            <returns>
            <para>Type: **[HRESULT](/windows/desktop/com/structure-of-com-error-codes)** If the function succeeds, it returns **S_OK**. Otherwise, it returns an [**HRESULT**](/windows/desktop/com/structure-of-com-error-codes) [error code](/windows/desktop/com/com-error-codes-10). If Desktop Composition has been disabled (Windows 7 and earlier), then this function returns **DWM_E_COMPOSITIONDISABLED**.</para>
            </returns>
            <remarks>
            <para><see href="https://docs.microsoft.com/windows/win32/api//dwmapi/nf-dwmapi-dwmsetwindowattribute">Learn more about this API from docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="M:Windows.Win32.PInvoke.DeleteDC(Windows.Win32.Graphics.Gdi.CreatedHDC)">
            <summary>The DeleteDC function deletes the specified device context (DC).</summary>
            <param name="hdc">A handle to the device context.</param>
            <returns>
            <para>If the function succeeds, the return value is nonzero. If the function fails, the return value is zero.</para>
            </returns>
            <remarks>
            <para><see href="https://docs.microsoft.com/windows/win32/api//wingdi/nf-wingdi-deletedc">Learn more about this API from docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="M:Windows.Win32.PInvoke.CreateCompatibleDC(System.Runtime.InteropServices.SafeHandle)">
            <inheritdoc cref="M:Windows.Win32.PInvoke.CreateCompatibleDC(Windows.Win32.Graphics.Gdi.HDC)"/>
        </member>
        <member name="M:Windows.Win32.PInvoke.CreateCompatibleDC(Windows.Win32.Graphics.Gdi.HDC)">
            <summary>The CreateCompatibleDC function creates a memory device context (DC) compatible with the specified device.</summary>
            <param name="hdc">A handle to an existing DC. If this handle is <b>NULL</b>, the function creates a memory DC compatible with the application's current screen.</param>
            <returns>
            <para>If the function succeeds, the return value is the handle to a memory DC. If the function fails, the return value is <b>NULL</b>.</para>
            </returns>
            <remarks>
            <para><see href="https://docs.microsoft.com/windows/win32/api//wingdi/nf-wingdi-createcompatibledc">Learn more about this API from docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="M:Windows.Win32.PInvoke.DeleteObject(Windows.Win32.Graphics.Gdi.HGDIOBJ)">
            <summary>The DeleteObject function deletes a logical pen, brush, font, bitmap, region, or palette, freeing all system resources associated with the object. After the object is deleted, the specified handle is no longer valid.</summary>
            <param name="ho">A handle to a logical pen, brush, font, bitmap, region, or palette.</param>
            <returns>
            <para>If the function succeeds, the return value is nonzero. If the specified handle is not valid or is currently selected into a DC, the return value is zero.</para>
            </returns>
            <remarks>
            <para><see href="https://docs.microsoft.com/windows/win32/api//wingdi/nf-wingdi-deleteobject">Learn more about this API from docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="M:Windows.Win32.PInvoke.CreateDIBSection(System.Runtime.InteropServices.SafeHandle,Windows.Win32.Graphics.Gdi.BITMAPINFO*,Windows.Win32.Graphics.Gdi.DIB_USAGE,System.Void**,System.Runtime.InteropServices.SafeHandle,System.UInt32)">
            <inheritdoc cref="M:Windows.Win32.PInvoke.CreateDIBSection(Windows.Win32.Graphics.Gdi.HDC,Windows.Win32.Graphics.Gdi.BITMAPINFO*,Windows.Win32.Graphics.Gdi.DIB_USAGE,System.Void**,Windows.Win32.Foundation.HANDLE,System.UInt32)"/>
        </member>
        <member name="M:Windows.Win32.PInvoke.CreateDIBSection(Windows.Win32.Graphics.Gdi.HDC,Windows.Win32.Graphics.Gdi.BITMAPINFO*,Windows.Win32.Graphics.Gdi.DIB_USAGE,System.Void**,Windows.Win32.Foundation.HANDLE,System.UInt32)">
            <summary>The CreateDIBSection function creates a DIB that applications can write to directly.</summary>
            <param name="hdc">A handle to a device context. If the value of <i>iUsage</i> is DIB_PAL_COLORS, the function uses this device context's logical palette to initialize the DIB colors.</param>
            <param name="pbmi">A pointer to a <a href="https://docs.microsoft.com/windows/desktop/api/wingdi/ns-wingdi-bitmapinfo">BITMAPINFO</a> structure that specifies various attributes of the DIB, including the bitmap dimensions and colors.</param>
            <param name="usage">
            <para>The type of data contained in the <b>bmiColors</b> array member of the <a href="https://docs.microsoft.com/windows/desktop/api/wingdi/ns-wingdi-bitmapinfo">BITMAPINFO</a> structure pointed to by <i>pbmi</i> (either logical palette indexes or literal RGB values). The following values are defined. </para>
            <para>This doc was truncated.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//wingdi/nf-wingdi-createdibsection#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <param name="ppvBits">A pointer to a variable that receives a pointer to the location of the DIB bit values.</param>
            <param name="hSection">
            <para>A handle to a file-mapping object that the function will use to create the DIB. This parameter can be <b>NULL</b>. If <i>hSection</i> is not <b>NULL</b>, it must be a handle to a file-mapping object created by calling the <a href="https://docs.microsoft.com/windows/desktop/api/winbase/nf-winbase-createfilemappinga">CreateFileMapping</a> function with the PAGE_READWRITE or PAGE_WRITECOPY flag. Read-only DIB sections are not supported. Handles created by other means will cause <b>CreateDIBSection</b> to fail. If <i>hSection</i> is not <b>NULL</b>, the <b>CreateDIBSection</b> function locates the bitmap bit values at offset <i>dwOffset</i> in the file-mapping object referred to by <i>hSection</i>. An application can later retrieve the <i>hSection</i> handle by calling the <a href="https://docs.microsoft.com/windows/desktop/api/wingdi/nf-wingdi-getobject">GetObject</a> function with the <b>HBITMAP</b> returned by <b>CreateDIBSection</b>. If <i>hSection</i> is <b>NULL</b>, the system allocates memory for the DIB. In this case, the <b>CreateDIBSection</b> function ignores the <i>dwOffset</i> parameter. An application cannot later obtain a handle to this memory. The <b>dshSection</b> member of the <a href="https://docs.microsoft.com/windows/desktop/api/wingdi/ns-wingdi-dibsection">DIBSECTION</a> structure filled in by calling the <a href="https://docs.microsoft.com/windows/desktop/api/wingdi/nf-wingdi-getobject">GetObject</a> function will be <b>NULL</b>.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//wingdi/nf-wingdi-createdibsection#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <param name="offset">The offset from the beginning of the file-mapping object referenced by <i>hSection</i> where storage for the bitmap bit values is to begin. This value is ignored if <i>hSection</i> is <b>NULL</b>. The bitmap bit values are aligned on doubleword boundaries, so <i>dwOffset</i> must be a multiple of the size of a <b>DWORD</b>.</param>
            <returns>
            <para>If the function succeeds, the return value is a handle to the newly created DIB, and *<i>ppvBits</i> points to the bitmap bit values. If the function fails, the return value is <b>NULL</b>, and *<i>ppvBits</i> is <b>NULL</b>. To get extended error information, call <a href="/windows/desktop/api/errhandlingapi/nf-errhandlingapi-getlasterror">GetLastError</a>. <a href="/windows/desktop/api/errhandlingapi/nf-errhandlingapi-getlasterror">GetLastError</a> can return the following value: </para>
            <para>This doc was truncated.</para>
            </returns>
            <remarks>
            <para><see href="https://docs.microsoft.com/windows/win32/api//wingdi/nf-wingdi-createdibsection">Learn more about this API from docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="M:Windows.Win32.PInvoke.GetDeviceCaps(System.Runtime.InteropServices.SafeHandle,Windows.Win32.Graphics.Gdi.GET_DEVICE_CAPS_INDEX)">
            <inheritdoc cref="M:Windows.Win32.PInvoke.GetDeviceCaps(Windows.Win32.Graphics.Gdi.HDC,Windows.Win32.Graphics.Gdi.GET_DEVICE_CAPS_INDEX)"/>
        </member>
        <member name="M:Windows.Win32.PInvoke.GetDeviceCaps(Windows.Win32.Graphics.Gdi.HDC,Windows.Win32.Graphics.Gdi.GET_DEVICE_CAPS_INDEX)">
            <summary>The GetDeviceCaps function retrieves device-specific information for the specified device.</summary>
            <param name="hdc">A handle to the DC.</param>
            <param name="index"></param>
            <returns>
            <para>The return value specifies the value of the desired item. When <i>nIndex</i> is BITSPIXEL and the device has 15bpp or 16bpp, the return value is 16.</para>
            </returns>
            <remarks>
            <para><see href="https://docs.microsoft.com/windows/win32/api//wingdi/nf-wingdi-getdevicecaps">Learn more about this API from docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="M:Windows.Win32.PInvoke.SelectObject(System.Runtime.InteropServices.SafeHandle,Windows.Win32.Graphics.Gdi.HGDIOBJ)">
            <inheritdoc cref="M:Windows.Win32.PInvoke.SelectObject(Windows.Win32.Graphics.Gdi.HDC,Windows.Win32.Graphics.Gdi.HGDIOBJ)"/>
        </member>
        <member name="M:Windows.Win32.PInvoke.SelectObject(Windows.Win32.Graphics.Gdi.HDC,Windows.Win32.Graphics.Gdi.HGDIOBJ)">
            <summary>The SelectObject function selects an object into the specified device context (DC). The new object replaces the previous object of the same type.</summary>
            <param name="hdc">A handle to the DC.</param>
            <param name="h">
            <para>A handle to the object to be selected. The specified object must have been created by using one of the following functions. </para>
            <para>This doc was truncated.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//wingdi/nf-wingdi-selectobject#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <returns>
            <para>If the selected object is not a region and the function succeeds, the return value is a handle to the object being replaced. If the selected object is a region and the function succeeds, the return value is one of the following values. </para>
            <para>This doc was truncated.</para>
            </returns>
            <remarks>
            <para><see href="https://docs.microsoft.com/windows/win32/api//wingdi/nf-wingdi-selectobject">Learn more about this API from docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="M:Windows.Win32.PInvoke.GetStockObject(Windows.Win32.Graphics.Gdi.GET_STOCK_OBJECT_FLAGS)">
            <summary>The GetStockObject function retrieves a handle to one of the stock pens, brushes, fonts, or palettes.</summary>
            <param name="i"></param>
            <returns>
            <para>If the function succeeds, the return value is a handle to the requested logical object. If the function fails, the return value is <b>NULL</b>.</para>
            </returns>
            <remarks>
            <para><see href="https://docs.microsoft.com/windows/win32/api//wingdi/nf-wingdi-getstockobject">Learn more about this API from docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="M:Windows.Win32.PInvoke.CloseHandle(Windows.Win32.Foundation.HANDLE)">
            <summary>Closes an open object handle.</summary>
            <param name="hObject">A valid handle to an open object.</param>
            <returns>
            <para>If the function succeeds, the return value is nonzero. If the function fails, the return value is zero. To get extended error information, call <a href="/windows/desktop/api/errhandlingapi/nf-errhandlingapi-getlasterror">GetLastError</a>. If the application is running under a debugger,  the function will throw an exception if it receives either a  handle value that is not valid  or a pseudo-handle value. This can happen if you close a handle twice, or if you  call <b>CloseHandle</b> on a handle returned by the <a href="/windows/desktop/api/fileapi/nf-fileapi-findfirstfilea">FindFirstFile</a> function instead of calling the <a href="/windows/desktop/api/fileapi/nf-fileapi-findclose">FindClose</a> function.</para>
            </returns>
            <remarks>
            <para><see href="https://docs.microsoft.com/windows/win32/api//handleapi/nf-handleapi-closehandle">Learn more about this API from docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="M:Windows.Win32.PInvoke.FreeLibrary(Windows.Win32.Foundation.HINSTANCE)">
            <summary>Frees the loaded dynamic-link library (DLL) module and, if necessary, decrements its reference count.</summary>
            <param name="hLibModule">
            <para>A handle to the loaded library module. The <a href="https://docs.microsoft.com/windows/desktop/api/libloaderapi/nf-libloaderapi-loadlibrarya">LoadLibrary</a>, <a href="https://docs.microsoft.com/windows/desktop/api/libloaderapi/nf-libloaderapi-loadlibraryexa">LoadLibraryEx</a>, <a href="https://docs.microsoft.com/windows/desktop/api/libloaderapi/nf-libloaderapi-getmodulehandlea">GetModuleHandle</a>, or <a href="https://docs.microsoft.com/windows/desktop/api/libloaderapi/nf-libloaderapi-getmodulehandleexa">GetModuleHandleEx</a> function returns this handle.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//libloaderapi/nf-libloaderapi-freelibrary#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <returns>
            <para>If the function succeeds, the return value is nonzero. If the function fails, the return value is zero. To get extended error information, call the <a href="/windows/desktop/api/errhandlingapi/nf-errhandlingapi-getlasterror">GetLastError</a> function.</para>
            </returns>
            <remarks>
            <para><see href="https://docs.microsoft.com/windows/win32/api//libloaderapi/nf-libloaderapi-freelibrary">Learn more about this API from docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="M:Windows.Win32.PInvoke.GetModuleHandle(System.String)">
            <inheritdoc cref="M:Windows.Win32.PInvoke.GetModuleHandle(Windows.Win32.Foundation.PCWSTR)"/>
        </member>
        <member name="M:Windows.Win32.PInvoke.GetModuleHandle(Windows.Win32.Foundation.PCWSTR)">
            <summary>Retrieves a module handle for the specified module. The module must have been loaded by the calling process.</summary>
            <param name="lpModuleName">
            <para>The name of the loaded module (either a .dll or .exe file). If the file name extension is omitted, the default library extension .dll is appended. The file name string can include a trailing point character (.) to indicate that the module name has no extension. The string does not have to specify a path. When specifying a path, be sure to use backslashes (\\), not forward slashes (/). The name is compared (case independently) to the names of modules currently mapped into the address space of the calling process.</para>
            <para>If this parameter is NULL, <b>GetModuleHandle</b> returns a handle to the file used to create the calling process (.exe file). The <b>GetModuleHandle</b> function does not retrieve handles for modules that were loaded using the <b>LOAD_LIBRARY_AS_DATAFILE</b> flag. For more information, see <a href="https://docs.microsoft.com/windows/desktop/api/libloaderapi/nf-libloaderapi-loadlibraryexa">LoadLibraryEx</a>.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//libloaderapi/nf-libloaderapi-getmodulehandlew#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <returns>
            <para>If the function succeeds, the return value is a handle to the specified module. If the function fails, the return value is NULL. To get extended error information, call <a href="/windows/desktop/api/errhandlingapi/nf-errhandlingapi-getlasterror">GetLastError</a>.</para>
            </returns>
            <remarks>
            <para><see href="https://docs.microsoft.com/windows/win32/api//libloaderapi/nf-libloaderapi-getmodulehandlew">Learn more about this API from docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="M:Windows.Win32.PInvoke.AlphaBlend(System.Runtime.InteropServices.SafeHandle,System.Int32,System.Int32,System.Int32,System.Int32,System.Runtime.InteropServices.SafeHandle,System.Int32,System.Int32,System.Int32,System.Int32,Windows.Win32.Graphics.Gdi.BLENDFUNCTION)">
            <inheritdoc cref="M:Windows.Win32.PInvoke.AlphaBlend(Windows.Win32.Graphics.Gdi.HDC,System.Int32,System.Int32,System.Int32,System.Int32,Windows.Win32.Graphics.Gdi.HDC,System.Int32,System.Int32,System.Int32,System.Int32,Windows.Win32.Graphics.Gdi.BLENDFUNCTION)"/>
        </member>
        <member name="M:Windows.Win32.PInvoke.AlphaBlend(Windows.Win32.Graphics.Gdi.HDC,System.Int32,System.Int32,System.Int32,System.Int32,Windows.Win32.Graphics.Gdi.HDC,System.Int32,System.Int32,System.Int32,System.Int32,Windows.Win32.Graphics.Gdi.BLENDFUNCTION)">
            <summary>The AlphaBlend function displays bitmaps that have transparent or semitransparent pixels.</summary>
            <param name="hdcDest">A handle to the destination device context.</param>
            <param name="xoriginDest">The x-coordinate, in logical units, of the upper-left corner of the destination rectangle.</param>
            <param name="yoriginDest">The y-coordinate, in logical units, of the upper-left corner of the destination rectangle.</param>
            <param name="wDest">The width, in logical units, of the destination rectangle.</param>
            <param name="hDest">The height, in logical units, of the destination rectangle.</param>
            <param name="hdcSrc">A handle to the source device context.</param>
            <param name="xoriginSrc">The x-coordinate, in logical units, of the upper-left corner of the source rectangle.</param>
            <param name="yoriginSrc">The y-coordinate, in logical units, of the upper-left corner of the source rectangle.</param>
            <param name="wSrc">The width, in logical units, of the source rectangle.</param>
            <param name="hSrc">The height, in logical units, of the source rectangle.</param>
            <param name="ftn">The alpha-blending function for source and destination bitmaps, a global alpha value to be applied to the entire source bitmap, and format information for the source bitmap. The source and destination blend functions are currently limited to AC_SRC_OVER. See the <a href="https://docs.microsoft.com/windows/desktop/api/wingdi/ns-wingdi-blendfunction">BLENDFUNCTION</a> and <a href="https://docs.microsoft.com/windows/desktop/api/wingdi/ns-wingdi-emralphablend">EMRALPHABLEND</a> structures.</param>
            <returns>
            <para>If the function succeeds, the return value is <b>TRUE</b>. If the function fails, the return value is <b>FALSE</b>.</para>
            </returns>
            <remarks>
            <para><see href="https://docs.microsoft.com/windows/win32/api//wingdi/nf-wingdi-alphablend">Learn more about this API from docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="M:Windows.Win32.PInvoke.ReleaseDC(Windows.Win32.Foundation.HWND,Windows.Win32.Graphics.Gdi.HDC)">
            <summary>The ReleaseDC function releases a device context (DC), freeing it for use by other applications. The effect of the ReleaseDC function depends on the type of DC. It frees only common and window DCs. It has no effect on class or private DCs.</summary>
            <param name="hWnd">A handle to the window whose DC is to be released.</param>
            <param name="hDC">A handle to the DC to be released.</param>
            <returns>
            <para>The return value indicates whether the DC was released. If the DC was released, the return value is 1. If the DC was not released, the return value is zero.</para>
            </returns>
            <remarks>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-releasedc">Learn more about this API from docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="M:Windows.Win32.PInvoke.AdjustWindowRect(Windows.Win32.Foundation.RECT*,Windows.Win32.UI.WindowsAndMessaging.WINDOW_STYLE,Windows.Win32.Foundation.BOOL)">
            <summary>Calculates the required size of the window rectangle, based on the desired client-rectangle size. The window rectangle can then be passed to the CreateWindow function to create a window whose client area is the desired size.</summary>
            <param name="lpRect">
            <para>Type: <b>LPRECT</b> A pointer to a <a href="https://docs.microsoft.com/windows/desktop/api/windef/ns-windef-rect">RECT</a> structure that contains the coordinates of the top-left and bottom-right corners of the desired client area. When the function returns, the structure contains the coordinates of the top-left and bottom-right corners of the window to accommodate the desired client area.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-adjustwindowrect#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <param name="dwStyle">
            <para>Type: <b>DWORD</b> The <a href="https://docs.microsoft.com/windows/desktop/winmsg/window-styles">window style</a> of the window whose required size is to be calculated. Note that you cannot specify the <b>WS_OVERLAPPED</b> style.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-adjustwindowrect#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <param name="bMenu">
            <para>Type: <b>BOOL</b> Indicates whether the window has a menu.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-adjustwindowrect#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <returns>
            <para>Type: <b>BOOL</b> If the function succeeds, the return value is nonzero. If the function fails, the return value is zero. To get extended error information, call <a href="/windows/desktop/api/errhandlingapi/nf-errhandlingapi-getlasterror">GetLastError</a>.</para>
            </returns>
            <remarks>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-adjustwindowrect">Learn more about this API from docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="M:Windows.Win32.PInvoke.BeginDeferWindowPos(System.Int32)">
            <summary>Allocates memory for a multiple-window- position structure and returns the handle to the structure.</summary>
            <param name="nNumWindows">
            <para>Type: <b>int</b> The initial number of windows for which to store position information. The <a href="https://docs.microsoft.com/windows/desktop/api/winuser/nf-winuser-deferwindowpos">DeferWindowPos</a> function increases the size of the structure, if necessary.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-begindeferwindowpos#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <returns>
            <para>Type: <b>HDWP</b> If the function succeeds, the return value identifies the multiple-window-position structure. If insufficient system resources are available to allocate the structure, the return value is <b>NULL</b>. To get extended error information, call <a href="/windows/desktop/api/errhandlingapi/nf-errhandlingapi-getlasterror">GetLastError</a>.</para>
            </returns>
            <remarks>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-begindeferwindowpos">Learn more about this API from docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="M:Windows.Win32.PInvoke.DestroyMenu(Windows.Win32.UI.WindowsAndMessaging.HMENU)">
            <summary>Destroys the specified menu and frees any memory that the menu occupies.</summary>
            <param name="hMenu">
            <para>Type: <b>HMENU</b> A handle to the menu to be destroyed.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-destroymenu#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <returns>
            <para>Type: <b>BOOL</b> If the function succeeds, the return value is nonzero. If the function fails, the return value is zero. To get extended error information, call <a href="/windows/desktop/api/errhandlingapi/nf-errhandlingapi-getlasterror">GetLastError</a>.</para>
            </returns>
            <remarks>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-destroymenu">Learn more about this API from docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="M:Windows.Win32.PInvoke.CreateWindowEx(Windows.Win32.UI.WindowsAndMessaging.WINDOW_EX_STYLE,System.String,System.String,Windows.Win32.UI.WindowsAndMessaging.WINDOW_STYLE,System.Int32,System.Int32,System.Int32,System.Int32,Windows.Win32.Foundation.HWND,System.Runtime.InteropServices.SafeHandle,System.Runtime.InteropServices.SafeHandle,System.Void*)">
            <inheritdoc cref="M:Windows.Win32.PInvoke.CreateWindowEx(Windows.Win32.UI.WindowsAndMessaging.WINDOW_EX_STYLE,Windows.Win32.Foundation.PCWSTR,Windows.Win32.Foundation.PCWSTR,Windows.Win32.UI.WindowsAndMessaging.WINDOW_STYLE,System.Int32,System.Int32,System.Int32,System.Int32,Windows.Win32.Foundation.HWND,Windows.Win32.UI.WindowsAndMessaging.HMENU,Windows.Win32.Foundation.HINSTANCE,System.Void*)"/>
        </member>
        <member name="M:Windows.Win32.PInvoke.CreateWindowEx(Windows.Win32.UI.WindowsAndMessaging.WINDOW_EX_STYLE,Windows.Win32.Foundation.PCWSTR,Windows.Win32.Foundation.PCWSTR,Windows.Win32.UI.WindowsAndMessaging.WINDOW_STYLE,System.Int32,System.Int32,System.Int32,System.Int32,Windows.Win32.Foundation.HWND,Windows.Win32.UI.WindowsAndMessaging.HMENU,Windows.Win32.Foundation.HINSTANCE,System.Void*)">
            <summary>Creates an overlapped, pop-up, or child window with an extended window style; otherwise, this function is identical to the CreateWindow function.</summary>
            <param name="dwExStyle">
            <para>Type: <b>DWORD</b> The extended window style of the window being created. For a list of possible values, see  <a href="https://docs.microsoft.com/windows/desktop/winmsg/extended-window-styles">Extended Window Styles</a>.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-createwindowexw#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <param name="lpClassName">
            <para>Type: <b>LPCTSTR</b> A <b>null</b>-terminated string or a class atom created by a previous call to the <a href="https://docs.microsoft.com/windows/desktop/api/winuser/nf-winuser-registerclassa">RegisterClass</a> or <a href="https://docs.microsoft.com/windows/desktop/api/winuser/nf-winuser-registerclassexa">RegisterClassEx</a> function. The atom must be in the low-order word of <i>lpClassName</i>; the high-order word must be zero. If <i>lpClassName</i> is a string, it specifies the window class name. The class name can be any name registered with <b>RegisterClass</b> or <b>RegisterClassEx</b>, provided that the module that registers the class is also the module that creates the window. The class name can also be any of the predefined <a href="https://docs.microsoft.com/windows/desktop/winmsg/about-window-classes">system class</a> names.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-createwindowexw#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <param name="lpWindowName">
            <para>Type: <b>LPCTSTR</b> The window name. If the window style specifies a title bar, the window title pointed to by <i>lpWindowName</i> is displayed in the title bar. When using <a href="https://docs.microsoft.com/windows/desktop/api/winuser/nf-winuser-createwindowa">CreateWindow</a> to create controls, such as buttons, check boxes, and static controls, use <i>lpWindowName</i> to specify the text of the control. When creating a static control with the <b>SS_ICON</b> style, use <i>lpWindowName</i> to specify the icon name or identifier. To specify an identifier, use the syntax "#<i>num</i>".</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-createwindowexw#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <param name="dwStyle">
            <para>Type: <b>DWORD</b> The style of the window being created. This parameter can be a combination of the <a href="https://docs.microsoft.com/windows/desktop/winmsg/window-styles">window style values</a>, plus the control styles indicated in the Remarks section.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-createwindowexw#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <param name="X">
            <para>Type: <b>int</b> The initial horizontal position of the window. For an overlapped or pop-up window, the <i>x</i> parameter is the initial x-coordinate of the window's upper-left corner, in screen coordinates. For a child window, <i>x</i> is the x-coordinate of the upper-left corner of the window relative to the upper-left corner of the parent window's client area. If <i>x</i> is set to <b>CW_USEDEFAULT</b>, the system selects the default position for the window's upper-left corner and ignores the <i>y</i> parameter. <b>CW_USEDEFAULT</b> is valid only for overlapped windows; if it is specified for a pop-up or child window, the <i>x</i> and <i>y</i> parameters are set to zero.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-createwindowexw#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <param name="Y">
            <para>Type: <b>int</b> The initial vertical position of the window. For an overlapped or pop-up window, the <i>y</i> parameter is the initial y-coordinate of the window's upper-left corner, in screen coordinates. For a child window, <i>y</i> is the initial y-coordinate of the upper-left corner of the child window relative to the upper-left corner of the parent window's client area. For a list box <i>y</i> is the initial y-coordinate of the upper-left corner of the list box's client area relative to the upper-left corner of the parent window's client area.</para>
            <para>If an overlapped window is created with the <b>WS_VISIBLE</b> style bit set and the <i>x</i> parameter is set to <b>CW_USEDEFAULT</b>, then the <i>y</i> parameter determines how the window is shown. If the <i>y</i> parameter is <b>CW_USEDEFAULT</b>, then the window manager calls <a href="https://docs.microsoft.com/windows/desktop/api/winuser/nf-winuser-showwindow">ShowWindow</a> with the <b>SW_SHOW</b> flag after the window has been created. If the <i>y</i> parameter is some other value, then the window manager calls <b>ShowWindow</b> with that value as the <i>nCmdShow</i> parameter.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-createwindowexw#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <param name="nWidth">
            <para>Type: <b>int</b> The width, in device units, of the window. For overlapped windows, <i>nWidth</i> is the window's width, in screen coordinates, or <b>CW_USEDEFAULT</b>. If <i>nWidth</i> is <b>CW_USEDEFAULT</b>, the system selects a default width and height for the window; the default width extends from the initial x-coordinates to the right edge of the screen; the default height extends from the initial y-coordinate to the top of the icon area. <b>CW_USEDEFAULT</b> is valid only for overlapped windows; if <b>CW_USEDEFAULT</b> is specified for a pop-up or child window, the <i>nWidth</i> and <i>nHeight</i> parameter are set to zero.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-createwindowexw#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <param name="nHeight">
            <para>Type: <b>int</b> The height, in device units, of the window. For overlapped windows, <i>nHeight</i> is the window's height, in screen coordinates. If the <i>nWidth</i> parameter is set to <b>CW_USEDEFAULT</b>, the system ignores <i>nHeight</i>.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-createwindowexw#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <param name="hWndParent">
            <para>Type: <b>HWND</b> A handle to the parent or owner window of the window being created. To create a child window or an owned window, supply a valid window handle. This parameter is optional for pop-up windows. To create a <a href="https://docs.microsoft.com/windows/desktop/winmsg/window-features">message-only window</a>, supply <b>HWND_MESSAGE</b> or a handle to an existing message-only window.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-createwindowexw#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <param name="hMenu">
            <para>Type: <b>HMENU</b> A handle to a menu, or specifies a child-window identifier, depending on the window style. For an overlapped or pop-up window, <i>hMenu</i> identifies the menu to be used with the window; it can be <b>NULL</b> if the class menu is to be used. For a child window, <i>hMenu</i> specifies the child-window identifier, an integer value used by a dialog box control to notify its parent about events. The application determines the child-window identifier; it must be unique for all child windows with the same parent window.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-createwindowexw#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <param name="hInstance">
            <para>Type: <b>HINSTANCE</b> A handle to the instance of the module to be associated with the window.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-createwindowexw#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <param name="lpParam">
            <para>Type: <b>LPVOID</b> Pointer to a value to be passed to the window through the <a href="https://docs.microsoft.com/windows/desktop/api/winuser/ns-winuser-createstructa">CREATESTRUCT</a> structure (<b>lpCreateParams</b> member) pointed to by the <i>lParam</i> param of the <b>WM_CREATE</b> message.  This message is sent to the created window by this function before it returns. If an application calls <a href="https://docs.microsoft.com/windows/desktop/api/winuser/nf-winuser-createwindowa">CreateWindow</a> to create a MDI client window, <i>lpParam</i> should point to a <a href="https://docs.microsoft.com/windows/desktop/api/winuser/ns-winuser-clientcreatestruct">CLIENTCREATESTRUCT</a> structure. If an MDI client window calls <b>CreateWindow</b> to create an MDI child window, <i>lpParam</i> should point to a <a href="https://docs.microsoft.com/windows/desktop/api/winuser/ns-winuser-mdicreatestructa">MDICREATESTRUCT</a> structure. <i>lpParam</i> may be <b>NULL</b> if no additional data is needed.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-createwindowexw#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <returns>
            <para>Type: <b>HWND</b> If the function succeeds, the return value is a handle to the new window. If the function fails, the return value is <b>NULL</b>. To get extended error information, call <a href="/windows/desktop/api/errhandlingapi/nf-errhandlingapi-getlasterror">GetLastError</a>. This function typically fails for one of the following reasons: </para>
            <para>This doc was truncated.</para>
            </returns>
            <remarks>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-createwindowexw">Learn more about this API from docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="M:Windows.Win32.PInvoke.DefWindowProc(Windows.Win32.Foundation.HWND,System.UInt32,Windows.Win32.Foundation.WPARAM,Windows.Win32.Foundation.LPARAM)">
            <summary>Calls the default window procedure to provide default processing for any window messages that an application does not process.</summary>
            <param name="hWnd">
            <para>Type: <b>HWND</b> A handle to the window procedure that received the message.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-defwindowprocw#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <param name="Msg">
            <para>Type: <b>UINT</b> The message.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-defwindowprocw#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <param name="wParam">
            <para>Type: <b>WPARAM</b> Additional message information. The content of this parameter depends on the value of the <i>Msg</i> parameter.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-defwindowprocw#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <param name="lParam">
            <para>Type: <b>LPARAM</b> Additional message information. The content of this parameter depends on the value of the <i>Msg</i> parameter.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-defwindowprocw#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <returns>
            <para>Type: <b>LRESULT</b> The return value is the result of the message processing and depends on the message.</para>
            </returns>
            <remarks>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-defwindowprocw">Learn more about this API from docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="M:Windows.Win32.PInvoke.DeferWindowPos(System.IntPtr,Windows.Win32.Foundation.HWND,Windows.Win32.Foundation.HWND,System.Int32,System.Int32,System.Int32,System.Int32,Windows.Win32.UI.WindowsAndMessaging.SET_WINDOW_POS_FLAGS)">
            <summary>Updates the specified multiple-window � position structure for the specified window.</summary>
            <param name="hWinPosInfo">
            <para>Type: <b>HDWP</b> A handle to a multiple-window – position structure that contains size and position information for one or more windows. This structure is returned by <a href="https://docs.microsoft.com/windows/desktop/api/winuser/nf-winuser-begindeferwindowpos">BeginDeferWindowPos</a> or by the most recent call to <b>DeferWindowPos</b>.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-deferwindowpos#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <param name="hWnd">
            <para>Type: <b>HWND</b> A handle to the window for which update information is stored in the structure. All windows in a multiple-window – position structure must have the same parent.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-deferwindowpos#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <param name="hWndInsertAfter">Type: <b>HWND</b></param>
            <param name="x">
            <para>Type: <b>int</b> The x-coordinate of the window's upper-left corner.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-deferwindowpos#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <param name="y">
            <para>Type: <b>int</b> The y-coordinate of the window's upper-left corner.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-deferwindowpos#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <param name="cx">
            <para>Type: <b>int</b> The window's new width, in pixels.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-deferwindowpos#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <param name="cy">
            <para>Type: <b>int</b> The window's new height, in pixels.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-deferwindowpos#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <param name="uFlags">Type: <b>UINT</b></param>
            <returns>
            <para>Type: <b>HDWP</b> The return value identifies the updated multiple-window – position structure. The handle returned by this function may differ from the handle passed to the function. The new handle that this function returns should be passed during the next call to the <b>DeferWindowPos</b> or <a href="/windows/desktop/api/winuser/nf-winuser-enddeferwindowpos">EndDeferWindowPos</a> function. If insufficient system resources are available for the function to succeed, the return value is <b>NULL</b>. To get extended error information, call <a href="/windows/desktop/api/errhandlingapi/nf-errhandlingapi-getlasterror">GetLastError</a>.</para>
            </returns>
            <remarks>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-deferwindowpos">Learn more about this API from docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="M:Windows.Win32.PInvoke.DestroyWindow(Windows.Win32.Foundation.HWND)">
            <summary>Destroys the specified window.</summary>
            <param name="hWnd">
            <para>Type: <b>HWND</b> A handle to the window to be destroyed.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-destroywindow#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <returns>
            <para>Type: <b>BOOL</b> If the function succeeds, the return value is nonzero. If the function fails, the return value is zero. To get extended error information, call <a href="/windows/desktop/api/errhandlingapi/nf-errhandlingapi-getlasterror">GetLastError</a>.</para>
            </returns>
            <remarks>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-destroywindow">Learn more about this API from docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="M:Windows.Win32.PInvoke.EnableMenuItem(System.Runtime.InteropServices.SafeHandle,System.UInt32,Windows.Win32.UI.WindowsAndMessaging.MENU_ITEM_FLAGS)">
            <inheritdoc cref="M:Windows.Win32.PInvoke.EnableMenuItem(Windows.Win32.UI.WindowsAndMessaging.HMENU,System.UInt32,Windows.Win32.UI.WindowsAndMessaging.MENU_ITEM_FLAGS)"/>
        </member>
        <member name="M:Windows.Win32.PInvoke.EnableMenuItem(Windows.Win32.UI.WindowsAndMessaging.HMENU,System.UInt32,Windows.Win32.UI.WindowsAndMessaging.MENU_ITEM_FLAGS)">
            <summary>Enables, disables, or grays the specified menu item.</summary>
            <param name="hMenu">
            <para>Type: <b>HMENU</b> A handle to the menu.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-enablemenuitem#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <param name="uIDEnableItem">
            <para>Type: <b>UINT</b> The menu item to be enabled, disabled, or grayed, as determined by the <i>uEnable</i> parameter. This parameter specifies an item in a menu bar, menu, or submenu.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-enablemenuitem#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <param name="uEnable">Type: <b>UINT</b></param>
            <returns>
            <para>Type: <b>BOOL</b> The return value specifies the previous state of the menu item (it is either <b>MF_DISABLED</b>, <b>MF_ENABLED</b>, or <b>MF_GRAYED</b>). If the menu item does not exist, the return value is -1.</para>
            </returns>
            <remarks>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-enablemenuitem">Learn more about this API from docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="M:Windows.Win32.PInvoke.EndDeferWindowPos(System.IntPtr)">
            <summary>Simultaneously updates the position and size of one or more windows in a single screen-refreshing cycle.</summary>
            <param name="hWinPosInfo">
            <para>Type: <b>HDWP</b> A handle to a multiple-window – position structure that contains size and position information for one or more windows. This internal structure is returned by the <a href="https://docs.microsoft.com/windows/desktop/api/winuser/nf-winuser-begindeferwindowpos">BeginDeferWindowPos</a> function or by the most recent call to the <a href="https://docs.microsoft.com/windows/desktop/api/winuser/nf-winuser-deferwindowpos">DeferWindowPos</a> function.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-enddeferwindowpos#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <returns>
            <para>Type: <b>BOOL</b> If the function succeeds, the return value is nonzero. If the function fails, the return value is zero. To get extended error information, call <a href="/windows/desktop/api/errhandlingapi/nf-errhandlingapi-getlasterror">GetLastError</a>.</para>
            </returns>
            <remarks>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-enddeferwindowpos">Learn more about this API from docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="M:Windows.Win32.PInvoke.FindWindow(System.String,System.String)">
            <inheritdoc cref="M:Windows.Win32.PInvoke.FindWindow(Windows.Win32.Foundation.PCWSTR,Windows.Win32.Foundation.PCWSTR)"/>
        </member>
        <member name="M:Windows.Win32.PInvoke.FindWindow(Windows.Win32.Foundation.PCWSTR,Windows.Win32.Foundation.PCWSTR)">
            <summary>Retrieves a handle to the top-level window whose class name and window name match the specified strings. This function does not search child windows. This function does not perform a case-sensitive search.</summary>
            <param name="lpClassName">
            <para>Type: <b>LPCTSTR</b> The class name or a class atom created by a previous call to the <a href="https://docs.microsoft.com/windows/desktop/api/winuser/nf-winuser-registerclassa">RegisterClass</a> or <a href="https://docs.microsoft.com/windows/desktop/api/winuser/nf-winuser-registerclassexa">RegisterClassEx</a> function. The atom must be in the low-order word of <i>lpClassName</i>; the high-order word must be zero. If <i>lpClassName</i> points to a string, it specifies the window class name. The class name can be any name registered with <a href="https://docs.microsoft.com/windows/desktop/api/winuser/nf-winuser-registerclassa">RegisterClass</a> or <a href="https://docs.microsoft.com/windows/desktop/api/winuser/nf-winuser-registerclassexa">RegisterClassEx</a>, or any of the predefined control-class names. If <i>lpClassName</i> is <b>NULL</b>, it finds any window whose title matches the <i>lpWindowName</i> parameter.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-findwindoww#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <param name="lpWindowName">
            <para>Type: <b>LPCTSTR</b> The window name (the window's title). If this parameter is <b>NULL</b>, all window names match.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-findwindoww#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <returns>
            <para>Type: <b>HWND</b> If the function succeeds, the return value is a handle to the window that has the specified class name and window name. If the function fails, the return value is <b>NULL</b>. To get extended error information, call <a href="/windows/desktop/api/errhandlingapi/nf-errhandlingapi-getlasterror">GetLastError</a>.</para>
            </returns>
            <remarks>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-findwindoww">Learn more about this API from docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="M:Windows.Win32.PInvoke.GetClientRect(Windows.Win32.Foundation.HWND,Windows.Win32.Foundation.RECT*)">
            <summary>Retrieves the coordinates of a window's client area.</summary>
            <param name="hWnd">
            <para>Type: <b>HWND</b> A handle to the window whose client coordinates are to be retrieved.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-getclientrect#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <param name="lpRect">
            <para>Type: <b>LPRECT</b> A pointer to a <a href="https://docs.microsoft.com/windows/desktop/api/windef/ns-windef-rect">RECT</a> structure that receives the client coordinates. The <b>left</b> and <b>top</b> members are zero. The <b>right</b> and <b>bottom</b> members contain the width and height of the window.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-getclientrect#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <returns>
            <para>Type: <b>BOOL</b> If the function succeeds, the return value is nonzero. If the function fails, the return value is zero. To get extended error information, call <a href="/windows/desktop/api/errhandlingapi/nf-errhandlingapi-getlasterror">GetLastError</a>.</para>
            </returns>
            <remarks>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-getclientrect">Learn more about this API from docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="M:Windows.Win32.PInvoke.GetCursorPos(System.Drawing.Point*)">
            <summary>Retrieves the position of the mouse cursor, in screen coordinates.</summary>
            <param name="lpPoint">
            <para>Type: <b>LPPOINT</b> A pointer to a <a href="https://docs.microsoft.com/previous-versions/dd162805(v=vs.85)">POINT</a> structure that receives the screen coordinates of the cursor.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-getcursorpos#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <returns>
            <para>Type: <b>BOOL</b> Returns nonzero if successful or zero otherwise. To get extended error information, call <a href="/windows/desktop/api/errhandlingapi/nf-errhandlingapi-getlasterror">GetLastError</a>.</para>
            </returns>
            <remarks>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-getcursorpos">Learn more about this API from docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="M:Windows.Win32.PInvoke.GetDC(Windows.Win32.Foundation.HWND)">
            <summary>The GetDC function retrieves a handle to a device context (DC) for the client area of a specified window or for the entire screen.</summary>
            <param name="hWnd">A handle to the window whose DC is to be retrieved. If this value is <b>NULL</b>, <b>GetDC</b> retrieves the DC for the entire screen.</param>
            <returns>
            <para>If the function succeeds, the return value is a handle to the DC for the specified window's client area. If the function fails, the return value is <b>NULL</b>.</para>
            </returns>
            <remarks>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-getdc">Learn more about this API from docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="M:Windows.Win32.PInvoke.GetKeyState(System.Int32)">
            <summary>Retrieves the status of the specified virtual key. The status specifies whether the key is up, down, or toggled (on, off�alternating each time the key is pressed).</summary>
            <param name="nVirtKey">
            <para>Type: <b>int</b> A virtual key. If the desired virtual key is a letter or digit (A through Z, a through z, or 0 through 9), <i>nVirtKey</i> must be set to the ASCII value of that character. For other keys, it must be a virtual-key code. If a non-English keyboard layout is used, virtual keys with values in the range ASCII A through Z and 0 through 9 are used to specify most of the character keys. For example, for the German keyboard layout, the virtual key of value ASCII O (0x4F) refers to the "o" key, whereas VK_OEM_1 refers to the "o with umlaut" key.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-getkeystate#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <returns>
            <para>Type: <b>SHORT</b> The return value specifies the status of the specified virtual key, as follows: </para>
            <para>This doc was truncated.</para>
            </returns>
            <remarks>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-getkeystate">Learn more about this API from docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="M:Windows.Win32.PInvoke.GetMonitorInfo(Windows.Win32.Graphics.Gdi.HMONITOR,Windows.Win32.Graphics.Gdi.MONITORINFO*)">
            <summary>The GetMonitorInfo function retrieves information about a display monitor.</summary>
            <param name="hMonitor">A handle to the display monitor of interest.</param>
            <param name="lpmi">
            <para>A pointer to a <a href="https://docs.microsoft.com/windows/desktop/api/winuser/ns-winuser-monitorinfo">MONITORINFO</a> or <a href="https://docs.microsoft.com/windows/desktop/api/winuser/ns-winuser-monitorinfoexa">MONITORINFOEX</a> structure that receives information about the specified display monitor. You must set the <b>cbSize</b> member of the structure to sizeof(MONITORINFO) or sizeof(MONITORINFOEX) before calling the <b>GetMonitorInfo</b> function. Doing so lets the function determine the type of structure you are passing to it. The <a href="https://docs.microsoft.com/windows/desktop/api/winuser/ns-winuser-monitorinfoexa">MONITORINFOEX</a> structure is a superset of the <a href="https://docs.microsoft.com/windows/desktop/api/winuser/ns-winuser-monitorinfo">MONITORINFO</a> structure. It has one additional member: a string that contains a name for the display monitor. Most applications have no use for a display monitor name, and so can save some bytes by using a <b>MONITORINFO</b> structure.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-getmonitorinfow#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <returns>
            <para>If the function succeeds, the return value is nonzero. If the function fails, the return value is zero.</para>
            </returns>
            <remarks>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-getmonitorinfow">Learn more about this API from docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="M:Windows.Win32.PInvoke.GetSystemMenu_SafeHandle(Windows.Win32.Foundation.HWND,Windows.Win32.Foundation.BOOL)">
            <inheritdoc cref="M:Windows.Win32.PInvoke.GetSystemMenu(Windows.Win32.Foundation.HWND,Windows.Win32.Foundation.BOOL)"/>
        </member>
        <member name="M:Windows.Win32.PInvoke.GetSystemMenu(Windows.Win32.Foundation.HWND,Windows.Win32.Foundation.BOOL)">
            <summary>Enables the application to access the window menu (also known as the system menu or the control menu) for copying and modifying.</summary>
            <param name="hWnd">
            <para>Type: <b>HWND</b> A handle to the window that will own a copy of the window menu.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-getsystemmenu#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <param name="bRevert">
            <para>Type: <b>BOOL</b> The action to be taken. If this parameter is <b>FALSE</b>, <b>GetSystemMenu</b> returns a handle to the copy of the window menu currently in use. The copy is initially identical to the window menu, but it can be modified. If this parameter is <b>TRUE</b>, <b>GetSystemMenu</b> resets the window menu back to the default state. The previous window menu, if any, is destroyed.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-getsystemmenu#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <returns>
            <para>Type: <b>HMENU</b> If the <i>bRevert</i> parameter is <b>FALSE</b>, the return value is a handle to a copy of the window menu. If the <i>bRevert</i> parameter is <b>TRUE</b>, the return value is <b>NULL</b>.</para>
            </returns>
            <remarks>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-getsystemmenu">Learn more about this API from docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="M:Windows.Win32.PInvoke.GetSystemMetrics(Windows.Win32.UI.WindowsAndMessaging.SYSTEM_METRICS_INDEX)">
            <summary>Retrieves the specified system metric or system configuration setting.</summary>
            <param name="nIndex">Type: <b>int</b></param>
            <returns>
            <para>Type: <b>int</b> If the function succeeds, the return value is the requested system metric or configuration setting. If the function fails, the return value is 0. <a href="/windows/desktop/api/errhandlingapi/nf-errhandlingapi-getlasterror">GetLastError</a> does not provide extended error information.</para>
            </returns>
            <remarks>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-getsystemmetrics">Learn more about this API from docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="M:Windows.Win32.PInvoke.GetWindowPlacement(Windows.Win32.Foundation.HWND,Windows.Win32.UI.WindowsAndMessaging.WINDOWPLACEMENT*)">
            <summary>Retrieves the show state and the restored, minimized, and maximized positions of the specified window.</summary>
            <param name="hWnd">
            <para>Type: <b>HWND</b> A handle to the window.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-getwindowplacement#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <param name="lpwndpl">
            <para>Type: <b><a href="https://docs.microsoft.com/windows/desktop/api/winuser/ns-winuser-windowplacement">WINDOWPLACEMENT</a>*</b> A pointer to the <a href="https://docs.microsoft.com/windows/desktop/api/winuser/ns-winuser-windowplacement">WINDOWPLACEMENT</a> structure that receives the show state and position information. Before calling <b>GetWindowPlacement</b>, set the <b>length</b> member to <c>sizeof(WINDOWPLACEMENT)</c>. <b>GetWindowPlacement</b> fails if <i>lpwndpl</i>-&gt; <i>length</i> is not set correctly.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-getwindowplacement#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <returns>
            <para>Type: <b>BOOL</b> If the function succeeds, the return value is nonzero. If the function fails, the return value is zero. To get extended error information, call <a href="/windows/desktop/api/errhandlingapi/nf-errhandlingapi-getlasterror">GetLastError</a>.</para>
            </returns>
            <remarks>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-getwindowplacement">Learn more about this API from docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="M:Windows.Win32.PInvoke.GetWindowRect(Windows.Win32.Foundation.HWND,Windows.Win32.Foundation.RECT*)">
            <summary>Retrieves the dimensions of the bounding rectangle of the specified window. The dimensions are given in screen coordinates that are relative to the upper-left corner of the screen.</summary>
            <param name="hWnd">
            <para>Type: <b>HWND</b> A handle to the window.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-getwindowrect#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <param name="lpRect">
            <para>Type: <b>LPRECT</b> A pointer to a  <a href="https://docs.microsoft.com/windows/desktop/api/windef/ns-windef-rect">RECT</a> structure that receives the screen coordinates of the upper-left and lower-right corners of the window.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-getwindowrect#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <returns>
            <para>Type: <b>BOOL</b> If the function succeeds, the return value is nonzero. If the function fails, the return value is zero. To get extended error information, call <a href="/windows/desktop/api/errhandlingapi/nf-errhandlingapi-getlasterror">GetLastError</a>.</para>
            </returns>
            <remarks>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-getwindowrect">Learn more about this API from docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="M:Windows.Win32.PInvoke.IntersectRect(Windows.Win32.Foundation.RECT*,Windows.Win32.Foundation.RECT*,Windows.Win32.Foundation.RECT*)">
            <summary>The IntersectRect function calculates the intersection of two source rectangles and places the coordinates of the intersection rectangle into the destination rectangle.</summary>
            <param name="lprcDst">A pointer to the <a href="https://docs.microsoft.com/windows/desktop/api/windef/ns-windef-rect">RECT</a> structure that is to receive the intersection of the rectangles pointed to by the <i>lprcSrc1</i> and <i>lprcSrc2</i> parameters. This parameter cannot be <b>NULL</b>.</param>
            <param name="lprcSrc1">A pointer to the <a href="https://docs.microsoft.com/windows/desktop/api/windef/ns-windef-rect">RECT</a> structure that contains the first source rectangle.</param>
            <param name="lprcSrc2">A pointer to the <a href="https://docs.microsoft.com/windows/desktop/api/windef/ns-windef-rect">RECT</a> structure that contains the second source rectangle.</param>
            <returns>
            <para>If the rectangles intersect, the return value is nonzero. If the rectangles do not intersect, the return value is zero.</para>
            </returns>
            <remarks>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-intersectrect">Learn more about this API from docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="M:Windows.Win32.PInvoke.IsIconic(Windows.Win32.Foundation.HWND)">
            <summary>Determines whether the specified window is minimized (iconic).</summary>
            <param name="hWnd">
            <para>Type: <b>HWND</b> A handle to the window to be tested.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-isiconic#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <returns>
            <para>Type: <b>BOOL</b> If the window is iconic, the return value is nonzero. If the window is not iconic, the return value is zero.</para>
            </returns>
            <remarks>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-isiconic">Learn more about this API from docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="M:Windows.Win32.PInvoke.IsWindow(Windows.Win32.Foundation.HWND)">
            <summary>Determines whether the specified window handle identifies an existing window.</summary>
            <param name="hWnd">
            <para>Type: <b>HWND</b> A handle to the window to be tested.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-iswindow#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <returns>
            <para>Type: <b>BOOL</b> If the window handle identifies an existing window, the return value is nonzero. If the window handle does not identify an existing window, the return value is zero.</para>
            </returns>
            <remarks>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-iswindow">Learn more about this API from docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="M:Windows.Win32.PInvoke.IsWindowVisible(Windows.Win32.Foundation.HWND)">
            <summary>Determines the visibility state of the specified window.</summary>
            <param name="hWnd">
            <para>Type: <b>HWND</b> A handle to the window to be tested.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-iswindowvisible#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <returns>
            <para>Type: <b>BOOL</b> If the specified window, its parent window, its parent's parent window, and so forth, have the <b>WS_VISIBLE</b> style, the return value is nonzero. Otherwise, the return value is zero. Because the return value specifies whether the window has the <b>WS_VISIBLE</b> style, it may be nonzero even if the window is totally obscured by other windows.</para>
            </returns>
            <remarks>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-iswindowvisible">Learn more about this API from docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="M:Windows.Win32.PInvoke.IsZoomed(Windows.Win32.Foundation.HWND)">
            <summary>Determines whether a window is maximized.</summary>
            <param name="hWnd">
            <para>Type: <b>HWND</b> A handle to the window to be tested.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-iszoomed#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <returns>
            <para>Type: <b>BOOL</b> If the window is zoomed, the return value is nonzero. If the window is not zoomed, the return value is zero.</para>
            </returns>
            <remarks>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-iszoomed">Learn more about this API from docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="M:Windows.Win32.PInvoke.MonitorFromPoint(System.Drawing.Point,Windows.Win32.Graphics.Gdi.MONITOR_FROM_FLAGS)">
            <summary>The MonitorFromPoint function retrieves a handle to the display monitor that contains a specified point.</summary>
            <param name="pt">A <a href="https://docs.microsoft.com/previous-versions/dd162805(v=vs.85)">POINT</a> structure that specifies the point of interest in virtual-screen coordinates.</param>
            <param name="dwFlags">Determines the function's return value if the point is not contained within any display monitor.</param>
            <returns>
            <para>If the point is contained by a display monitor, the return value is an <b>HMONITOR</b> handle to that display monitor. If the point is not contained by a display monitor, the return value depends on the value of <i>dwFlags</i>.</para>
            </returns>
            <remarks>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-monitorfrompoint">Learn more about this API from docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="M:Windows.Win32.PInvoke.MonitorFromRect(Windows.Win32.Foundation.RECT*,Windows.Win32.Graphics.Gdi.MONITOR_FROM_FLAGS)">
            <summary>The MonitorFromRect function retrieves a handle to the display monitor that has the largest area of intersection with a specified rectangle.</summary>
            <param name="lprc">A pointer to a <a href="https://docs.microsoft.com/windows/desktop/api/windef/ns-windef-rect">RECT</a> structure that specifies the rectangle of interest in virtual-screen coordinates.</param>
            <param name="dwFlags">Determines the function's return value if the rectangle does not intersect any display monitor.</param>
            <returns>
            <para>If the rectangle intersects one or more display monitor rectangles, the return value is an <b>HMONITOR</b> handle to the display monitor that has the largest area of intersection with the rectangle. If the rectangle does not intersect a display monitor, the return value depends on the value of <i>dwFlags</i>.</para>
            </returns>
            <remarks>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-monitorfromrect">Learn more about this API from docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="M:Windows.Win32.PInvoke.MonitorFromWindow(Windows.Win32.Foundation.HWND,Windows.Win32.Graphics.Gdi.MONITOR_FROM_FLAGS)">
            <summary>The MonitorFromWindow function retrieves a handle to the display monitor that has the largest area of intersection with the bounding rectangle of a specified window.</summary>
            <param name="hwnd">A handle to the window of interest.</param>
            <param name="dwFlags">Determines the function's return value if the window does not intersect any display monitor.</param>
            <returns>
            <para>If the window intersects one or more display monitor rectangles, the return value is an <b>HMONITOR</b> handle to the display monitor that has the largest area of intersection with the window. If the window does not intersect a display monitor, the return value depends on the value of <i>dwFlags</i>.</para>
            </returns>
            <remarks>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-monitorfromwindow">Learn more about this API from docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="M:Windows.Win32.PInvoke.PostMessage(Windows.Win32.Foundation.HWND,System.UInt32,Windows.Win32.Foundation.WPARAM,Windows.Win32.Foundation.LPARAM)">
            <summary>Places (posts) a message in the message queue associated with the thread that created the specified window and returns without waiting for the thread to process the message.</summary>
            <param name="hWnd">
            <para>Type: <b>HWND</b> A handle to the window whose window procedure is to receive the message. The following values have special meanings. </para>
            <para>This doc was truncated.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-postmessagew#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <param name="Msg">
            <para>Type: <b>UINT</b> The message to be posted. For lists of the system-provided messages, see <a href="https://docs.microsoft.com/windows/desktop/winmsg/about-messages-and-message-queues">System-Defined Messages</a>.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-postmessagew#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <param name="wParam">
            <para>Type: <b>WPARAM</b> Additional message-specific information.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-postmessagew#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <param name="lParam">
            <para>Type: <b>LPARAM</b> Additional message-specific information.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-postmessagew#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <returns>
            <para>Type: <b>BOOL</b> If the function succeeds, the return value is nonzero. If the function fails, the return value is zero. To get extended error information, call <a href="/windows/desktop/api/errhandlingapi/nf-errhandlingapi-getlasterror">GetLastError</a>. <b>GetLastError</b> returns <b>ERROR_NOT_ENOUGH_QUOTA</b> when the limit is hit.</para>
            </returns>
            <remarks>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-postmessagew">Learn more about this API from docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="M:Windows.Win32.PInvoke.RedrawWindow(Windows.Win32.Foundation.HWND,Windows.Win32.Foundation.RECT*,System.Runtime.InteropServices.SafeHandle,Windows.Win32.Graphics.Gdi.REDRAW_WINDOW_FLAGS)">
            <inheritdoc cref="M:Windows.Win32.PInvoke.RedrawWindow(Windows.Win32.Foundation.HWND,Windows.Win32.Foundation.RECT*,Windows.Win32.Graphics.Gdi.HRGN,Windows.Win32.Graphics.Gdi.REDRAW_WINDOW_FLAGS)"/>
        </member>
        <member name="M:Windows.Win32.PInvoke.RedrawWindow(Windows.Win32.Foundation.HWND,Windows.Win32.Foundation.RECT*,Windows.Win32.Graphics.Gdi.HRGN,Windows.Win32.Graphics.Gdi.REDRAW_WINDOW_FLAGS)">
            <summary>The RedrawWindow function updates the specified rectangle or region in a window's client area.</summary>
            <param name="hWnd">A handle to the window to be redrawn. If this parameter is <b>NULL</b>, the desktop window is updated.</param>
            <param name="lprcUpdate">A pointer to a <a href="https://docs.microsoft.com/windows/desktop/api/windef/ns-windef-rect">RECT</a> structure containing the coordinates, in device units, of the update rectangle. This parameter is ignored if the <i>hrgnUpdate</i> parameter identifies a region.</param>
            <param name="hrgnUpdate">A handle to the update region. If both the <i>hrgnUpdate</i> and <i>lprcUpdate</i> parameters are <b>NULL</b>, the entire client area is added to the update region.</param>
            <param name="flags">
            <para>One or more redraw flags. This parameter can be used to invalidate or validate a window, control repainting, and control which windows are affected by <b>RedrawWindow</b>. The following flags are used to invalidate the window. </para>
            <para>This doc was truncated.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-redrawwindow#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <returns>
            <para>If the function succeeds, the return value is nonzero. If the function fails, the return value is zero.</para>
            </returns>
            <remarks>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-redrawwindow">Learn more about this API from docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="M:Windows.Win32.PInvoke.DestroyIcon(Windows.Win32.UI.WindowsAndMessaging.HICON)">
            <summary>Destroys an icon and frees any memory the icon occupied.</summary>
            <param name="hIcon">
            <para>Type: <b>HICON</b> A handle to the icon to be destroyed. The icon must not be in use.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-destroyicon#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <returns>
            <para>Type: <b>BOOL</b> If the function succeeds, the return value is nonzero. If the function fails, the return value is zero. To get extended error information, call <a href="/windows/desktop/api/errhandlingapi/nf-errhandlingapi-getlasterror">GetLastError</a>.</para>
            </returns>
            <remarks>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-destroyicon">Learn more about this API from docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="M:Windows.Win32.PInvoke.DestroyCursor(Windows.Win32.UI.WindowsAndMessaging.HCURSOR)">
            <summary>Destroys a cursor and frees any memory the cursor occupied. Do not use this function to destroy a shared cursor.</summary>
            <param name="hCursor">
            <para>Type: <b>HCURSOR</b> A handle to the cursor to be destroyed. The cursor must not be in use.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-destroycursor#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <returns>
            <para>Type: <b>BOOL</b> If the function succeeds, the return value is nonzero. If the function fails, the return value is zero. To get extended error information, call <a href="/windows/desktop/api/errhandlingapi/nf-errhandlingapi-getlasterror">GetLastError</a>.</para>
            </returns>
            <remarks>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-destroycursor">Learn more about this API from docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="M:Windows.Win32.PInvoke.RegisterClassEx(Windows.Win32.UI.WindowsAndMessaging.WNDCLASSEXW@)">
            <summary>Registers a window class for subsequent use in calls to the CreateWindow or CreateWindowEx function.</summary>
            <returns>
            <para>Type: <b>ATOM</b> If the function succeeds, the return value is a class atom that uniquely identifies the class being registered. This atom can only be used by the <a href="/windows/desktop/api/winuser/nf-winuser-createwindowa">CreateWindow</a>, <a href="/windows/desktop/api/winuser/nf-winuser-createwindowexa">CreateWindowEx</a>, <a href="/windows/desktop/api/winuser/nf-winuser-getclassinfoa">GetClassInfo</a>, <a href="/windows/desktop/api/winuser/nf-winuser-getclassinfoexa">GetClassInfoEx</a>, <a href="/windows/desktop/api/winuser/nf-winuser-findwindowa">FindWindow</a>, <a href="/windows/desktop/api/winuser/nf-winuser-findwindowexa">FindWindowEx</a>, and <a href="/windows/desktop/api/winuser/nf-winuser-unregisterclassa">UnregisterClass</a> functions and the <b>IActiveIMMap::FilterClientWindows</b> method. If the function fails, the return value is zero. To get extended error information, call <a href="/windows/desktop/api/errhandlingapi/nf-errhandlingapi-getlasterror">GetLastError</a>.</para>
            </returns>
            <remarks>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-registerclassexw">Learn more about this API from docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="M:Windows.Win32.PInvoke.ScreenToClient(Windows.Win32.Foundation.HWND,System.Drawing.Point*)">
            <summary>The ScreenToClient function converts the screen coordinates of a specified point on the screen to client-area coordinates.</summary>
            <param name="hWnd">A handle to the window whose client area will be used for the conversion.</param>
            <param name="lpPoint">A pointer to a <a href="https://docs.microsoft.com/previous-versions/dd162805(v=vs.85)">POINT</a> structure that specifies the screen coordinates to be converted.</param>
            <returns>
            <para>If the function succeeds, the return value is nonzero. If the function fails, the return value is zero.</para>
            </returns>
            <remarks>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-screentoclient">Learn more about this API from docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="M:Windows.Win32.PInvoke.SendMessage(Windows.Win32.Foundation.HWND,System.UInt32,Windows.Win32.Foundation.WPARAM,Windows.Win32.Foundation.LPARAM)">
            <summary>Sends the specified message to a window or windows. The SendMessage function calls the window procedure for the specified window and does not return until the window procedure has processed the message.</summary>
            <param name="hWnd">
            <para>Type: <b>HWND</b> A handle to the window whose window procedure will receive the message. If this parameter is <b>HWND_BROADCAST</b> ((HWND)0xffff), the message is sent to all top-level windows in the system, including disabled or invisible unowned windows, overlapped windows, and pop-up windows; but the message is not sent to child windows. Message sending is subject to UIPI. The thread of a process can send messages only to message queues of threads in processes of lesser or equal integrity level.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-sendmessagew#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <param name="Msg">
            <para>Type: <b>UINT</b> The message to be sent. For lists of the system-provided messages, see <a href="https://docs.microsoft.com/windows/desktop/winmsg/about-messages-and-message-queues">System-Defined Messages</a>.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-sendmessagew#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <param name="wParam">
            <para>Type: <b>WPARAM</b> Additional message-specific information.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-sendmessagew#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <param name="lParam">
            <para>Type: <b>LPARAM</b> Additional message-specific information.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-sendmessagew#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <returns>
            <para>Type: <b>LRESULT</b> The return value specifies the result of the message processing; it depends on the message sent.</para>
            </returns>
            <remarks>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-sendmessagew">Learn more about this API from docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="M:Windows.Win32.PInvoke.SetFocus(Windows.Win32.Foundation.HWND)">
            <summary>Sets the keyboard focus to the specified window. The window must be attached to the calling thread's message queue.</summary>
            <param name="hWnd">
            <para>Type: **HWND** A handle to the window that will receive the keyboard input. If this parameter is NULL, keystrokes are ignored.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-setfocus#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <returns>
            <para>Type: **HWND** If the function succeeds, the return value is the handle to the window that previously had the keyboard focus. If the *hWnd* parameter is invalid or the window is not attached to the calling thread's message queue, the return value is NULL. To get extended error information, call [GetLastError function](../errhandlingapi/nf-errhandlingapi-getlasterror.md). Extended error ERROR_INVALID_PARAMETER (0x57) means that window is in disabled state.</para>
            </returns>
            <remarks>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-setfocus">Learn more about this API from docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="M:Windows.Win32.PInvoke.SetWindowPos(Windows.Win32.Foundation.HWND,Windows.Win32.Foundation.HWND,System.Int32,System.Int32,System.Int32,System.Int32,Windows.Win32.UI.WindowsAndMessaging.SET_WINDOW_POS_FLAGS)">
            <summary>Changes the size, position, and Z order of a child, pop-up, or top-level window. These windows are ordered according to their appearance on the screen. The topmost window receives the highest rank and is the first window in the Z order.</summary>
            <param name="hWnd">
            <para>Type: <b>HWND</b> A handle to the window.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-setwindowpos#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <param name="hWndInsertAfter">Type: <b>HWND</b></param>
            <param name="X">
            <para>Type: <b>int</b> The new position of the left side of the window, in client coordinates.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-setwindowpos#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <param name="Y">
            <para>Type: <b>int</b> The new position of the top of the window, in client coordinates.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-setwindowpos#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <param name="cx">
            <para>Type: <b>int</b> The new width of the window, in pixels.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-setwindowpos#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <param name="cy">
            <para>Type: <b>int</b> The new height of the window, in pixels.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-setwindowpos#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <param name="uFlags">Type: <b>UINT</b></param>
            <returns>
            <para>Type: <b>BOOL</b> If the function succeeds, the return value is nonzero. If the function fails, the return value is zero. To get extended error information, call <a href="/windows/desktop/api/errhandlingapi/nf-errhandlingapi-getlasterror">GetLastError</a>.</para>
            </returns>
            <remarks>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-setwindowpos">Learn more about this API from docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="M:Windows.Win32.PInvoke.TrackMouseEvent(Windows.Win32.UI.Input.KeyboardAndMouse.TRACKMOUSEEVENT*)">
            <summary>Posts messages when the mouse pointer leaves a window or hovers over a window for a specified amount of time.</summary>
            <param name="lpEventTrack">
            <para>Type: <b>LPTRACKMOUSEEVENT</b> A pointer to a <a href="https://docs.microsoft.com/windows/desktop/api/winuser/ns-winuser-trackmouseevent">TRACKMOUSEEVENT</a> structure that contains tracking information.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-trackmouseevent#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <returns>
            <para>Type: <b>BOOL</b> If the function succeeds, the return value is nonzero . If the function fails, return value is zero. To get extended error information, call <a href="/windows/desktop/api/errhandlingapi/nf-errhandlingapi-getlasterror">GetLastError</a>.</para>
            </returns>
            <remarks>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-trackmouseevent">Learn more about this API from docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="M:Windows.Win32.PInvoke.TrackPopupMenuEx(System.Runtime.InteropServices.SafeHandle,System.UInt32,System.Int32,System.Int32,Windows.Win32.Foundation.HWND,Windows.Win32.UI.WindowsAndMessaging.TPMPARAMS*)">
            <inheritdoc cref="M:Windows.Win32.PInvoke.TrackPopupMenuEx(Windows.Win32.UI.WindowsAndMessaging.HMENU,System.UInt32,System.Int32,System.Int32,Windows.Win32.Foundation.HWND,Windows.Win32.UI.WindowsAndMessaging.TPMPARAMS*)"/>
        </member>
        <member name="M:Windows.Win32.PInvoke.TrackPopupMenuEx(Windows.Win32.UI.WindowsAndMessaging.HMENU,System.UInt32,System.Int32,System.Int32,Windows.Win32.Foundation.HWND,Windows.Win32.UI.WindowsAndMessaging.TPMPARAMS*)">
            <summary>Displays a shortcut menu at the specified location and tracks the selection of items on the shortcut menu. The shortcut menu can appear anywhere on the screen.</summary>
            <param name="hMenu">
            <para>Type: <b>HMENU</b> A handle to the shortcut menu to be displayed. This handle can be obtained by calling the <a href="https://docs.microsoft.com/windows/desktop/api/winuser/nf-winuser-createpopupmenu">CreatePopupMenu</a> function to create a new shortcut menu or by calling the <a href="https://docs.microsoft.com/windows/desktop/api/winuser/nf-winuser-getsubmenu">GetSubMenu</a> function to retrieve a handle to a submenu associated with an existing menu item.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-trackpopupmenuex#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <param name="uFlags">
            <para>Type: <b>UINT</b> Specifies function options.</para>
            <para>Use one of the following flags to specify how the function positions the shortcut menu horizontally.</para>
            <para></para>
            <para>This doc was truncated.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-trackpopupmenuex#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <param name="x">
            <para>Type: <b>int</b> The horizontal location of the shortcut menu, in screen coordinates.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-trackpopupmenuex#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <param name="y">
            <para>Type: <b>int</b> The vertical location of the shortcut menu, in screen coordinates.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-trackpopupmenuex#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <param name="hwnd">
            <para>Type: <b>HWND</b> A handle to the window that owns the shortcut menu. This window receives all messages from the menu. The window does not receive a <a href="https://docs.microsoft.com/windows/desktop/menurc/wm-command">WM_COMMAND</a> message from the menu until the function returns. If you specify TPM_NONOTIFY in the <i>fuFlags</i> parameter, the function does not send messages to the window identified by <i>hwnd</i>. However, you must still pass a window handle in <i>hwnd</i>. It can be any window handle from your application.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-trackpopupmenuex#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <param name="lptpm">
            <para>Type: <b>LPTPMPARAMS</b> A pointer to a <a href="https://docs.microsoft.com/windows/desktop/api/winuser/ns-winuser-tpmparams">TPMPARAMS</a> structure that specifies an area of the screen the menu should not overlap. This parameter can be <b>NULL</b>.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-trackpopupmenuex#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <returns>
            <para>Type: <b>BOOL</b> If you specify <b>TPM_RETURNCMD</b> in the <i>fuFlags</i> parameter, the return value is the menu-item identifier of the item that the user selected. If the user cancels the menu without making a selection, or if an error occurs, the return value is zero. If you do not specify <b>TPM_RETURNCMD</b> in the <i>fuFlags</i> parameter, the return value is nonzero if the function succeeds and zero if it fails. To get extended error information, call <a href="/windows/desktop/api/errhandlingapi/nf-errhandlingapi-getlasterror">GetLastError</a>.</para>
            </returns>
            <remarks>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-trackpopupmenuex">Learn more about this API from docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="M:Windows.Win32.PInvoke.UnregisterClass(System.String,System.Runtime.InteropServices.SafeHandle)">
            <inheritdoc cref="M:Windows.Win32.PInvoke.UnregisterClass(Windows.Win32.Foundation.PCWSTR,Windows.Win32.Foundation.HINSTANCE)"/>
        </member>
        <member name="M:Windows.Win32.PInvoke.UnregisterClass(Windows.Win32.Foundation.PCWSTR,Windows.Win32.Foundation.HINSTANCE)">
            <summary>Unregisters a window class, freeing the memory required for the class.</summary>
            <param name="lpClassName">
            <para>Type: <b>LPCTSTR</b> A null-terminated string or a class atom. If <i>lpClassName</i> is a string, it specifies the window class name. This class name must have been registered by a previous call to the <a href="https://docs.microsoft.com/windows/desktop/api/winuser/nf-winuser-registerclassa">RegisterClass</a> or <a href="https://docs.microsoft.com/windows/desktop/api/winuser/nf-winuser-registerclassexa">RegisterClassEx</a> function. System classes, such as dialog box controls, cannot be unregistered. If this parameter is an atom, it must be a class atom created by a previous call to the <b>RegisterClass</b> or <b>RegisterClassEx</b> function. The atom must be in the low-order word of <i>lpClassName</i>; the high-order word must be zero.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-unregisterclassw#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <param name="hInstance">
            <para>Type: <b>HINSTANCE</b> A handle to the instance of the module that created the class.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-unregisterclassw#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <returns>
            <para>Type: <b>BOOL</b> If the function succeeds, the return value is nonzero. If the class could not be found or if a window still exists that was created with the class, the return value is zero. To get extended error information, call <a href="/windows/desktop/api/errhandlingapi/nf-errhandlingapi-getlasterror">GetLastError</a>.</para>
            </returns>
            <remarks>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-unregisterclassw">Learn more about this API from docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="M:Windows.Win32.PInvoke.UpdateLayeredWindow(Windows.Win32.Foundation.HWND,System.Runtime.InteropServices.SafeHandle,System.Drawing.Point*,Windows.Win32.Foundation.SIZE*,System.Runtime.InteropServices.SafeHandle,System.Drawing.Point*,Windows.Win32.Foundation.COLORREF,Windows.Win32.Graphics.Gdi.BLENDFUNCTION*,Windows.Win32.UI.WindowsAndMessaging.UPDATE_LAYERED_WINDOW_FLAGS)">
            <inheritdoc cref="M:Windows.Win32.PInvoke.UpdateLayeredWindow(Windows.Win32.Foundation.HWND,Windows.Win32.Graphics.Gdi.HDC,System.Drawing.Point*,Windows.Win32.Foundation.SIZE*,Windows.Win32.Graphics.Gdi.HDC,System.Drawing.Point*,Windows.Win32.Foundation.COLORREF,Windows.Win32.Graphics.Gdi.BLENDFUNCTION*,Windows.Win32.UI.WindowsAndMessaging.UPDATE_LAYERED_WINDOW_FLAGS)"/>
        </member>
        <member name="M:Windows.Win32.PInvoke.UpdateLayeredWindow(Windows.Win32.Foundation.HWND,Windows.Win32.Graphics.Gdi.HDC,System.Drawing.Point*,Windows.Win32.Foundation.SIZE*,Windows.Win32.Graphics.Gdi.HDC,System.Drawing.Point*,Windows.Win32.Foundation.COLORREF,Windows.Win32.Graphics.Gdi.BLENDFUNCTION*,Windows.Win32.UI.WindowsAndMessaging.UPDATE_LAYERED_WINDOW_FLAGS)">
            <summary>Updates the position, size, shape, content, and translucency of a layered window.</summary>
            <param name="hWnd">
            <para>Type: <b>HWND</b> A handle to a layered window. A layered window is created by specifying <b>WS_EX_LAYERED</b> when creating the window with the <a href="https://docs.microsoft.com/windows/desktop/api/winuser/nf-winuser-createwindowexa">CreateWindowEx</a> function. <b>Windows 8:  </b>The <b>WS_EX_LAYERED</b> style is supported for top-level windows and child windows. Previous Windows versions support <b>WS_EX_LAYERED</b> only for top-level windows.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-updatelayeredwindow#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <param name="hdcDst">
            <para>Type: <b>HDC</b> A handle to a DC for the screen. This handle is obtained by specifying <b>NULL</b> when calling the <a href="https://docs.microsoft.com/windows/win32/api/winuser/nf-winuser-getdc">GetDC</a> function. It is used for palette color matching when the window contents are updated. If <i>hdcDst</i> is <b>NULL</b>, the default palette will be used. If <i>hdcSrc</i> is <b>NULL</b>, <i>hdcDst</i> must be <b>NULL</b>.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-updatelayeredwindow#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <param name="pptDst">
            <para>Type: <b><a href="https://docs.microsoft.com/previous-versions/dd162805(v=vs.85)">POINT</a>*</b> A pointer to a structure that specifies the new screen position of the layered window. If the current position is not changing, <i>pptDst</i> can be <b>NULL</b>.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-updatelayeredwindow#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <param name="psize">
            <para>Type: <b><a href="https://docs.microsoft.com/previous-versions/dd145106(v=vs.85)">SIZE</a>*</b> A pointer to a structure that specifies the new size of the layered window. If the size of the window is not changing, <i>psize</i> can be <b>NULL</b>. If <i>hdcSrc</i> is <b>NULL</b>, <i>psize</i> must be <b>NULL</b>.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-updatelayeredwindow#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <param name="hdcSrc">
            <para>Type: <b>HDC</b> A handle to a DC for the surface that defines the layered window. This handle can be obtained by calling the <a href="https://docs.microsoft.com/windows/desktop/api/wingdi/nf-wingdi-createcompatibledc">CreateCompatibleDC</a> function. If the shape and visual context of the window are not changing, <i>hdcSrc</i> can be <b>NULL</b>.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-updatelayeredwindow#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <param name="pptSrc">
            <para>Type: <b><a href="https://docs.microsoft.com/previous-versions/dd162805(v=vs.85)">POINT</a>*</b> A pointer to a structure that specifies the location of the layer in the device context. If <i>hdcSrc</i> is <b>NULL</b>, <i>pptSrc</i> should be <b>NULL</b>.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-updatelayeredwindow#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <param name="crKey">
            <para>Type: <b><a href="https://docs.microsoft.com/windows/desktop/gdi/colorref">COLORREF</a></b> A structure that specifies the color key to be used when composing the layered window. To generate a <a href="https://docs.microsoft.com/windows/desktop/gdi/colorref">COLORREF</a>, use the <a href="https://docs.microsoft.com/windows/desktop/api/wingdi/nf-wingdi-rgb">RGB</a> macro.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-updatelayeredwindow#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <param name="pblend">
            <para>Type: <b><a href="https://docs.microsoft.com/windows/desktop/api/wingdi/ns-wingdi-blendfunction">BLENDFUNCTION</a>*</b> A pointer to a structure that specifies the transparency value to be used when composing the layered window.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-updatelayeredwindow#parameters">Read more on docs.microsoft.com</see>.</para>
            </param>
            <param name="dwFlags">Type: <b>DWORD</b></param>
            <returns>
            <para>Type: <b>BOOL</b> If the function succeeds, the return value is nonzero. If the function fails, the return value is zero. To get extended error information, call <a href="/windows/desktop/api/errhandlingapi/nf-errhandlingapi-getlasterror">GetLastError</a>.</para>
            </returns>
            <remarks>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-updatelayeredwindow">Learn more about this API from docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="M:Windows.Win32.PInvoke.FillRect(System.Runtime.InteropServices.SafeHandle,Windows.Win32.Foundation.RECT*,System.Runtime.InteropServices.SafeHandle)">
            <inheritdoc cref="M:Windows.Win32.PInvoke.FillRect(Windows.Win32.Graphics.Gdi.HDC,Windows.Win32.Foundation.RECT*,Windows.Win32.Graphics.Gdi.HBRUSH)"/>
        </member>
        <member name="M:Windows.Win32.PInvoke.FillRect(Windows.Win32.Graphics.Gdi.HDC,Windows.Win32.Foundation.RECT*,Windows.Win32.Graphics.Gdi.HBRUSH)">
            <summary>The FillRect function fills a rectangle by using the specified brush. This function includes the left and top borders, but excludes the right and bottom borders of the rectangle.</summary>
            <param name="hDC">A handle to the device context.</param>
            <param name="lprc">A pointer to a <a href="https://docs.microsoft.com/windows/desktop/api/windef/ns-windef-rect">RECT</a> structure that contains the logical coordinates of the rectangle to be filled.</param>
            <param name="hbr">A handle to the brush used to fill the rectangle.</param>
            <returns>
            <para>If the function succeeds, the return value is nonzero. If the function fails, the return value is zero.</para>
            </returns>
            <remarks>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-fillrect">Learn more about this API from docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="F:Windows.Win32.SC.F_ISSECURE">
            <summary>
            SCF_ISSECURE
            </summary>
        </member>
        <member name="T:Windows.Win32.WM">
            <summary>
            Window message values, WM_*
            </summary>
        </member>
        <!-- Badly formed XML comment ignored for member "T:Windows.Win32.Graphics.Gdi.BITMAPINFO" -->
        <member name="F:Windows.Win32.Graphics.Gdi.BITMAPINFO.bmiHeader">
            <summary>
            <para>A <a href="https://docs.microsoft.com/previous-versions/dd183376(v=vs.85)">BITMAPINFOHEADER</a> structure that contains information about the dimensions of color format. .</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//wingdi/ns-wingdi-bitmapinfo#members">Read more on docs.microsoft.com</see>.</para>
            </summary>
        </member>
        <member name="F:Windows.Win32.Graphics.Gdi.BITMAPINFO.bmiColors">
            <summary>
            <para>The <b>bmiColors</b> member contains one of the following: </para>
            <para>This doc was truncated.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//wingdi/ns-wingdi-bitmapinfo#members">Read more on docs.microsoft.com</see>.</para>
            </summary>
        </member>
        <member name="P:Windows.Win32.Graphics.Gdi.BITMAPINFO.__winmdroot_Graphics_Gdi_RGBQUAD_1.Length">
            <summary>Always <c>1</c>.</summary>
        </member>
        <member name="T:Windows.Win32.Graphics.Gdi.BITMAPINFOHEADER">
            <summary>The BITMAPINFOHEADER structure contains information about the dimensions and color format of a device-independent bitmap (DIB).</summary>
            <remarks>
            <para><h3><a id="Color_Tables"></a><a id="color_tables"></a><a id="COLOR_TABLES"></a>Color Tables</h3> The <b>BITMAPINFOHEADER</b> structure may be followed by an array of palette entries or color masks. The rules depend on the value of <b>biCompression</b>. </para>
            <para>This doc was truncated.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//wingdi/ns-wingdi-bitmapinfoheader#">Read more on docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="F:Windows.Win32.Graphics.Gdi.BITMAPINFOHEADER.biSize">
            <summary>Specifies the number of bytes required by the structure. This value does not include the size of the color table or the size of the color masks, if they are appended to the end of structure. See Remarks.</summary>
        </member>
        <member name="F:Windows.Win32.Graphics.Gdi.BITMAPINFOHEADER.biWidth">
            <summary>Specifies the width of the bitmap, in pixels. For information about calculating the stride of the bitmap, see Remarks.</summary>
        </member>
        <member name="F:Windows.Win32.Graphics.Gdi.BITMAPINFOHEADER.biHeight">
            <summary>
            <para>Specifies the height of the bitmap, in pixels. </para>
            <para>This doc was truncated.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//wingdi/ns-wingdi-bitmapinfoheader#members">Read more on docs.microsoft.com</see>.</para>
            </summary>
        </member>
        <member name="F:Windows.Win32.Graphics.Gdi.BITMAPINFOHEADER.biPlanes">
            <summary>Specifies the number of planes for the target device. This value must be set to 1.</summary>
        </member>
        <member name="F:Windows.Win32.Graphics.Gdi.BITMAPINFOHEADER.biBitCount">
            <summary>Specifies the number of bits per pixel (bpp). For uncompressed formats, this value is the average number of bits per pixel. For compressed formats, this value is the implied bit depth of the uncompressed image, after the image has been decoded.</summary>
        </member>
        <member name="F:Windows.Win32.Graphics.Gdi.BITMAPINFOHEADER.biCompression">
            <summary>
            <para>For compressed video and YUV formats, this member is a FOURCC code, specified as a <b>DWORD</b> in little-endian order. For example, YUYV video has the FOURCC 'VYUY' or 0x56595559. For more information, see <a href="https://docs.microsoft.com/windows/desktop/DirectShow/fourcc-codes">FOURCC Codes</a>. For uncompressed RGB formats, the following values are possible: </para>
            <para>This doc was truncated.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//wingdi/ns-wingdi-bitmapinfoheader#members">Read more on docs.microsoft.com</see>.</para>
            </summary>
        </member>
        <member name="F:Windows.Win32.Graphics.Gdi.BITMAPINFOHEADER.biSizeImage">
            <summary>Specifies the size, in bytes, of the image. This can be set to 0 for uncompressed RGB bitmaps.</summary>
        </member>
        <member name="F:Windows.Win32.Graphics.Gdi.BITMAPINFOHEADER.biXPelsPerMeter">
            <summary>Specifies the horizontal resolution, in pixels per meter, of the target device for the bitmap.</summary>
        </member>
        <member name="F:Windows.Win32.Graphics.Gdi.BITMAPINFOHEADER.biYPelsPerMeter">
            <summary>Specifies the vertical resolution, in pixels per meter, of the target device for the bitmap.</summary>
        </member>
        <member name="F:Windows.Win32.Graphics.Gdi.BITMAPINFOHEADER.biClrUsed">
            <summary>Specifies the number of color indices in the color table that are actually used by the bitmap. See Remarks for more information.</summary>
        </member>
        <member name="F:Windows.Win32.Graphics.Gdi.BITMAPINFOHEADER.biClrImportant">
            <summary>Specifies the number of color indices that are considered important for displaying the bitmap. If this value is zero, all colors are important.</summary>
        </member>
        <member name="T:Windows.Win32.Graphics.Gdi.BLENDFUNCTION">
            <summary>The BLENDFUNCTION structure controls blending by specifying the blending functions for source and destination bitmaps.</summary>
            <remarks>
            <para>When the <b>AlphaFormat</b> member is AC_SRC_ALPHA, the source bitmap must be 32 bpp. If it is not, the <a href="https://docs.microsoft.com/windows/desktop/api/wingdi/nf-wingdi-alphablend">AlphaBlend</a> function will fail. When the <b>BlendOp</b> member is AC_SRC_OVER, the source bitmap is placed over the destination bitmap based on the alpha values of the source pixels. If the source bitmap has no per-pixel alpha value (that is, AC_SRC_ALPHA is not set), the <b>SourceConstantAlpha</b> value determines the blend of the source and destination bitmaps, as shown in the following table. Note that SCA is used for <b>SourceConstantAlpha</b> here. Also, SCA is divided by 255 because it has a value that ranges from 0 to 255. </para>
            <para>This doc was truncated.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//wingdi/ns-wingdi-blendfunction#">Read more on docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="F:Windows.Win32.Graphics.Gdi.BLENDFUNCTION.BlendOp">
            <summary>The source blend operation. Currently, the only source and destination blend operation that has been defined is AC_SRC_OVER. For details, see the following Remarks section.</summary>
        </member>
        <member name="F:Windows.Win32.Graphics.Gdi.BLENDFUNCTION.BlendFlags">
            <summary>Must be zero.</summary>
        </member>
        <member name="F:Windows.Win32.Graphics.Gdi.BLENDFUNCTION.SourceConstantAlpha">
            <summary>Specifies an alpha transparency value to be used on the entire source bitmap. The <b>SourceConstantAlpha</b> value is combined with any per-pixel alpha values in the source bitmap. If you set <b>SourceConstantAlpha</b> to 0, it is assumed that your image is transparent. Set the <b>SourceConstantAlpha</b> value to 255 (opaque) when you only want to use per-pixel alpha values.</summary>
        </member>
        <member name="F:Windows.Win32.Graphics.Gdi.BLENDFUNCTION.AlphaFormat">
            <summary>
            <para>This member controls the way the source and destination bitmaps are interpreted. <b>AlphaFormat</b> has the following value. </para>
            <para>This doc was truncated.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//wingdi/ns-wingdi-blendfunction#members">Read more on docs.microsoft.com</see>.</para>
            </summary>
        </member>
        <member name="T:Windows.Win32.Graphics.Gdi.MONITORINFO">
            <summary>The MONITORINFO structure contains information about a display monitor.The GetMonitorInfo function stores information in a MONITORINFO structure or a MONITORINFOEX structure.The MONITORINFO structure is a subset of the MONITORINFOEX structure.</summary>
            <remarks>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/ns-winuser-monitorinfo">Learn more about this API from docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="F:Windows.Win32.Graphics.Gdi.MONITORINFO.cbSize">
            <summary>
            <para>The size of the structure, in bytes. Set this member to <c>sizeof ( MONITORINFO )</c> before calling the <a href="https://docs.microsoft.com/windows/desktop/api/winuser/nf-winuser-getmonitorinfoa">GetMonitorInfo</a> function. Doing so lets the function determine the type of structure you are passing to it.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/ns-winuser-monitorinfo#members">Read more on docs.microsoft.com</see>.</para>
            </summary>
        </member>
        <member name="F:Windows.Win32.Graphics.Gdi.MONITORINFO.rcMonitor">
            <summary>A <a href="https://docs.microsoft.com/windows/desktop/api/windef/ns-windef-rect">RECT</a> structure that specifies the display monitor rectangle, expressed in virtual-screen coordinates. Note that if the monitor is not the primary display monitor, some of the rectangle's coordinates may be negative values.</summary>
        </member>
        <member name="F:Windows.Win32.Graphics.Gdi.MONITORINFO.rcWork">
            <summary>A <a href="https://docs.microsoft.com/windows/desktop/api/windef/ns-windef-rect">RECT</a> structure that specifies the work area rectangle of the display monitor, expressed in virtual-screen coordinates. Note that if the monitor is not the primary display monitor, some of the rectangle's coordinates may be negative values.</summary>
        </member>
        <member name="F:Windows.Win32.Graphics.Gdi.MONITORINFO.dwFlags">
            <summary>
            <para>A set of flags that represent attributes of the display monitor. The following flag is defined. </para>
            <para>This doc was truncated.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/ns-winuser-monitorinfo#members">Read more on docs.microsoft.com</see>.</para>
            </summary>
        </member>
        <member name="T:Windows.Win32.Graphics.Gdi.RGBQUAD">
            <summary>The RGBQUAD structure describes a color consisting of relative intensities of red, green, and blue.</summary>
            <remarks>
            <para>The <b>bmiColors</b> member of the <a href="https://docs.microsoft.com/windows/desktop/api/wingdi/ns-wingdi-bitmapinfo">BITMAPINFO</a> structure consists of an array of <b>RGBQUAD</b> structures.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//wingdi/ns-wingdi-rgbquad#">Read more on docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="F:Windows.Win32.Graphics.Gdi.RGBQUAD.rgbBlue">
            <summary>The intensity of blue in the color.</summary>
        </member>
        <member name="F:Windows.Win32.Graphics.Gdi.RGBQUAD.rgbGreen">
            <summary>The intensity of green in the color.</summary>
        </member>
        <member name="F:Windows.Win32.Graphics.Gdi.RGBQUAD.rgbRed">
            <summary>The intensity of red in the color.</summary>
        </member>
        <member name="F:Windows.Win32.Graphics.Gdi.RGBQUAD.rgbReserved">
            <summary>This member is reserved and must be zero.</summary>
        </member>
        <member name="T:Windows.Win32.Graphics.Dwm.DWMWINDOWATTRIBUTE">
            <summary>Flags used by the [DwmGetWindowAttribute](/windows/desktop/api/dwmapi/nf-dwmapi-dwmgetwindowattribute) and [DwmSetWindowAttribute](/windows/desktop/api/dwmapi/nf-dwmapi-dwmsetwindowattribute) functions to specify window attributes for Desktop Window Manager (DWM) non-client rendering.</summary>
            <remarks>
            <para>> [!IMPORTANT] > The value described below is available in pre-release versions of the [SDK Insider Preview](https://www.microsoft.com/software-download/windowsinsiderpreviewSDK). `DWMWA_USE_HOSTBACKDROPBRUSH` Use with [DwmSetWindowAttribute](/windows/win32/api/dwmapi/nf-dwmapi-dwmsetwindowattribute). Enables a non-UWP window to use host backdrop brushes. If this flag is set, then a Win32 app that calls [Windows::UI::Composition](/uwp/api/windows.ui.composition) APIs can build transparency effects using the host backdrop brush (see [Compositor.CreateHostBackdropBrush](/uwp/api/windows.ui.composition.compositor.createhostbackdropbrush)). The retrieved value is of type **BOOL**. **TRUE** to enable host backdrop brushes for the window; otherwise, **FALSE**.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//dwmapi/ne-dwmapi-dwmwindowattribute#">Read more on docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="F:Windows.Win32.Graphics.Dwm.DWMWINDOWATTRIBUTE.DWMWA_NCRENDERING_ENABLED">
            <summary>Use with <a href="https://docs.microsoft.com/windows/desktop/api/dwmapi/nf-dwmapi-dwmgetwindowattribute">DwmGetWindowAttribute</a>. Discovers whether non-client rendering is enabled. The retrieved value is of type <b>BOOL</b>. <b>TRUE</b> if non-client rendering is enabled; otherwise, <b>FALSE</b>.</summary>
        </member>
        <member name="F:Windows.Win32.Graphics.Dwm.DWMWINDOWATTRIBUTE.DWMWA_NCRENDERING_POLICY">
            <summary>Use with <a href="https://docs.microsoft.com/windows/desktop/api/dwmapi/nf-dwmapi-dwmsetwindowattribute">DwmSetWindowAttribute</a>. Sets the non-client rendering policy. The <i>pvAttribute</i> parameter points to a value from the <a href="https://docs.microsoft.com/windows/desktop/api/dwmapi/ne-dwmapi-dwmncrenderingpolicy">DWMNCRENDERINGPOLICY</a> enumeration.</summary>
        </member>
        <member name="F:Windows.Win32.Graphics.Dwm.DWMWINDOWATTRIBUTE.DWMWA_TRANSITIONS_FORCEDISABLED">
            <summary>Use with <a href="https://docs.microsoft.com/windows/desktop/api/dwmapi/nf-dwmapi-dwmsetwindowattribute">DwmSetWindowAttribute</a>. Enables or forcibly disables DWM transitions. The <i>pvAttribute</i> parameter points to a value of type <b>BOOL</b>. <b>TRUE</b> to disable transitions, or <b>FALSE</b> to enable transitions.</summary>
        </member>
        <member name="F:Windows.Win32.Graphics.Dwm.DWMWINDOWATTRIBUTE.DWMWA_ALLOW_NCPAINT">
            <summary>Use with <a href="https://docs.microsoft.com/windows/desktop/api/dwmapi/nf-dwmapi-dwmsetwindowattribute">DwmSetWindowAttribute</a>. Enables content rendered in the non-client area to be visible on the frame drawn by DWM. The <i>pvAttribute</i> parameter points to a value of type <b>BOOL</b>. <b>TRUE</b> to enable content rendered in the non-client area to be visible on the frame; otherwise, <b>FALSE</b>.</summary>
        </member>
        <member name="F:Windows.Win32.Graphics.Dwm.DWMWINDOWATTRIBUTE.DWMWA_CAPTION_BUTTON_BOUNDS">
            <summary>Use with <a href="https://docs.microsoft.com/windows/desktop/api/dwmapi/nf-dwmapi-dwmgetwindowattribute">DwmGetWindowAttribute</a>. Retrieves the bounds of the caption button area in the window-relative space. The retrieved value is of type <a href="https://docs.microsoft.com/windows/desktop/api/windef/ns-windef-rect">RECT</a>. If the window is minimized or otherwise not visible to the user, then the value of the **RECT** retrieved is undefined. You should check whether the retrieved **RECT** contains a boundary that you can work with, and if it doesn't then you can conclude that the window is minimized or otherwise not visible.</summary>
        </member>
        <member name="F:Windows.Win32.Graphics.Dwm.DWMWINDOWATTRIBUTE.DWMWA_NONCLIENT_RTL_LAYOUT">
            <summary>Use with <a href="https://docs.microsoft.com/windows/desktop/api/dwmapi/nf-dwmapi-dwmsetwindowattribute">DwmSetWindowAttribute</a>. Specifies whether non-client content is right-to-left (RTL) mirrored. The <i>pvAttribute</i> parameter points to a value of type <b>BOOL</b>. <b>TRUE</b> if the non-client content is right-to-left (RTL) mirrored; otherwise, <b>FALSE</b>.</summary>
        </member>
        <member name="F:Windows.Win32.Graphics.Dwm.DWMWINDOWATTRIBUTE.DWMWA_FORCE_ICONIC_REPRESENTATION">
            <summary>Use with <a href="https://docs.microsoft.com/windows/desktop/api/dwmapi/nf-dwmapi-dwmsetwindowattribute">DwmSetWindowAttribute</a>. Forces the window to display an iconic thumbnail or peek representation (a static bitmap), even if a live or snapshot representation of the window is available. This value is normally set during a window's creation, and not changed throughout the window's lifetime. Some scenarios, however, might require the value to change over time. The <i>pvAttribute</i> parameter points to a value of type <b>BOOL</b>. <b>TRUE</b> to require a iconic thumbnail or peek representation; otherwise, <b>FALSE</b>.</summary>
        </member>
        <member name="F:Windows.Win32.Graphics.Dwm.DWMWINDOWATTRIBUTE.DWMWA_FLIP3D_POLICY">
            <summary>Use with <a href="https://docs.microsoft.com/windows/desktop/api/dwmapi/nf-dwmapi-dwmsetwindowattribute">DwmSetWindowAttribute</a>. Sets how Flip3D treats the window. The <i>pvAttribute</i> parameter points to a value from the <a href="https://docs.microsoft.com/windows/desktop/api/dwmapi/ne-dwmapi-dwmflip3dwindowpolicy">DWMFLIP3DWINDOWPOLICY</a> enumeration.</summary>
        </member>
        <member name="F:Windows.Win32.Graphics.Dwm.DWMWINDOWATTRIBUTE.DWMWA_EXTENDED_FRAME_BOUNDS">
            <summary>Use with <a href="https://docs.microsoft.com/windows/desktop/api/dwmapi/nf-dwmapi-dwmgetwindowattribute">DwmGetWindowAttribute</a>. Retrieves the extended frame bounds rectangle in screen space. The retrieved value is of type <a href="https://docs.microsoft.com/windows/desktop/api/windef/ns-windef-rect">RECT</a>.</summary>
        </member>
        <member name="F:Windows.Win32.Graphics.Dwm.DWMWINDOWATTRIBUTE.DWMWA_HAS_ICONIC_BITMAP">
            <summary>
            <para>Use with <a href="https://docs.microsoft.com/windows/desktop/api/dwmapi/nf-dwmapi-dwmsetwindowattribute">DwmSetWindowAttribute</a>. The window will provide a bitmap for use by DWM as an iconic thumbnail or peek representation (a static bitmap) for the window. <b>DWMWA_HAS_ICONIC_BITMAP</b> can be specified with <b>DWMWA_FORCE_ICONIC_REPRESENTATION</b>. <b>DWMWA_HAS_ICONIC_BITMAP</b> normally is set during a window's creation and not changed throughout the window's lifetime. Some scenarios, however, might require the value to change over time. The <i>pvAttribute</i> parameter points to a value of type <b>BOOL</b>.  <b>TRUE</b> to inform DWM that the window will provide an iconic thumbnail or peek representation; otherwise, <b>FALSE</b>. <b>Windows Vista and earlier: </b>This value is not supported.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//dwmapi/ne-dwmapi-dwmwindowattribute#members">Read more on docs.microsoft.com</see>.</para>
            </summary>
        </member>
        <member name="F:Windows.Win32.Graphics.Dwm.DWMWINDOWATTRIBUTE.DWMWA_DISALLOW_PEEK">
            <summary>
            <para>Use with <a href="https://docs.microsoft.com/windows/desktop/api/dwmapi/nf-dwmapi-dwmsetwindowattribute">DwmSetWindowAttribute</a>. Do not show peek preview for the window. The peek view shows a full-sized preview of the window when the mouse hovers over the window's thumbnail in the taskbar. If this attribute is set, hovering the mouse pointer over the window's thumbnail dismisses peek (in case another window in the group has a peek preview showing). The <i>pvAttribute</i> parameter points to a value of type <b>BOOL</b>. <b>TRUE</b> to prevent peek functionality, or <b>FALSE</b> to allow it. <b>Windows Vista and earlier: </b>This value is not supported.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//dwmapi/ne-dwmapi-dwmwindowattribute#members">Read more on docs.microsoft.com</see>.</para>
            </summary>
        </member>
        <member name="F:Windows.Win32.Graphics.Dwm.DWMWINDOWATTRIBUTE.DWMWA_EXCLUDED_FROM_PEEK">
            <summary>
            <para>Use with <a href="https://docs.microsoft.com/windows/desktop/api/dwmapi/nf-dwmapi-dwmsetwindowattribute">DwmSetWindowAttribute</a>. Prevents a window from fading to a glass sheet when peek is invoked. The <i>pvAttribute</i> parameter points to a value of type <b>BOOL</b>. <b>TRUE</b> to prevent the window from fading during another window's peek, or <b>FALSE</b> for normal behavior. <b>Windows Vista and earlier: </b>This value is not supported.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//dwmapi/ne-dwmapi-dwmwindowattribute#members">Read more on docs.microsoft.com</see>.</para>
            </summary>
        </member>
        <member name="F:Windows.Win32.Graphics.Dwm.DWMWINDOWATTRIBUTE.DWMWA_CLOAK">
            <summary>
            <para>Use with <a href="https://docs.microsoft.com/windows/desktop/api/dwmapi/nf-dwmapi-dwmsetwindowattribute">DwmSetWindowAttribute</a>. Cloaks the window such that it is not visible to the user. The window is still composed by DWM. <b>Using with DirectComposition: </b>Use the DWMWA_CLOAK flag to cloak the layered child window when animating a representation of the window's content via a DirectComposition visual that has been associated with the layered child window. For more details on this usage case, see <a href="https://docs.microsoft.com/windows/desktop/directcomp/how-to--animate-the-bitmap-of-a-layered-child-window">How to animate the bitmap of a layered child window</a>. <b>Windows 7 and earlier: </b>This value is not supported.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//dwmapi/ne-dwmapi-dwmwindowattribute#members">Read more on docs.microsoft.com</see>.</para>
            </summary>
        </member>
        <member name="F:Windows.Win32.Graphics.Dwm.DWMWINDOWATTRIBUTE.DWMWA_CLOAKED">
            <summary></summary>
        </member>
        <member name="F:Windows.Win32.Graphics.Dwm.DWMWINDOWATTRIBUTE.DWMWA_FREEZE_REPRESENTATION">
            <summary>
            <para>Use with <a href="https://docs.microsoft.com/windows/desktop/api/dwmapi/nf-dwmapi-dwmsetwindowattribute">DwmSetWindowAttribute</a>. Freeze the window's thumbnail image with its current visuals. Do no further live updates on the thumbnail image to match the window's contents. <b>Windows 7 and earlier: </b>This value is not supported.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//dwmapi/ne-dwmapi-dwmwindowattribute#members">Read more on docs.microsoft.com</see>.</para>
            </summary>
        </member>
        <member name="F:Windows.Win32.Graphics.Dwm.DWMWINDOWATTRIBUTE.DWMWA_LAST">
            <summary>The maximum recognized <b>DWMWINDOWATTRIBUTE</b> value, used for validation purposes.</summary>
        </member>
        <member name="M:Windows.Win32.Foundation.HRESULT.ThrowOnFailure(System.IntPtr)">
            <inheritdoc cref="M:System.Runtime.InteropServices.Marshal.ThrowExceptionForHR(System.Int32,System.IntPtr)" />
            <param name="errorInfo">
            A pointer to the IErrorInfo interface that provides more information about the
            error. You can specify <see cref="F:System.IntPtr.Zero"/> to use the current IErrorInfo interface, or
            <c>new IntPtr(-1)</c> to ignore the current IErrorInfo interface and construct the exception
            just from the error code.
            </param>
            <returns><see langword="this"/> <see cref="T:Windows.Win32.Foundation.HRESULT"/>, if it does not reflect an error.</returns>
            <seealso cref="M:System.Runtime.InteropServices.Marshal.ThrowExceptionForHR(System.Int32,System.IntPtr)"/>
        </member>
        <member name="F:Windows.Win32.Foundation.HWND.HWND_TOP">
            <summary>Places the window at the top of the Z order.</summary>
            <remarks>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-setwindowpos">Learn more about this API from docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="F:Windows.Win32.Foundation.HWND.HWND_BOTTOM">
            <summary>Places the window at the bottom of the Z order. If the <i>hWnd</i> parameter identifies a topmost window, the window loses its topmost status and is placed at the bottom of all other windows.</summary>
            <remarks>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-setwindowpos">Learn more about this API from docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="F:Windows.Win32.Foundation.HWND.HWND_TOPMOST">
            <summary>Places the window above all non-topmost windows. The window maintains its topmost position even when it is deactivated.</summary>
            <remarks>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-setwindowpos">Learn more about this API from docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="F:Windows.Win32.Foundation.HWND.HWND_NOTOPMOST">
            <summary>Places the window above all non-topmost windows (that is, behind all topmost windows). This flag has no effect if the window is already a non-topmost window.</summary>
            <remarks>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/nf-winuser-setwindowpos">Learn more about this API from docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="T:Windows.Win32.Foundation.PCWSTR">
            <summary>
            A pointer to a constant character string.
            </summary>
        </member>
        <member name="F:Windows.Win32.Foundation.PCWSTR.Value">
            <summary>
            A pointer to the first character in the string. The content should be considered readonly, as it was typed as constant in the SDK.
            </summary>
        </member>
        <member name="P:Windows.Win32.Foundation.PCWSTR.Length">
            <summary>
            Gets the number of characters up to the first null character (exclusive).
            </summary>
        </member>
        <member name="M:Windows.Win32.Foundation.PCWSTR.ToString">
            <summary>
            Returns a <see langword="string"/> with a copy of this character array.
            </summary>
            <returns>A <see langword="string"/>, or <see langword="null"/> if <see cref="F:Windows.Win32.Foundation.PCWSTR.Value"/> is <see langword="null"/>.</returns>
        </member>
        <member name="T:Windows.Win32.Foundation.RECT">
            <summary>The RECT structure defines a rectangle by the coordinates of its upper-left and lower-right corners.</summary>
            <remarks>
            <para>The RECT structure is identical to the <a href="https://docs.microsoft.com/windows/desktop/api/windef/ns-windef-rectl">RECTL</a> structure.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//windef/ns-windef-rect#">Read more on docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="F:Windows.Win32.Foundation.RECT.left">
            <summary>Specifies the <i>x</i>-coordinate of the upper-left corner of the rectangle.</summary>
        </member>
        <member name="F:Windows.Win32.Foundation.RECT.top">
            <summary>Specifies the <i>y</i>-coordinate of the upper-left corner of the rectangle.</summary>
        </member>
        <member name="F:Windows.Win32.Foundation.RECT.right">
            <summary>Specifies the <i>x</i>-coordinate of the lower-right corner of the rectangle.</summary>
        </member>
        <member name="F:Windows.Win32.Foundation.RECT.bottom">
            <summary>Specifies the <i>y</i>-coordinate of the lower-right corner of the rectangle.</summary>
        </member>
        <member name="T:Windows.Win32.Foundation.SIZE">
            <summary>The SIZE structure defines the width and height of a rectangle.</summary>
            <remarks>
            <para>The rectangle dimensions stored in this structure can correspond to viewport extents, window extents, text extents, bitmap dimensions, or the aspect-ratio filter for some extended functions.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//windef/ns-windef-size#">Read more on docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="F:Windows.Win32.Foundation.SIZE.cx">
            <summary>Specifies the rectangle's width. The units depend on which function uses this structure.</summary>
        </member>
        <member name="F:Windows.Win32.Foundation.SIZE.cy">
            <summary>Specifies the rectangle's height. The units depend on which function uses this structure.</summary>
        </member>
        <member name="T:Windows.Win32.UI.WindowsAndMessaging.MINMAXINFO">
            <summary>Contains information about a window's maximized size and position and its minimum and maximum tracking size.</summary>
            <remarks>
            <para>For systems with multiple monitors, the <b>ptMaxSize</b> and <b>ptMaxPosition</b> members describe the maximized size and position of the window on the primary monitor, even if the window ultimately maximizes onto a secondary monitor. In that case, the window manager adjusts these values to compensate for differences between the primary monitor and the monitor that displays the window. Thus, if the user leaves <b>ptMaxSize</b> untouched, a window on a monitor larger than the primary monitor maximizes to the size of the larger monitor.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/ns-winuser-minmaxinfo#">Read more on docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="F:Windows.Win32.UI.WindowsAndMessaging.MINMAXINFO.ptReserved">
            <summary>
            <para>Type: <b><a href="https://docs.microsoft.com/previous-versions/dd162805(v=vs.85)">POINT</a></b> Reserved; do not use.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/ns-winuser-minmaxinfo#members">Read more on docs.microsoft.com</see>.</para>
            </summary>
        </member>
        <member name="F:Windows.Win32.UI.WindowsAndMessaging.MINMAXINFO.ptMaxSize">
            <summary>
            <para>Type: <b><a href="https://docs.microsoft.com/previous-versions/dd162805(v=vs.85)">POINT</a></b> The maximized width (<b>x</b> member) and the maximized height (<b>y</b> member) of the window. For top-level windows, this value is based on the width of the primary monitor.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/ns-winuser-minmaxinfo#members">Read more on docs.microsoft.com</see>.</para>
            </summary>
        </member>
        <member name="F:Windows.Win32.UI.WindowsAndMessaging.MINMAXINFO.ptMaxPosition">
            <summary>
            <para>Type: <b><a href="https://docs.microsoft.com/previous-versions/dd162805(v=vs.85)">POINT</a></b> The position of the left side of the maximized window (<b>x</b> member) and the position of the top of the maximized window (<b>y</b> member). For top-level windows, this value is based on the position of the primary monitor.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/ns-winuser-minmaxinfo#members">Read more on docs.microsoft.com</see>.</para>
            </summary>
        </member>
        <member name="F:Windows.Win32.UI.WindowsAndMessaging.MINMAXINFO.ptMinTrackSize">
            <summary>
            <para>Type: <b><a href="https://docs.microsoft.com/previous-versions/dd162805(v=vs.85)">POINT</a></b> The minimum tracking width (<b>x</b> member) and the minimum tracking height (<b>y</b> member) of the window. This value can be obtained programmatically from the system metrics <b>SM_CXMINTRACK</b> and <b>SM_CYMINTRACK</b> (see the <a href="https://docs.microsoft.com/windows/desktop/api/winuser/nf-winuser-getsystemmetrics">GetSystemMetrics</a> function).</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/ns-winuser-minmaxinfo#members">Read more on docs.microsoft.com</see>.</para>
            </summary>
        </member>
        <member name="F:Windows.Win32.UI.WindowsAndMessaging.MINMAXINFO.ptMaxTrackSize">
            <summary>
            <para>Type: <b><a href="https://docs.microsoft.com/previous-versions/dd162805(v=vs.85)">POINT</a></b> The maximum tracking width (<b>x</b> member) and the maximum tracking height (<b>y</b> member) of the window. This value is based on the size of the virtual screen and can be obtained programmatically from the system metrics <b>SM_CXMAXTRACK</b> and <b>SM_CYMAXTRACK</b> (see the <a href="https://docs.microsoft.com/windows/desktop/api/winuser/nf-winuser-getsystemmetrics">GetSystemMetrics</a> function).</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/ns-winuser-minmaxinfo#members">Read more on docs.microsoft.com</see>.</para>
            </summary>
        </member>
        <member name="T:Windows.Win32.UI.WindowsAndMessaging.STYLESTRUCT">
            <summary>Contains the styles for a window.</summary>
            <remarks>
            <para>The styles in <b>styleOld</b> and <b>styleNew</b> can be either the window styles (<b>WS_*</b>) or the extended window styles (<b>WS_EX_*</b>), depending on the <i>wParam</i> of the message that includes <b>STYLESTRUCT</b>. The <b>styleOld</b> and <b>styleNew</b> members indicate the styles through their bit pattern. Note that several styles are equal to zero; to detect these styles, test for the negation of their inverse style. For example, to see if <b>WS_EX_LEFT</b> is set, you test for ~<b>WS_EX_RIGHT</b>.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/ns-winuser-stylestruct#">Read more on docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="F:Windows.Win32.UI.WindowsAndMessaging.STYLESTRUCT.styleOld">
            <summary>
            <para>Type: <b>DWORD</b> The previous styles for a window. For more information, see the Remarks.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/ns-winuser-stylestruct#members">Read more on docs.microsoft.com</see>.</para>
            </summary>
        </member>
        <member name="F:Windows.Win32.UI.WindowsAndMessaging.STYLESTRUCT.styleNew">
            <summary>
            <para>Type: <b>DWORD</b> The new styles for a window. For more information, see the Remarks.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/ns-winuser-stylestruct#members">Read more on docs.microsoft.com</see>.</para>
            </summary>
        </member>
        <member name="T:Windows.Win32.UI.WindowsAndMessaging.TPMPARAMS">
            <summary>Contains extended parameters for the TrackPopupMenuEx function.</summary>
            <remarks>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/ns-winuser-tpmparams">Learn more about this API from docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="F:Windows.Win32.UI.WindowsAndMessaging.TPMPARAMS.cbSize">
            <summary>
            <para>Type: <b>UINT</b> The size of structure, in bytes.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/ns-winuser-tpmparams#members">Read more on docs.microsoft.com</see>.</para>
            </summary>
        </member>
        <member name="F:Windows.Win32.UI.WindowsAndMessaging.TPMPARAMS.rcExclude">
            <summary>
            <para>Type: <b><a href="https://docs.microsoft.com/windows/desktop/api/windef/ns-windef-rect">RECT</a></b> The rectangle to be excluded when positioning the window, in screen coordinates.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/ns-winuser-tpmparams#members">Read more on docs.microsoft.com</see>.</para>
            </summary>
        </member>
        <member name="T:Windows.Win32.UI.WindowsAndMessaging.WINDOWPLACEMENT">
            <summary>Contains information about the placement of a window on the screen.</summary>
            <remarks>
            <para>If the window is a top-level window that does not have the <b>WS_EX_TOOLWINDOW</b> window style, then the coordinates represented by the following members are in workspace coordinates: <b>ptMinPosition</b>, <b>ptMaxPosition</b>, and <b>rcNormalPosition</b>. Otherwise, these members are in screen coordinates. Workspace coordinates differ from screen coordinates in that they take the locations and sizes of application toolbars (including the taskbar) into account. Workspace coordinate (0,0) is the upper-left corner of the workspace area, the area of the screen not being used by application toolbars. The coordinates used in a <b>WINDOWPLACEMENT</b> structure should be used only by the <a href="https://docs.microsoft.com/windows/desktop/api/winuser/nf-winuser-getwindowplacement">GetWindowPlacement</a> and <a href="https://docs.microsoft.com/windows/desktop/api/winuser/nf-winuser-setwindowplacement">SetWindowPlacement</a> functions. Passing workspace coordinates to functions which expect screen coordinates (such as <a href="https://docs.microsoft.com/windows/desktop/api/winuser/nf-winuser-setwindowpos">SetWindowPos</a>) will result in the window appearing in the wrong location. For example, if the taskbar is at the top of the screen, saving window coordinates using <b>GetWindowPlacement</b> and restoring them using <b>SetWindowPos</b> causes the window to appear to "creep" up the screen.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/ns-winuser-windowplacement#">Read more on docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="F:Windows.Win32.UI.WindowsAndMessaging.WINDOWPLACEMENT.length">
            <summary>
            <para>Type: <b>UINT</b> The length of the structure, in bytes. Before calling the <a href="https://docs.microsoft.com/windows/desktop/api/winuser/nf-winuser-getwindowplacement">GetWindowPlacement</a> or <a href="https://docs.microsoft.com/windows/desktop/api/winuser/nf-winuser-setwindowplacement">SetWindowPlacement</a> functions, set this member to <c>sizeof(WINDOWPLACEMENT)</c>.</para>
            <para><a href="https://docs.microsoft.com/windows/desktop/api/winuser/nf-winuser-getwindowplacement">GetWindowPlacement</a> and <a href="https://docs.microsoft.com/windows/desktop/api/winuser/nf-winuser-setwindowplacement">SetWindowPlacement</a> fail if this member is not set correctly.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/ns-winuser-windowplacement#members">Read more on docs.microsoft.com</see>.</para>
            </summary>
        </member>
        <member name="F:Windows.Win32.UI.WindowsAndMessaging.WINDOWPLACEMENT.flags">
            <summary>Type: <b>UINT</b></summary>
        </member>
        <member name="F:Windows.Win32.UI.WindowsAndMessaging.WINDOWPLACEMENT.showCmd">
            <summary>
            <para>Type: <b>UINT</b> The current show state of the window. It can be any of the values that can be specified in the <i>nCmdShow</i> parameter for the <a href="https://docs.microsoft.com/windows/desktop/api/winuser/nf-winuser-showwindow">ShowWindow</a> function.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/ns-winuser-windowplacement#members">Read more on docs.microsoft.com</see>.</para>
            </summary>
        </member>
        <member name="F:Windows.Win32.UI.WindowsAndMessaging.WINDOWPLACEMENT.ptMinPosition">
            <summary>
            <para>Type: <b><a href="https://docs.microsoft.com/previous-versions/dd162805(v=vs.85)">POINT</a></b> The coordinates of the window's upper-left corner when the window is minimized.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/ns-winuser-windowplacement#members">Read more on docs.microsoft.com</see>.</para>
            </summary>
        </member>
        <member name="F:Windows.Win32.UI.WindowsAndMessaging.WINDOWPLACEMENT.ptMaxPosition">
            <summary>
            <para>Type: <b><a href="https://docs.microsoft.com/previous-versions/dd162805(v=vs.85)">POINT</a></b> The coordinates of the window's upper-left corner when the window is maximized.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/ns-winuser-windowplacement#members">Read more on docs.microsoft.com</see>.</para>
            </summary>
        </member>
        <member name="F:Windows.Win32.UI.WindowsAndMessaging.WINDOWPLACEMENT.rcNormalPosition">
            <summary>
            <para>Type: <b><a href="https://docs.microsoft.com/windows/desktop/api/windef/ns-windef-rect">RECT</a></b> The window's coordinates when the window is in the restored position.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/ns-winuser-windowplacement#members">Read more on docs.microsoft.com</see>.</para>
            </summary>
        </member>
        <member name="T:Windows.Win32.UI.WindowsAndMessaging.WINDOWPOS">
            <summary>Contains information about the size and position of a window.</summary>
            <remarks>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/ns-winuser-windowpos">Learn more about this API from docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="F:Windows.Win32.UI.WindowsAndMessaging.WINDOWPOS.hwnd">
            <summary>
            <para>Type: <b>HWND</b> A handle to the window.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/ns-winuser-windowpos#members">Read more on docs.microsoft.com</see>.</para>
            </summary>
        </member>
        <member name="F:Windows.Win32.UI.WindowsAndMessaging.WINDOWPOS.hwndInsertAfter">
            <summary>
            <para>Type: <b>HWND</b> The position of the window in Z order (front-to-back position). This member can be a handle to the window behind which this window is placed, or can be one of the special values listed with the <a href="https://docs.microsoft.com/windows/desktop/api/winuser/nf-winuser-setwindowpos">SetWindowPos</a> function.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/ns-winuser-windowpos#members">Read more on docs.microsoft.com</see>.</para>
            </summary>
        </member>
        <member name="F:Windows.Win32.UI.WindowsAndMessaging.WINDOWPOS.x">
            <summary>
            <para>Type: <b>int</b> The position of the left edge of the window.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/ns-winuser-windowpos#members">Read more on docs.microsoft.com</see>.</para>
            </summary>
        </member>
        <member name="F:Windows.Win32.UI.WindowsAndMessaging.WINDOWPOS.y">
            <summary>
            <para>Type: <b>int</b> The position of the top edge of the window.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/ns-winuser-windowpos#members">Read more on docs.microsoft.com</see>.</para>
            </summary>
        </member>
        <member name="F:Windows.Win32.UI.WindowsAndMessaging.WINDOWPOS.cx">
            <summary>
            <para>Type: <b>int</b> The window width, in pixels.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/ns-winuser-windowpos#members">Read more on docs.microsoft.com</see>.</para>
            </summary>
        </member>
        <member name="F:Windows.Win32.UI.WindowsAndMessaging.WINDOWPOS.cy">
            <summary>
            <para>Type: <b>int</b> The window height, in pixels.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/ns-winuser-windowpos#members">Read more on docs.microsoft.com</see>.</para>
            </summary>
        </member>
        <member name="F:Windows.Win32.UI.WindowsAndMessaging.WINDOWPOS.flags">
            <summary>Type: <b>UINT</b></summary>
        </member>
        <member name="T:Windows.Win32.UI.WindowsAndMessaging.WNDCLASSEXW">
            <summary>Contains window class information.</summary>
            <remarks>
            <para>> [!NOTE] > The winuser.h header defines WNDCLASSEX as an alias which automatically selects the ANSI or Unicode version of this function based on the definition of the UNICODE preprocessor constant. Mixing usage of the encoding-neutral alias with code that not encoding-neutral can lead to mismatches that result in compilation or runtime errors. For more information, see [Conventions for Function Prototypes](/windows/win32/intl/conventions-for-function-prototypes).</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/ns-winuser-wndclassexw#">Read more on docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="F:Windows.Win32.UI.WindowsAndMessaging.WNDCLASSEXW.cbSize">
            <summary>
            <para>Type: <b>UINT</b> The size, in bytes, of this structure. Set this member to <c>sizeof(WNDCLASSEX)</c>. Be sure to set this member before calling the <a href="https://docs.microsoft.com/windows/desktop/api/winuser/nf-winuser-getclassinfoexa">GetClassInfoEx</a> function.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/ns-winuser-wndclassexw#members">Read more on docs.microsoft.com</see>.</para>
            </summary>
        </member>
        <member name="F:Windows.Win32.UI.WindowsAndMessaging.WNDCLASSEXW.style">
            <summary>
            <para>Type: <b>UINT</b> The class style(s). This member can be any combination of the <a href="https://docs.microsoft.com/windows/desktop/winmsg/about-window-classes">Class Styles</a>.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/ns-winuser-wndclassexw#members">Read more on docs.microsoft.com</see>.</para>
            </summary>
        </member>
        <member name="F:Windows.Win32.UI.WindowsAndMessaging.WNDCLASSEXW.lpfnWndProc">
            <summary>
            <para>Type: <b>WNDPROC</b> A pointer to the window procedure. You must use the <a href="https://docs.microsoft.com/windows/desktop/api/winuser/nf-winuser-callwindowproca">CallWindowProc</a> function to call the window procedure. For more information, see <a href="https://docs.microsoft.com/previous-versions/windows/desktop/legacy/ms633573(v=vs.85)">WindowProc</a>.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/ns-winuser-wndclassexw#members">Read more on docs.microsoft.com</see>.</para>
            </summary>
        </member>
        <member name="F:Windows.Win32.UI.WindowsAndMessaging.WNDCLASSEXW.cbClsExtra">
            <summary>
            <para>Type: <b>int</b> The number of extra bytes to allocate following the window-class structure. The system initializes the bytes to zero.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/ns-winuser-wndclassexw#members">Read more on docs.microsoft.com</see>.</para>
            </summary>
        </member>
        <member name="F:Windows.Win32.UI.WindowsAndMessaging.WNDCLASSEXW.cbWndExtra">
            <summary>
            <para>Type: <b>int</b> The number of extra bytes to allocate following the window instance. The system initializes the bytes to zero. If an application uses <b>WNDCLASSEX</b> to register a dialog box created by using the <b>CLASS</b> directive in the resource file, it must set this member to <b>DLGWINDOWEXTRA</b>.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/ns-winuser-wndclassexw#members">Read more on docs.microsoft.com</see>.</para>
            </summary>
        </member>
        <member name="F:Windows.Win32.UI.WindowsAndMessaging.WNDCLASSEXW.hInstance">
            <summary>
            <para>Type: <b>HINSTANCE</b> A handle to the instance that contains the window procedure for the class.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/ns-winuser-wndclassexw#members">Read more on docs.microsoft.com</see>.</para>
            </summary>
        </member>
        <member name="F:Windows.Win32.UI.WindowsAndMessaging.WNDCLASSEXW.hIcon">
            <summary>
            <para>Type: <b>HICON</b> A handle to the class icon. This member must be a handle to an icon resource. If this member is <b>NULL</b>, the system provides a default icon.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/ns-winuser-wndclassexw#members">Read more on docs.microsoft.com</see>.</para>
            </summary>
        </member>
        <member name="F:Windows.Win32.UI.WindowsAndMessaging.WNDCLASSEXW.hCursor">
            <summary>
            <para>Type: <b>HCURSOR</b> A handle to the class cursor. This member must be a handle to a cursor resource. If this member is <b>NULL</b>, an application must explicitly set the cursor shape whenever the mouse moves into the application's window.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/ns-winuser-wndclassexw#members">Read more on docs.microsoft.com</see>.</para>
            </summary>
        </member>
        <member name="F:Windows.Win32.UI.WindowsAndMessaging.WNDCLASSEXW.hbrBackground">
            <summary>
            <para>Type: <b>HBRUSH</b> A handle to the class background brush. This member can be a handle to the brush to be used for painting the background, or it can be a color value. A color value must be one of the following standard system colors (the value 1 must be added to the chosen color). If a color value is given, you must convert it to one of the following <b>HBRUSH</b> types: </para>
            <para>This doc was truncated.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/ns-winuser-wndclassexw#members">Read more on docs.microsoft.com</see>.</para>
            </summary>
        </member>
        <member name="F:Windows.Win32.UI.WindowsAndMessaging.WNDCLASSEXW.lpszMenuName">
            <summary>
            <para>Type: <b>LPCTSTR</b> Pointer to a null-terminated character string that specifies the resource name of the class menu, as the name appears in the resource file. If you use an integer to identify the menu, use the <a href="https://docs.microsoft.com/windows/desktop/api/winuser/nf-winuser-makeintresourcea">MAKEINTRESOURCE</a> macro. If this member is <b>NULL</b>, windows belonging to this class have no default menu.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/ns-winuser-wndclassexw#members">Read more on docs.microsoft.com</see>.</para>
            </summary>
        </member>
        <member name="F:Windows.Win32.UI.WindowsAndMessaging.WNDCLASSEXW.lpszClassName">
            <summary>
            <para>Type: <b>LPCTSTR</b> A pointer to a null-terminated string or is an atom. If this parameter is an atom, it must be a class atom created by a previous call to the <a href="https://docs.microsoft.com/windows/desktop/api/winuser/nf-winuser-registerclassa">RegisterClass</a> or <a href="https://docs.microsoft.com/windows/desktop/api/winuser/nf-winuser-registerclassexa">RegisterClassEx</a> function. The atom must be in the low-order word of <b>lpszClassName</b>; the high-order word must be zero.</para>
            <para>If <b>lpszClassName</b> is a string, it specifies the window class name. The class name can be any name registered with <a href="https://docs.microsoft.com/windows/desktop/api/winuser/nf-winuser-registerclassa">RegisterClass</a> or <a href="https://docs.microsoft.com/windows/desktop/api/winuser/nf-winuser-registerclassexa">RegisterClassEx</a>, or any of the predefined control-class names. The maximum length for <b>lpszClassName</b> is 256. If <b>lpszClassName</b> is greater than the maximum length, the <a href="https://docs.microsoft.com/windows/desktop/api/winuser/nf-winuser-registerclassexa">RegisterClassEx</a> function will fail.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/ns-winuser-wndclassexw#members">Read more on docs.microsoft.com</see>.</para>
            </summary>
        </member>
        <member name="F:Windows.Win32.UI.WindowsAndMessaging.WNDCLASSEXW.hIconSm">
            <summary>
            <para>Type: <b>HICON</b> A handle to a small icon that is associated with the window class. If this member is <b>NULL</b>, the system searches the icon resource specified by the <b>hIcon</b> member for an icon of the appropriate size to use as the small icon.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/ns-winuser-wndclassexw#members">Read more on docs.microsoft.com</see>.</para>
            </summary>
        </member>
        <member name="T:Windows.Win32.UI.Controls.MARGINS">
            <summary>Returned by the GetThemeMargins function to define the margins of windows that have visual styles applied.</summary>
            <remarks>
            <para><see href="https://docs.microsoft.com/windows/win32/api//uxtheme/ns-uxtheme-margins">Learn more about this API from docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="F:Windows.Win32.UI.Controls.MARGINS.cxLeftWidth">
            <summary>
            <para>Type: <b>int</b> Width of the left border that retains its size.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//uxtheme/ns-uxtheme-margins#members">Read more on docs.microsoft.com</see>.</para>
            </summary>
        </member>
        <member name="F:Windows.Win32.UI.Controls.MARGINS.cxRightWidth">
            <summary>
            <para>Type: <b>int</b> Width of the right border that retains its size.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//uxtheme/ns-uxtheme-margins#members">Read more on docs.microsoft.com</see>.</para>
            </summary>
        </member>
        <member name="F:Windows.Win32.UI.Controls.MARGINS.cyTopHeight">
            <summary>
            <para>Type: <b>int</b> Height of the top border that retains its size.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//uxtheme/ns-uxtheme-margins#members">Read more on docs.microsoft.com</see>.</para>
            </summary>
        </member>
        <member name="F:Windows.Win32.UI.Controls.MARGINS.cyBottomHeight">
            <summary>
            <para>Type: <b>int</b> Height of the bottom border that retains its size.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//uxtheme/ns-uxtheme-margins#members">Read more on docs.microsoft.com</see>.</para>
            </summary>
        </member>
        <member name="T:Windows.Win32.UI.Input.KeyboardAndMouse.TRACKMOUSEEVENT">
            <summary>Used by the TrackMouseEvent function to track when the mouse pointer leaves a window or hovers over a window for a specified amount of time.</summary>
            <remarks>
            <para>The system default hover time-out is initially the menu drop-down time, which is 400 milliseconds. You can call <a href="https://docs.microsoft.com/windows/desktop/api/winuser/nf-winuser-systemparametersinfoa">SystemParametersInfo</a> and use <b>SPI_GETMOUSEHOVERTIME</b> to retrieve the default hover time-out. The system default hover rectangle is the same as the double-click rectangle. You can call <a href="https://docs.microsoft.com/windows/desktop/api/winuser/nf-winuser-systemparametersinfoa">SystemParametersInfo</a> and use <b>SPI_GETMOUSEHOVERWIDTH</b> and <b>SPI_GETMOUSEHOVERHEIGHT</b> to retrieve the size of the rectangle within which the mouse pointer has to stay for <a href="https://docs.microsoft.com/windows/desktop/api/winuser/nf-winuser-trackmouseevent">TrackMouseEvent</a> to generate a <a href="https://docs.microsoft.com/windows/desktop/inputdev/wm-mousehover">WM_MOUSEHOVER</a> message.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/ns-winuser-trackmouseevent#">Read more on docs.microsoft.com</see>.</para>
            </remarks>
        </member>
        <member name="F:Windows.Win32.UI.Input.KeyboardAndMouse.TRACKMOUSEEVENT.cbSize">
            <summary>
            <para>Type: <b>DWORD</b> The size of the <b>TRACKMOUSEEVENT</b> structure, in bytes.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/ns-winuser-trackmouseevent#members">Read more on docs.microsoft.com</see>.</para>
            </summary>
        </member>
        <member name="F:Windows.Win32.UI.Input.KeyboardAndMouse.TRACKMOUSEEVENT.dwFlags">
            <summary>Type: <b>DWORD</b></summary>
        </member>
        <member name="F:Windows.Win32.UI.Input.KeyboardAndMouse.TRACKMOUSEEVENT.hwndTrack">
            <summary>
            <para>Type: <b>HWND</b> A handle to the window to track.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/ns-winuser-trackmouseevent#members">Read more on docs.microsoft.com</see>.</para>
            </summary>
        </member>
        <member name="F:Windows.Win32.UI.Input.KeyboardAndMouse.TRACKMOUSEEVENT.dwHoverTime">
            <summary>
            <para>Type: <b>DWORD</b> The hover time-out (if <b>TME_HOVER</b> was specified in <b>dwFlags</b>), in milliseconds. Can be <b>HOVER_DEFAULT</b>, which means to use the system default hover time-out.</para>
            <para><see href="https://docs.microsoft.com/windows/win32/api//winuser/ns-winuser-trackmouseevent#members">Read more on docs.microsoft.com</see>.</para>
            </summary>
        </member>
        <member name="T:Windows.Win32.DeleteDCSafeHandle">
            <summary>
            Represents a Win32 handle that can be closed with <see cref="M:Windows.Win32.PInvoke.DeleteDC(Windows.Win32.Graphics.Gdi.CreatedHDC)"/>.
            </summary>
        </member>
        <member name="T:Windows.Win32.DeleteObjectSafeHandle">
            <summary>
            Represents a Win32 handle that can be closed with <see cref="M:Windows.Win32.PInvoke.DeleteObject(Windows.Win32.Graphics.Gdi.HGDIOBJ)"/>.
            </summary>
        </member>
        <member name="T:Windows.Win32.DestroyMenuSafeHandle">
            <summary>
            Represents a Win32 handle that can be closed with <see cref="M:Windows.Win32.PInvoke.DestroyMenu(Windows.Win32.UI.WindowsAndMessaging.HMENU)"/>.
            </summary>
        </member>
        <member name="T:Windows.Win32.FreeLibrarySafeHandle">
            <summary>
            Represents a Win32 handle that can be closed with <see cref="M:Windows.Win32.PInvoke.FreeLibrary(Windows.Win32.Foundation.HINSTANCE)"/>.
            </summary>
        </member>
    </members>
</doc>
