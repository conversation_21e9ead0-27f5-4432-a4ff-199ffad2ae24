{"About": "<PERSON><PERSON>", "AccentColor": "Accent Color", "Add": "Add", "AlwaysOnTop": "Immer im Vordergrund", "Audio": "Audio", "AudioFormat": "Audio Format", "AudioSaved": "Audio gespeichert", "BackColor": "Hintergrundfarbe", "BorderColor": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "BorderThickness": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Bottom": "Unten", "CaptureDuration": "Aufnahmedauer (in Sekunden)", "Center": "<PERSON><PERSON>", "Changelog": "Changelog", "Clear": "<PERSON><PERSON>", "ClearRecentList": "<PERSON>e leeren", "Clipboard": "Zwischenablage", "Close": "Schließen", "Color": "Farbe", "ConfigCodecs": "Configure Codecs", "Configure": "Einstellungen", "CopyOutPathClipboard": "Pfad zur Ausgabedatei in Zwischenablage kopieren", "CopyPath": "Pfad kopieren", "CopyToClipboard": "In Zwischenablage kopieren", "CornerRadius": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "CrashLogs": "Crash Logs", "Crop": "Crop", "CustomSize": "Custom Size", "CustomUrl": "Custom Url", "DarkTheme": "Dark Theme", "Delete": "Löschen", "DiscardChanges": "Discard Changes", "Disk": "Festplatte", "Donate": "<PERSON><PERSON><PERSON>", "DownloadFFmpeg": "Download FFmpeg", "Edit": "Edit", "Elapsed": "Elapsed", "ErrorOccurred": "Ein Fehler ist aufgetreten", "Exit": "<PERSON>den", "FFmpegFolder": "FFmpeg Verzeichnis", "FFmpegLog": "FFmpeg Log", "FileMenu": "File", "FileMenuNew": "New", "FileMenuOpen": "Open", "FileMenuSave": "Save", "FileNaming": "File Naming", "Flip": "<PERSON><PERSON><PERSON>", "FontSize": "Schriftgröße", "FrameRate": "FPS", "FullScreen": "Vollbildschirm", "HideOnFullScreenShot": "Anwendung bei Screenshot des gesamten Bildschirms verstecken", "Horizontal": "Horizontal", "Host": "Host", "Hotkeys": "Hotkeys", "ImageEditor": "Image Editor", "ImgEmpty": "Das Bild wurde nicht ges<PERSON>, da es leer war.", "ImgFormat": "Bildformat", "ImgSavedClipboard": "Bild in die Zwischenablage gespeichert", "ImgurFailed": "Imgur Upload fehlgeschlagen", "ImgurSuccess": "Imgur Upload erfolgreich", "ImgurUploading": "<PERSON><PERSON> <PERSON>n", "IncludeClicks": "<PERSON><PERSON><PERSON><PERSON> anzeigen", "IncludeCursor": "<PERSON><PERSON><PERSON> an<PERSON>igen", "IncludeKeys": "Tastenanschläge anzeigen", "Keymap": "Keymap", "Keystrokes": "Tastenanschläge", "KeystrokesHistoryCount": "History Count", "KeystrokesHistorySpacing": "History Spacing", "KeystrokesSeparateFile": "Save to separate Text file", "Language": "<PERSON><PERSON><PERSON>", "Left": "Links", "LoopbackSource": "Audio Ausgabequelle", "MaxRecent": "Maximale Anzahl Einträge", "MaxTextLength": "Maximale Textlänge", "MicSource": "Audio Eingabequelle", "MinCapture": "Bei Start der Aufnahme minimieren", "Minimize": "Minimieren", "MinTray": "In Tray minimieren", "MinTrayStartup": "Minimize to Tray on Startup", "MinTrayClose": "Minimize to Tray when Closed", "MouseClicks": "<PERSON><PERSON><PERSON><PERSON>", "MouseMiddleClickColor": "Middle Click Color", "MousePointer": "<PERSON>", "MouseRightClickColor": "Right Click Color", "NewWindow": "NewWindow", "No": "<PERSON><PERSON>", "None": "None", "Notifications": "Notifications", "NotSaved": "Nicht gespeichert", "NoWebcam": "Keine Webcam", "Ok": "OK", "OnlyAudio": "Nur Audio", "Opacity": "Opacity", "OpenFromClipboard": "Open from Clipboard", "OpenOutFolder": "Ausgabeordner öffnen", "OutFolder": "Ausgabeordner", "Overlays": "Overlays", "Padding": "Padding", "Password": "Password", "Paused": "<PERSON><PERSON><PERSON><PERSON> p<PERSON>", "PauseResume": "Pause | Fortsetzen", "PauseResumeRecording": "Aufnahme pausieren/fortsetzen", "PlayRecAudio": "Playback recorded audio in real-time", "Port": "Port", "Preview": "Preview", "PreStartCountdown": "Pre Start Countdown (seconds)", "Proxy": "Proxy", "Quality": "Qualität", "Radius": "<PERSON><PERSON>", "Recent": "Letzte Aufnahmen", "RecordStop": "Aufnehmen | Stopp", "Redo": "Redo", "Refresh": "Aktualisieren", "Region": "Region", "RegionSelector": "Regionsauswahl", "RemoveFromList": "Aus Liste löschen", "Reset": "Z<PERSON>ücksetzen", "Resize": "Größe ändern", "RestoreDefaults": "<PERSON><PERSON>", "Right": "<PERSON><PERSON><PERSON>", "Rotate": "Rotieren", "SaveToClipboard": "Save to Clipboard", "Screen": "Bildschirm", "ScreenShot": "ScreenShot", "ScreenShotActiveWindow": "ScreenShot Aktives Fenster", "ScreenShotDesktop": "ScreenShot Desktop", "ScreenShotSaved": "ScreenShot gespeichert", "ScreenShotTransforms": "ScreenShot Transformationen", "SelectFFmpegFolder": "FFmpeg Ordner auswählen", "SelectOutFolder": "Ausgabeordner auswählen", "SeparateAudioFiles": "Separate files for every audio source", "ShowSysNotify": "Tray Benachrichtigungen anzeigen", "SnapToWindow": "Snap to Window", "Sounds": "Sounds", "StartStopRecording": "Start/Stopp Aufnahme", "StreamingKeys": "Streaming Keys", "Timeout": "Zeitüberschreitung", "ToggleMouseClicks": "Toggle Mouse Clicks", "ToggleKeystrokes": "Toggle Keystrokes", "Tools": "Tools", "Top": "<PERSON><PERSON>", "TrayIcon": "Tray Icon", "Trim": "<PERSON><PERSON>", "Undo": "Undo", "UploadToImgur": "Upload to Imgur", "UseProxyAuth": "Proxy Authentifizierung verwenden", "UserName": "<PERSON><PERSON><PERSON><PERSON>", "VarFrameRate": "Variable Wiederholrate", "Vertical": "Vertikal", "Video": "Video", "VideoEncoder": "Video Encoder", "VideoSaved": "Video gespeichert", "VideoSource": "Video Quelle", "ViewCrashLogs": "View Crash Logs", "ViewLicenses": "View Licenses", "ViewOnGitHub": "View on GitHub", "WantToTranslate": "Wollen Sie bei der Übersetzung helfen?", "WebCam": "Webcam", "WebCamSeparateFile": "Record Webcam to separate file", "WebCamView": "WebCam Anzeige", "Website": "Website", "Window": "<PERSON><PERSON>", "Yes": "<PERSON>a"}