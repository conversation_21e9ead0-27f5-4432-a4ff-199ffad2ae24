{"About": "Ɣef", "AccentColor": "Ini n useɣdebbu", "Add": "Rnu", "AlwaysOnTop": "<PERSON><PERSON> nnig", "Audio": "<PERSON><PERSON>", "AudioFormat": "<PERSON><PERSON><PERSON>", "AudioSaved": "<PERSON><PERSON>", "BackColor": "Ini n ugilal", "BorderColor": "Ini n yiri", "BorderThickness": "Tuzert n yiri", "Bottom": "Ddaw", "CaptureDuration": "Tanzagt n tuṭṭfa (s tasinin)", "Center": "<PERSON><PERSON>", "Changelog": "Aɣmis n ibeddilen", "Clear": "<PERSON><PERSON><PERSON>", "ClearRecentList": "<PERSON><PERSON><PERSON> tabdart n melmi kan", "Clipboard": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Close": "<PERSON><PERSON>", "Color": "Ini", "ConfigCodecs": "S<PERSON><PERSON> ikudaken", "Configure": "Swel", "CopyOutPathClipboard": "Sukken abrid n ufaylu n tuffɣa ɣer tecfawit", "CopyPath": "Sukken abrid", "CopyToClipboard": "<PERSON><PERSON><PERSON>", "CornerRadius": "Aqqar n teɣmert", "CrashLogs": "Aɣ<PERSON> n iɣ<PERSON>en", "Crop": "Ɣeẓ", "CustomSize": "<PERSON><PERSON><PERSON> y<PERSON>", "CustomUrl": "<PERSON><PERSON> yug<PERSON>", "DarkTheme": "Asentel aberkan", "Delete": "Kkes", "DiscardChanges": "Sefsex ibeddilen", "Disk": "<PERSON><PERSON><PERSON><PERSON>", "Donate": "Efk", "DownloadFFmpeg": "Sider FFmpeg", "Edit": "Ẓreg", "Elapsed": "<PERSON><PERSON><PERSON>", "ErrorOccurred": "Tella-d tucc<PERSON>a", "Exit": "Ffeɣ", "FFmpegFolder": "Akaram n FFmpeg", "FFmpegLog": "Aɣmis n FFmpeg", "FileMenu": "<PERSON><PERSON><PERSON><PERSON>", "FileMenuNew": "Am<PERSON><PERSON>", "FileMenuOpen": "Ldi", "FileMenuSave": "<PERSON><PERSON>", "FileNaming": "File Naming", "Flip": "Snegdam", "FontSize": "Tiddi n tsefsit", "FrameRate": "FPS", "FullScreen": "<PERSON>g<PERSON><PERSON>", "HideOnFullScreenShot": "Ffer mi ara tili tuṭṭfa tummidt n ugdil", "Horizontal": "Aglawan", "Host": "Asneftaɣ", "Hotkeys": "Inegzumen", "ImageEditor": "Amaẓrag n tugna", "ImgEmpty": "<PERSON>r tettwas<PERSON><PERSON> ara. Tugna yettwaṭṭfen d tilemt", "ImgFormat": "Amasal n tugna", "ImgSavedClipboard": "Tugna tettwasekles di tecfawit", "ImgurFailed": "<PERSON><PERSON>r ur yeddi ara", "ImgurSuccess": "<PERSON><PERSON> yedda akken i<PERSON>a", "ImgurUploading": "<PERSON><PERSON> al<PERSON>", "IncludeClicks": "Seddu isitiyen n tɣerdayt", "IncludeCursor": "<PERSON>ddu ta<PERSON>", "IncludeKeys": "Seddu inekcumen n unasiw", "Keymap": "Keymap", "Keystrokes": "Inekcumen n unasiw", "KeystrokesHistoryCount": "History Count", "KeystrokesHistorySpacing": "History Spacing", "KeystrokesSeparateFile": "<PERSON><PERSON> u<PERSON>", "Language": "<PERSON><PERSON><PERSON><PERSON>", "Left": "<PERSON><PERSON><PERSON><PERSON>", "LoopbackSource": "Aɣbalu n tuffɣa n imesmeɣren n imesli", "MaxRecent": "Afella n iferdisen ara yeqqimen", "MaxTextLength": "<PERSON><PERSON><PERSON> tafellayt n uḍris", "MicSource": "Aɣbalu n usawaḍ", "MinCapture": "Simẓi di tazwara n tuṭṭfa", "Minimize": "Zimẓi", "MinTray": "Simẓi di teɣzut n telɣut", "MinTrayStartup": "Minimize to Tray on Startup", "MinTrayClose": "Minimize to Tray when Closed", "MouseClicks": "Isitiyen n tɣerdayt", "MouseMiddleClickColor": "Ini n usiti alemmas", "MousePointer": "<PERSON>", "MouseRightClickColor": "Ini n usiti ayefus", "NewWindow": "NewWindow", "No": "Ala", "None": "Ulac", "Notifications": "<PERSON><PERSON><PERSON><PERSON>", "NotSaved": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> ara", "NoWebcam": "Ulac webcam", "Ok": "IH", "OnlyAudio": "<PERSON><PERSON> kan", "Opacity": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "OpenFromClipboard": "Ldi si tecfawit", "OpenOutFolder": "<PERSON><PERSON> akaram n tuffɣa", "OutFolder": "Akaram n tuffɣa", "Overlays": "Overlays", "Padding": "Padding", "Password": "Awal n uɛeddi", "Paused": "<PERSON><PERSON><PERSON> i<PERSON>d", "PauseResume": "Seḥbes | Kemmel", "PauseResumeRecording": "<PERSON><PERSON><PERSON>/<PERSON><PERSON>l asekles", "PlayRecAudio": "Playback recorded audio in real-time", "Port": "Tawwurt", "Preview": "Preview", "PreStartCountdown": "Pre Start Countdown (seconds)", "Proxy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Quality": "Taɣara", "Radius": "<PERSON><PERSON><PERSON><PERSON>", "Recent": "<PERSON><PERSON> kan", "RecordStop": "Sekles | Bedd", "Redo": "Redo", "Refresh": "<PERSON><PERSON><PERSON><PERSON>", "Region": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "RegionSelector": "Amsefran n tmennaḍt", "RemoveFromList": "Kkes tabdart", "Reset": "<PERSON><PERSON>", "Resize": "<PERSON><PERSON> am<PERSON>", "RestoreDefaults": "Err-d lex<PERSON>as", "Right": "<PERSON><PERSON><PERSON>", "Rotate": "<PERSON><PERSON>", "SaveToClipboard": "Sekles di tecfawit", "Screen": "Agdil", "ScreenShot": "Tuṭṭfa n ugdil", "ScreenShotActiveWindow": "Ṭṭef as<PERSON><PERSON>u urmid", "ScreenShotDesktop": "Ṭṭef tanarit", "ScreenShotSaved": "Tuṭṭfa n ugdil tettwasekles", "ScreenShotTransforms": "Aselket n tuṭṭfa n ugdil", "SelectFFmpegFolder": "Fren akaram n FFmpeg", "SelectOutFolder": "Fren akaram n tuffɣa", "SeparateAudioFiles": "Ifuyla iberzen i yal aɣbalu ameslaw", "ShowSysNotify": "<PERSON><PERSON> til<PERSON>a", "SnapToWindow": "Snap to Window", "Sounds": "Sounds", "StartStopRecording": "<PERSON><PERSON>/Se<PERSON><PERSON> asekles", "StreamingKeys": "Streaming Keys", "Timeout": "<PERSON><PERSON><PERSON><PERSON> afellay", "ToggleMouseClicks": "Toggle Mouse Clicks", "ToggleKeystrokes": "Toggle Keystrokes", "Tools": "<PERSON><PERSON><PERSON>", "Top": "Nnig", "TrayIcon": "Tray Icon", "Trim": "<PERSON><PERSON>", "Undo": "<PERSON><PERSON><PERSON>", "UploadToImgur": "<PERSON><PERSON>", "UseProxyAuth": "Seqdec apṛuk<PERSON> n usesteb", "UserName": "Isem n useqdac", "VarFrameRate": "Atug n tugna d ameskil", "Vertical": "<PERSON><PERSON><PERSON><PERSON>", "Video": "<PERSON><PERSON><PERSON>", "VideoEncoder": "Asettengal n uvidyu", "VideoSaved": "<PERSON><PERSON><PERSON>", "VideoSource": "<PERSON><PERSON><PERSON><PERSON>", "ViewCrashLogs": "<PERSON>ali aɣmis n uɣelluy", "ViewLicenses": "<PERSON><PERSON> turagin", "ViewOnGitHub": "W<PERSON> di GitHub", "WantToTranslate": "Te<PERSON>ɣiḍ ad tsuqqleḍ?", "WebCam": "Takamirat n web", "WebCamSeparateFile": "Record Webcam to separate file", "WebCamView": "Tamuɣli n webcam", "Website": "Asmel n web", "Window": "<PERSON><PERSON><PERSON><PERSON>", "Yes": "Ih"}