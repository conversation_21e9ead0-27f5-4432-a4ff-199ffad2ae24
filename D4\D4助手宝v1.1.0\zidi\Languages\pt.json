{"About": "Sobre", "AccentColor": "Accent Color", "Add": "Add", "AlwaysOnTop": "Sempre no Topo", "Audio": "<PERSON><PERSON><PERSON>", "AudioFormat": "Audio Format", "AudioSaved": "<PERSON><PERSON><PERSON>", "BackColor": "<PERSON><PERSON> <PERSON>", "BorderColor": "<PERSON><PERSON>", "BorderThickness": "<PERSON><PERSON><PERSON><PERSON>rda", "Bottom": "Parte Inferior", "CaptureDuration": "Duração da Captura (em segundos)", "Center": "Centro", "Changelog": "Log de alterações", "Clear": "Limpar", "ClearRecentList": "Limpar Lista Recente", "Clipboard": "Área de Transferência", "Close": "<PERSON><PERSON><PERSON>", "Color": "Cor", "ConfigCodecs": "Configure Codecs", "Configure": "Configurar", "CopyOutPathClipboard": "Copiar caminho do arquivo de saída para a área de transferência", "CopyPath": "<PERSON><PERSON><PERSON>", "CopyToClipboard": "Copiar para Área de Transferência", "CornerRadius": "Raio do Canto", "CrashLogs": "Crash Logs", "Crop": "Crop", "CustomSize": "Custom Size", "CustomUrl": "Custom Url", "DarkTheme": "Dark Theme", "Delete": "Excluir", "DiscardChanges": "Discard Changes", "Disk": "Disco", "Donate": "<PERSON><PERSON>", "DownloadFFmpeg": "Download FFmpeg", "Edit": "Edit", "Elapsed": "Elapsed", "ErrorOccurred": "Ocorreu um erro", "Exit": "<PERSON><PERSON>", "FFmpegFolder": "Pasta do FFmpeg", "FFmpegLog": "Log do FFmpeg", "FileMenu": "File", "FileMenuNew": "New", "FileMenuOpen": "Open", "FileMenuSave": "Save", "FileNaming": "File Naming", "Flip": "Inverter", "FontSize": "<PERSON><PERSON><PERSON>", "FrameRate": "FPS", "FullScreen": "Tela Cheia", "HideOnFullScreenShot": "Ocultar na Captura em Tela Cheia", "Horizontal": "Horizontalmente", "Host": "<PERSON><PERSON><PERSON>", "Hotkeys": "Teclas de Atalho", "ImageEditor": "Image Editor", "ImgEmpty": "<PERSON><PERSON> salvo, a imagem capturada estava vazia", "ImgFormat": "Formato de Imagem", "ImgSavedClipboard": "Imagem salva na área de transferência", "ImgurFailed": "Falha ao enviar para Imgur", "ImgurSuccess": "Enviado para Imgur com sucesso", "ImgurUploading": "Enviando para Imgur", "IncludeClicks": "Incluir Cliques do Mouse", "IncludeCursor": "<PERSON>luir <PERSON>", "IncludeKeys": "Incluir Teclas Pressionadas", "Keymap": "Keymap", "Keystrokes": "Teclas Pressionadas", "KeystrokesHistoryCount": "History Count", "KeystrokesHistorySpacing": "History Spacing", "KeystrokesSeparateFile": "Save to separate Text file", "Language": "Idioma", "Left": "E<PERSON>rda", "LoopbackSource": "Fonte de Saída do Alto-falante", "MaxRecent": "Máximo de itens para manter", "MaxTextLength": "Comprimento Máximo do Texto", "MicSource": "Fonte do Microfone", "MinCapture": "Minimizar ao Iniciar a Captura", "Minimize": "<PERSON><PERSON><PERSON>", "MinTray": "Minimizar para a Área de Notificação", "MinTrayStartup": "Minimize to Tray on Startup", "MinTrayClose": "Minimize to Tray when Closed", "MouseClicks": "Cliques do Mouse", "MouseMiddleClickColor": "Middle Click Color", "MousePointer": "<PERSON>", "MouseRightClickColor": "Right Click Color", "NewWindow": "NewWindow", "No": "Não", "None": "None", "Notifications": "Notifications", "NotSaved": "Não salvo", "NoWebcam": "Nenhuma Webcam", "Ok": "OK", "OnlyAudio": "<PERSON><PERSON>", "Opacity": "Opacity", "OpenFromClipboard": "Open from Clipboard", "OpenOutFolder": "<PERSON><PERSON>r Pasta de Saída", "OutFolder": "Pasta de Saída", "Overlays": "Overlays", "Padding": "Padding", "Password": "<PERSON><PERSON>", "Paused": "Gravação Pausada", "PauseResume": "Pausar | Continuar", "PauseResumeRecording": "Pausar/Continuar Gra<PERSON>ção", "PlayRecAudio": "Playback recorded audio in real-time", "Port": "Porta", "Preview": "Preview", "PreStartCountdown": "Pre Start Countdown (seconds)", "Proxy": "Proxy", "Quality": "Qualidade", "Radius": "Raio", "Recent": "<PERSON><PERSON>", "RecordStop": "Gravar | Parar", "Redo": "Redo", "Refresh": "<PERSON><PERSON><PERSON><PERSON>", "Region": "Região", "RegionSelector": "Selecionar Região", "RemoveFromList": "Remover da Lista", "Reset": "Redefinir", "Resize": "Redimensionar", "RestoreDefaults": "<PERSON><PERSON>", "Right": "<PERSON><PERSON><PERSON>", "Rotate": "<PERSON><PERSON><PERSON>", "SaveToClipboard": "Save to Clipboard", "Screen": "Tela", "ScreenShot": "Captura de Tela", "ScreenShotActiveWindow": "<PERSON><PERSON><PERSON>", "ScreenShotDesktop": "<PERSON><PERSON><PERSON>", "ScreenShotSaved": "<PERSON><PERSON>", "ScreenShotTransforms": "Transformações da Captura", "SelectFFmpegFolder": "Selecione a Pasta do FFmpeg", "SelectOutFolder": "Selecione a Pasta de Saída", "SeparateAudioFiles": "Separate files for every audio source", "ShowSysNotify": "Exibir Notificações na Área de Notificação", "SnapToWindow": "Snap to Window", "Sounds": "Sounds", "StartStopRecording": "Iniciar/Parar Gravação", "StreamingKeys": "Streaming Keys", "Timeout": "Tempo limite", "ToggleMouseClicks": "Toggle Mouse Clicks", "ToggleKeystrokes": "Toggle Keystrokes", "Tools": "Tools", "Top": "Parte Superior", "TrayIcon": "Tray Icon", "Trim": "<PERSON><PERSON>", "Undo": "Undo", "UploadToImgur": "Upload to Imgur", "UseProxyAuth": "Usar Autenticação de Proxy", "UserName": "Nome de Usuário", "VarFrameRate": "Taxa de Quadros Variável", "Vertical": "Verticalmente", "Video": "Vídeo", "VideoEncoder": "Codificador de Vídeo", "VideoSaved": "<PERSON><PERSON><PERSON><PERSON>", "VideoSource": "Fonte de Vídeo", "ViewCrashLogs": "View Crash Logs", "ViewLicenses": "View Licenses", "ViewOnGitHub": "View on GitHub", "WantToTranslate": "Quer ajudar a traduzir?", "WebCam": "Webcam", "WebCamSeparateFile": "Record Webcam to separate file", "WebCamView": "Visualização da Webcam", "Website": "Website", "Window": "<PERSON><PERSON>", "Yes": "<PERSON>m"}