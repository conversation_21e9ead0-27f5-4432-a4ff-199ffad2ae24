function()
    local e = aura_env
    if not e.lastRefresh or e.lastRefresh < GetTime() - e.refreshRate then
        e.lastRefresh = GetTime()
        
        local last = e.result or 0
        
        local counter = 0
        for i = 1, 40 do
            local unit = "nameplate"..i
            if UnitExists(unit) and not UnitIsFriend("player", unit) then
                local min, max = e.GetRange(unit)
                if min and max and min >= e.minRange and max <= e.maxRange then
                    counter = counter + 1 
                end
            end
        end
        if counter ~= last then
            WeakAuras.ScanEvents("WA_ENEMIES_IN_RANGE", counter)
        end
        e.result = counter
		if e.result > 2 then
		return true
		else
		return false
    end
end
end