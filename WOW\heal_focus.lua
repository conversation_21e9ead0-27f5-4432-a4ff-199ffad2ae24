-- Holding variable to assist with minimizing some chat spam later on
local last_target_before_clear

-- Localized Performance Variables
-- Stores a reference to the button frame we manipulate
local auraBtn
-- Store configs in a local variable to avoid referencing aura_env global a zillion times
local auraConfigs = aura_env.config
-- Stores player's class color so it doesn't need to recall these functions
local playerClassColor = select(4, GetClassColor(select(2, UnitClass("player"))))
-- Stores the UNKNOWNOBJECT globalstring to prevent global lookups
local UnkObject = UNKNOWNOBJECT


------- Custom Options -------

-- Holds the unit that will be selected as a target if we can't find one that matches our criteria
local defaultUnit = auraConfigs["defaultUnit"]
-- Enables some extra debugging information to the chat frame
local debugOn = auraConfigs["debugOn"]



--
-- Author: Proxying - https://github.com/Proxying
-- Idea from: Chi<PERSON> @ Draenor
-- Refactored by: <PERSON><PERSON> @ Area52
--
--

-- A wrapper function with debug announcing capabilities
local ButtonSetAttribute = function(button, attribute, value)
    button:SetAttribute(attribute, value)
    
    local debugString = table.concat({": Setting \"", attribute, "\" to \"", tostring(value), "\""})
    DebugPrint(table.concat({WrapTextInColorCode("DEBUG", playerClassColor), debugString}))
    if debugOn then
        print(table.concat({WrapTextInColorCode("SmartFocus DEBUG", playerClassColor), debugString}))
    end
end

-- Utility that allows you to input a table and a value, and it will return true if the value is in the table
local function TableContains(table, val)
    for i=1,#table do
        if table[i] == val then 
            return true
        end
    end
    return false
end


-- Main function
-- Parses through your group to look for members with specific conditions, then assigns that person to a buttonFrame, intended to be clicked via macro
aura_env.smart_focus = function()
    -- Localized performance variables
    local isInRaid = IsInRaid()
    
    -- Holds the unit that we'll eventually assign to the button
    local target_unit = nil
    -- Will be set to true if we are also looking for a specific priority target
    local priority_target = false
    -- Holds a target that satisfies all the basic criteria, but is not our priority target
    -- This target will be used if we don't end up finding our priority target
    local backup_target = nil
    local max_hp_leak = 0
    
    
    -- Only look for other people while in some form of group
    -- Note that if not in a group we'll still be checking the pet fallback and default unit down below
    if IsInGroup() then
        -- member_unit stores the literal "unit" of every group member
        for member_unit in WA_IterateGroupMembers() do
            -- To display names we need names, and the server doesn't always return them (thanks Blizzard)
            -- This method will end up re-running itself if it doesn't get a proper name at first
            if UnitNameUnmodified(member_unit) ~= UnkObject
            and UnitNameUnmodified(member_unit) ~= nil 
            and UnitExists(member_unit) and not UnitIsDead(member_unit) and UnitIsConnected(member_unit)
			and WeakAuras.CheckRange(member_unit, 40, "<=")
            then
				local hp = UnitHealth(member_unit)
				local max_hp = UnitHealthMax(member_unit)
				local hp_leak = max_hp-hp
				
				if hp_leak > max_hp_leak then                    
                    max_hp_leak = hp_leak
                    -- Returns e.g. "Kovi" and "Area52"
                    local member_name, member_realm = UnitNameUnmodified(member_unit)
                    
                    -- Unit-like name, will have its realm appended if it's on another realm just below
                    local memberUnitName = member_name
                    
                    -- If the unit is on another realm
                    if member_realm ~= nil then
                        -- Add the realm to its unit name
                        memberUnitName = table.concat({member_name, '-', member_realm})
                    else
                        -- Otherwise, input the player's realm name for use in the Priority Name Realm system
                        member_realm = GetNormalizedRealmName()
                    end
                    
                    target_unit = memberUnitName
                end
            end
        end
    end
    
    
    
    -- If the unit is the same as it already was, don't bother updating the button
    if auraBtn:GetAttribute("unit") ~= target_unit then
        -- If we're setting to the default target, don't bother alerting the user
        if target_unit == nil and auraBtn:GetAttribute("unit") ~= defaultUnit then
            ButtonSetAttribute(auraBtn, "unit", defaultUnit)
		end
        if target_unit then
            ButtonSetAttribute(auraBtn, "unit", target_unit)
        end
    end
	
end

-- If this button frame is not already taken
if SmartFocus == nil then
    -- Creates a frame with a custom name. If you have two copies of this weakaura with the same name, they'll probably fight each other.
    auraBtn = CreateFrame("Button", "SmartFocus", UIParent, "SecureActionButtonTemplate")
    -- If the frame name is taken, then use the one that already exists instead
else
    auraBtn = SmartFocus
end


-- Initialize the button to some default attributes.
ButtonSetAttribute(auraBtn, "type", "focus")
ButtonSetAttribute(auraBtn, "unit", defaultUnit)

-- Run the gauntlet once on load to initialize without requiring any special events
aura_env.smart_focus()












