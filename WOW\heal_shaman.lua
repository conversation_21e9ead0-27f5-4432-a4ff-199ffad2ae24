-- Holding variable to assist with minimizing some chat spam later on
local last_target_before_clear

-- Localized Performance Variables
-- Stores a reference to the button frame we manipulate
local auraBtn
-- Store configs in a local variable to avoid referencing aura_env global a zillion times
local auraConfigs = aura_env.config
-- Stores player's class color so it doesn't need to recall these functions
local playerClassColor = select(4, GetClassColor(select(2, UnitClass("player"))))
-- Stores the UNKNOWNOBJECT globalstring to prevent global lookups
local UnkObject = UNKNOWNOBJECT


------- Custom Options -------

-- Holds the unit that will be selected as a target if we can't find one that matches our criteria
local defaultUnit = auraConfigs["defaultUnit"]
-- Enables some extra debugging information to the chat frame
local debugOn = auraConfigs["debugOn"]
-- 治疗量
local RiptideAmount = auraConfigs["RiptideAmount"]
local HealingSurgeAmount = auraConfigs["HealingSurgeAmount"]
local ChainHealAmount = auraConfigs["ChainHealAmount"]

--
-- Author: Proxying - https://github.com/Proxying
-- Idea from: Chilly @ Draenor
-- Refactored by: Kovi @ Area52
--
--

-- A wrapper function with debug announcing capabilities
local ButtonSetAttribute = function(button, attribute, value)
    button:SetAttribute(attribute, value)
    
    local debugString = table.concat({": Setting \"", attribute, "\" to \"", tostring(value), "\""})
    DebugPrint(table.concat({WrapTextInColorCode("DEBUG", playerClassColor), debugString}))
    if debugOn then
        print(table.concat({WrapTextInColorCode("ShamanSmartHeal DEBUG", playerClassColor), debugString}))
    end
end

-- Print table for debug
local PrintTable = function(table_name)
	if table_name then
		for k, v in next, table_name do
			print("table_name[" .. tostring(k) .. "] = " .. tostring(v))
		end
	end
end

-- Main function
-- Parses through your group to look for members with specific conditions, then assigns that person to a buttonFrame, intended to be clicked via macro
aura_env.smart_heal = function()
    -- Localized performance variables  
    -- Holds the unit that we'll eventually assign to the button
    local target_unit = nil
	local target_spell = nil
    local max_hp_leak = 0
	local leak_count = 0
	local UnitSave
	local RiptideSpellCharges = C_Spell.GetSpellCharges('激流')
	local HealingStreamTotemCharges = C_Spell.GetSpellCharges('治疗之泉图腾')
	local RiptideCurrentCharges = RiptideSpellCharges.currentCharges 
	local RiptideBuffPlayer = C_UnitAuras.GetAuraDataBySpellName('player','潮汐奔涌','HELPFUL')
	local Totem4thInfo = C_TooltipInfo.GetTotem(4)
	local AncestralGuidanceStatus = C_Spell.GetSpellCooldown('先祖指引')
	local AscendanceStatus = C_Spell.GetSpellCooldown('升腾')
	local memberUnitName 

    local hp = UnitHealth('player')
	local max_hp = UnitHealthMax('player')
	local hp_leak = max_hp-hp	
    -- Only look for other people while in some form of group
    -- Note that if not in a group we'll still be checking the pet fallback and default unit down below
    if IsInGroup() then		
        -- member_unit stores the literal "unit" of every group member
        for member_unit in WA_IterateGroupMembers() do
            -- To display names we need names, and the server doesn't always return them (thanks Blizzard)
            -- This method will end up re-running itself if it doesn't get a proper name at first
            if UnitNameUnmodified(member_unit) ~= UnkObject
            and UnitNameUnmodified(member_unit) ~= nil 
            and UnitExists(member_unit) and not UnitIsDead(member_unit) and UnitIsConnected(member_unit)
			and WeakAuras.CheckRange(member_unit, 40, "<=")
            then
                hp = UnitHealth(member_unit)
				max_hp = UnitHealthMax(member_unit)
				hp_leak = max_hp-hp
				if hp_leak > tonumber(ChainHealAmount) then
					leak_count = leak_count + 1
				end
                if hp_leak > max_hp_leak then
                    
                    max_hp_leak = hp_leak
					UnitSave = member_unit
                    -- Returns e.g. "Kovi" and "Area52"
                    local member_name, member_realm = UnitNameUnmodified(member_unit)
                    
                    -- Unit-like name, will have its realm appended if it's on another realm just below
                    memberUnitName = member_name
                    
                    -- If the unit is on another realm
                    if member_realm ~= nil then
                        -- Add the realm to its unit name
                        memberUnitName = table.concat({member_name, '-', member_realm})
                    else
                        -- Otherwise, input the player's realm name for use in the Priority Name Realm system
                        member_realm = GetNormalizedRealmName()
                    end					
                end
			end
        end	
		-- 激流 -- 		
		if max_hp_leak > tonumber(RiptideAmount) and RiptideCurrentCharges>0 and not RiptideBuffPlayer then
            target_unit = memberUnitName
			target_spell = '61295'
		end		
		-- 涌 -- 
		if max_hp_leak > tonumber(HealingSurgeAmount) then
            target_unit = memberUnitName
			target_spell = '8004'
		end			
		if max_hp_leak > (tonumber(HealingSurgeAmount)*1.8+tonumber(RiptideAmount)) and RiptideCurrentCharges>0 and not RiptideBuffPlayer then
            target_unit = memberUnitName
			target_spell = '61295'
		end			
		-- 治疗链 -- 
		if leak_count >= 3 then
			target_unit = memberUnitName
			target_spell = '1064'
		end
		-- 治疗之泉图腾 -- 
		if leak_count >= 3 and HealingStreamTotemCharges.currentCharges > 0 and Totem4thInfo == nil then
			target_unit = memberUnitName
			target_spell = '5394'
		end		
		if leak_count >= 3 and max_hp_leak > (tonumber(ChainHealAmount)*1.8+tonumber(RiptideAmount)) and RiptideCurrentCharges>0 and not RiptideBuffPlayer then
			target_unit = memberUnitName
			target_spell = '61295'
		end
		-- 治疗之潮图腾 -- 
		if leak_count >= 3 and max_hp_leak > tonumber(ChainHealAmount)*3 and Totem4thInfo == nil then
			target_unit = memberUnitName
			target_spell = '108280'
		end
		-- 先祖指引 --
		if leak_count >= 3 and max_hp_leak > tonumber(ChainHealAmount)*5 and AncestralGuidanceStatus.duration == 0 then
			target_unit = memberUnitName
			target_spell = '108281'		
		end
		-- 升腾 --
		if leak_count >= 3 and max_hp_leak > tonumber(ChainHealAmount)*8 and AscendanceStatus.duration == 0 then
			target_unit = memberUnitName
			target_spell = '114052'		
		end		
    end
    
    
    -- If the unit is the same as it already was, don't bother updating the button
    if auraBtn:GetAttribute("unit") ~= target_unit then
        -- If we're setting to the default target, don't bother alerting the user
        if target_unit == nil and auraBtn:GetAttribute("unit") ~= defaultUnit then
            ButtonSetAttribute(auraBtn, "unit", defaultUnit)
		end
        if target_unit then
            ButtonSetAttribute(auraBtn, "unit", target_unit)
        end
    end
    if auraBtn:GetAttribute("spell") ~= target_spell then
        -- If we're setting to the default target, don't bother alerting the user
        ButtonSetAttribute(auraBtn, "spell", target_spell)
    end	
end

-- If this button frame is not already taken
if ShamanSmartHeal == nil then
    -- Creates a frame with a custom name. If you have two copies of this weakaura with the same name, they'll probably fight each other.
    auraBtn = CreateFrame("Button", "ShamanSmartHeal", UIParent, "SecureActionButtonTemplate")
    -- If the frame name is taken, then use the one that already exists instead
else
    auraBtn = ShamanSmartHeal
end


-- Initialize the button to some default attributes.
ButtonSetAttribute(auraBtn, "type", "spell")
ButtonSetAttribute(auraBtn, "unit", defaultUnit)

-- Run the gauntlet once on load to initialize without requiring any special events
aura_env.smart_heal()

