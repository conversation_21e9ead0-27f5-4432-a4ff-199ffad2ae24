-- Holding variable to assist with minimizing some chat spam later on
local last_target_before_clear

-- Localized Performance Variables
-- Stores a reference to the button frame we manipulate
local auraBtn
-- Store configs in a local variable to avoid referencing aura_env global a zillion times
local auraConfigs = aura_env.config
-- Stores player's class color so it doesn't need to recall these functions
local playerClassColor = select(4, GetClassColor(select(2, UnitClass("player"))))
-- Stores the UNKNOWNOBJECT globalstring to prevent global lookups
local UnkObject = UNKNOWNOBJECT
local misdirectName = C_Spell.GetSpellInfo(34477).name
local interlopeName = C_Spell.GetSpellInfo(248518).name
local tricksName = C_Spell.GetSpellInfo(57934).name

------- Custom Options -------
-- Holds a player's character name. Can be used without worrying about server names
local priorityTargetName = auraConfigs["priorityTankName"]
-- If specified, can be used to more precisely select a character, in case there are two people with the same name
local priorityTargetNameServer = auraConfigs["priorityTankNameServer"]:gsub("%s+", ""):gsub("-", "")
-- Holds the unit that will be selected as a target if we can't find one that matches our criteria
local defaultUnit = auraConfigs["defaultUnit"]
-- Boolean that determines whether the player's pet (if they have one) will be picked as a target unit instead of the defaultUnit
local petFallback =  auraConfigs["petFallback"]
-- Enables some extra debugging information to the chat frame
local debugOn = auraConfigs["debugOn"]

-- Interlope doesn't work when you cast Interlope, it only works when you cast Misdirection, so we store the displayed spell here. I fucking hate blizzard.
local spellName

-- Holds a user's selection of Main Tank status criteria
-- Notably contains a "NOT" option, in order to help people who want to pick the non-MT in raids
local customMainTankStatus = {}
for i=1,#auraConfigs["customMainTankStatus"] do
    if auraConfigs["customMainTankStatus"][i] == true then
        if i == 1 then customMainTankStatus[#customMainTankStatus+1] = true -- MAINTANK
        elseif i == 2 then customMainTankStatus[#customMainTankStatus+1] = false -- NOT MAINTANK
        end
    end
end

--
-- Author: Proxying - https://github.com/Proxying
-- Idea from: Chilly @ Draenor
-- Refactored by: Kovi @ Area52
--
--

-- A wrapper function with debug announcing capabilities
local ButtonSetAttribute = function(button, attribute, value)
    button:SetAttribute(attribute, value)
    
    local debugString = table.concat({": Setting \"", attribute, "\" to \"", tostring(value), "\""})
    DebugPrint(table.concat({WrapTextInColorCode("DEBUG", playerClassColor), debugString}))
    if debugOn then
        print(table.concat({WrapTextInColorCode("SmartMisdirect DEBUG", playerClassColor), debugString}))
    end
end

-- Utility that allows you to input a table and a value, and it will return true if the value is in the table
local function TableContains(table, val)
    for i=1,#table do
        if table[i] == val then 
            return true
        end
    end
    return false
end

-- Dynamically switches the spell to Interlope or Misdirect, then updates the button
aura_env.setInterlopeOrMisdirect = function()
    local oldSpellName = spellName
    spellName = IsPlayerSpell(248518) and interlopeName or misdirectName -- Set displayed spell name to "Interlope" if we know Interlope, or "Misdirect" if we don't
    
    if oldSpellName ~= nil and #oldSpellName ~= 0 and oldSpellName ~= spellName then
        print(table.concat({WrapTextInColorCode("SmartMisdirect", playerClassColor), " updated to use ", WrapTextInColorCode(spellName, playerClassColor)}))
    end
    
    aura_env.smart_find_target()
end

-- Main function
-- Parses through your group to look for members with specific conditions, then assigns that person to a buttonFrame, intended to be clicked via macro
aura_env.smart_find_target = function()
    -- Localized performance variables
    local isInRaid = IsInRaid()
    
    -- Holds the unit that we'll eventually assign to the button
    local target_unit = nil
    -- Will be set to true if we are also looking for a specific priority target
    local priority_target = false
    -- Holds a target that satisfies all the basic criteria, but is not our priority target
    -- This target will be used if we don't end up finding our priority target
    local backup_target = nil
    
    -- Enable priority target mode if the user input a string into the custom option
    if #priorityTargetName ~= 0 then
        priority_target = true
    end
    
    -- Only look for other people while in some form of group
    -- Note that if not in a group we'll still be checking the pet fallback and default unit down below
    if IsInGroup() then
        -- member_unit stores the literal "unit" of every group member
        for member_unit in WA_IterateGroupMembers() do
            -- To display names we need names, and the server doesn't always return them (thanks Blizzard)
            -- This method will end up re-running itself if it doesn't get a proper name at first
            if UnitNameUnmodified(member_unit) ~= UnkObject
            and UnitNameUnmodified(member_unit) ~= nil 
            and UnitGroupRolesAssigned(member_unit) == "TANK"
            -- Raiding-only functionality. This is only checked while in raid.
            -- Intended to help people who want weakauras that work more precisely in raids, but are not limited to only working while in raid
            -- If the user didn't select any MainTank statuses as criteria, or if the target has one of the correct MainTank statuses
            and (not isInRaid or #customMainTankStatus == 0 or (isInRaid and TableContains(customMainTankStatus, GetPartyAssignment("MAINTANK", member_unit))))
            then
                -- Returns e.g. "Kovi" and "Area52"
                local member_name, member_realm = UnitNameUnmodified(member_unit)
                
                -- Unit-like name, will have its realm appended if it's on another realm just below
                local memberUnitName = member_name
                
                -- If the unit is on another realm
                if member_realm ~= nil then
                    -- Add the realm to its unit name
                    memberUnitName = table.concat({member_name, '-', member_realm})
                else
                    -- Otherwise, input the player's realm name for use in the Priority Name Realm system
                    member_realm = GetNormalizedRealmName()
                end
                
                -- If the user has a priority target, check if the target is our priority target, and if the user specifies a target server, then it will be checked as well
                -- Further scanning will stop as we've already found our priority target
                if priority_target and priorityTargetName == member_name and (#priorityTargetNameServer == 0 or priorityTargetNameServer == member_realm) then
                    target_unit = memberUnitName
                    break
                    -- If the user has a priority target but the target is not our priority target, store the unit here for now
                    -- If we find the priority target later, we'll ignore this backup target. If we don't, we can use it
                elseif priority_target then
                    backup_target = memberUnitName
                    -- If the user doesn't specify a priority target, then we'll use this target
                    -- Further scanning will stop as we've already found our priority target
                else
                    target_unit = memberUnitName
                    break
                end
            end
        end
    end
    
    -- If we scanned the entire group without finding a target that has 100% confidence, we'll figure out where to direct the spell instead
    if target_unit == nil then
        -- If there's a backup_target, we'll set to that instead (this will be very common if there's a priority target specified that's not in the group)
        if backup_target == nil then
            -- If there's no backup_target, we'll check whether the pet has priority and whether the player even has a pet
            if petFallback and UnitNameUnmodified("pet") ~= nil then
                target_unit = IsPlayerSpell(248518) and "player" or "pet" -- Set to Player if we know Interlope, else Pet
            end
        else
            target_unit = backup_target
        end
    end
    
    -- If the unit is the same as it already was, don't bother updating the button
    if auraBtn:GetAttribute("unit") ~= target_unit then
        -- If we're setting to the default target, don't bother alerting the user
        if target_unit == nil then
            ButtonSetAttribute(auraBtn, "unit", defaultUnit)
        else
            ButtonSetAttribute(auraBtn, "unit", target_unit)
            
            -- last_target_before_clear and this gate are used to limit spamming when you have a pet as your tank and mount/land repeatedly
            -- (which flips the unit from pet to defaultUnit to pet to defaultUnit)
            if(last_target_before_clear ~= target_unit) then
                local coloredSpellName = WrapTextInColorCode(spellName, playerClassColor)
                local coloredTargetName = WrapTextInColorCode(UnitNameUnmodified(target_unit), select(4,GetClassColor(select(2,UnitClass(target_unit)))))
                -- Alert the user that something has changed
                print(table.concat({coloredSpellName, " updated with ", coloredTargetName, " as target"}))
            end
            last_target_before_clear = target_unit
        end
    end
end

-- If this button frame is not already taken
if SmartMisdirect == nil then
    -- Creates a frame with a custom name. If you have two copies of this weakaura with the same name, they'll probably fight each other.
    auraBtn = CreateFrame("Button", "SmartMisdirect", UIParent, "SecureActionButtonTemplate")
    -- If the frame name is taken, then use the one that already exists instead
else
    auraBtn = SmartMisdirect
end


-- Initialize the button to some default attributes.
ButtonSetAttribute(auraBtn, "type", "spell")
ButtonSetAttribute(auraBtn, "unit", defaultUnit)

if select(3, UnitClass("player")) == 4 then 
-- 职业为盗贼
    ButtonSetAttribute(auraBtn, "spell", 57934)
    spellName = tricksName
else
-- 不然当猎人算
    ButtonSetAttribute(auraBtn, "spell", 34477)
    aura_env.setInterlopeOrMisdirect()
end

-- Initialize a metronome to send an event that we can listen to every 5 seconds
-- Used as circumvention for when a new unit should be chosen but the player is in combat (we're not allowed to change the target in combat)
-- In this case, a flag is set to find a new unit, but it won't be acted upon until out of combat and the metronome event happens
if not _G["METRONOME_5_SECONDS"] then 
    C_Timer.NewTicker(5, function() WeakAuras.ScanEvents("METRONOME_5_SECONDS") end)
    _G["METRONOME_5_SECONDS"] = true
end

-- Run the gauntlet once on load to initialize without requiring any special events
aura_env.smart_find_target()


--
-- Author: Proxying - https://github.com/Proxying
-- Idea from: Chilly @ Draenor
-- Refactored by: Kovi @ Area52
--
--

function(event)
    if event ~= "METRONOME_5_SECONDS" then
        if event == "GROUP_ROSTER_UPDATE"
        or event == "PLAYER_SPECIALIZATION_CHANGED"
        or event == "UNIT_PET"
        or event == "PET_BAR_UPDATE"
        or event == "PLAYER_ENTERING_WORLD" then
            aura_env.update_required = true
            -- Interlope PVP talent handling
        elseif (event == "PVP_WORLDSTATE_UPDATE" or event == "PLAYER_PVP_TALENT_UPDATE" or "PVP_TIMER_UPDATE") and select(3, UnitClass("player")) == 3 then
            aura_env.setInterlopeOrMisdirect()
        end
    end
    
    if aura_env.update_required and not InCombatLockdown() then
        aura_env.smart_find_target()
        aura_env.update_required = false
    end
end

function()
    -- Limit scan rate to improve performance
    if GetTime()-(aura_env.LastScan or 0) < 0.3 then return false end 
    aura_env.LastScan=GetTime()
    
    local cur_hp, cur_pid, hp, pid
    for i=1, GetNumGroupMembers() do
        pid="raid"..i
        if UnitExists(pid) and not UnitIsDead(pid) and UnitIsConnected(pid) then
            hp=UnitHealth(pid)
            if not cur_pid or (hp or 0) < (cur_hp or 0) then
                cur_pid=pid
                cur_hp=hp
            end
        end
    end
    
    if cur_pid then
        aura_env.name=string.sub(UnitName(cur_pid),0,6)
        aura_env.cur=cur_hp
        aura_env.max=UnitHealthMax(cur_pid)
        local c=RAID_CLASS_COLORS[select(2,UnitClass(cur_pid))]
        aura_env.colors={r=c.r,g=c.g,b=c.b}
        return true
    end
end











