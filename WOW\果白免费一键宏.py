#author:果白白
import tkinter as tk
from tkinter import simpledialog, messagebox, filedialog
import pyautogui

class ColorPickerGUI:
    def __init__(self, master):
        self.master = master
        self.master.title("取色绑定键位生成AHK--BY果白白")

        self.label = tk.Label(master, text="鼠标移动到指定位置然后按 Enter.")
        self.label.pack()

        self.pick_color_btn = tk.But<PERSON>(master, text="取色（按Enter）", command=self.pick_color_and_bind_key)
        self.pick_color_btn.pack()

        # 给按钮设置焦点以便可以通过键盘事件触发
        self.pick_color_btn.focus_set()

        # 绑定回车键到pick_color函数
        self.master.bind('<Return>', lambda event: self.pick_color_and_bind_key())

        self.close_btn = tk.But<PERSON>(master, text="完成并生成ahk脚本", command=self.finish_and_generate_script)
        self.close_btn.pack()

        self.color_bindings = []

    def pick_color_and_bind_key(self):
        # Capture the color at the current mouse position and its surrounding pixels
        x, y = pyautogui.position()
        colors = self.get_surrounding_colors(x, y)
        # Ask for the key to bind with these colors
        key = simpledialog.askstring("按键绑定", "输入想要绑定的按键:", parent=self.master)
        if key:
            self.color_bindings.append({"colors": colors, "key": key})
            messagebox.showinfo("Color Picked and Key Bound", f"Colors at ({x}, {y}) and surrounding bound to key: {key}")

    def get_surrounding_colors(self, x, y):
        offsets = [(0, 0), (-3, 0), (3, 0), (0, -3), (0, 3)]
        colors = []
        for dx, dy in offsets:
            color = pyautogui.pixel(x + dx, y + dy)
            # Convert color to hex and take the upper 4 digits
            color_hex = ''.join([format(c, '02X') for c in color])[-4:]
            colors.append({"x": x + dx, "y": y + dy, "color": color_hex})
        return colors

    def finish_and_generate_script(self):
        filename = filedialog.asksaveasfilename(defaultextension=".ahk", filetypes=[("AutoHotkey Scripts", "*.ahk")], parent=self.master)
        if filename:
            with open(filename, 'w') as file:
                file.write(";果白免费一键宏，爱发电：https://afdian.net/a/wowahk \n#NoEnv\n#Warn\nSendMode Input\nSetWorkingDir %A_ScriptDir%\n\nXButton1::\nSetTimer,CheckColors, 0\nKeyWait, XButton1\nSetTimer,CheckColors, Off\nReturn\n\nCheckColors:\n")
                for binding in self.color_bindings:
                    conditions = " and ".join([f'(GetColor({color["x"]},{color["y"]})=="{color["color"]}")' for color in binding["colors"]])
                    file.write(f"    if {conditions}:\n")
                    file.write(f"    {{\n        pyautogui.press('{{{binding['key']}}}')\n    }}\n")
                file.write("Return\n\nGetColor(x,y)\n{\n    CoordMode, Pixel, Screen\n    PixelGetColor, color, x, y, RGB\n    StringRight, color, color, 4\n    return color\n}\n")
            messagebox.showinfo("Success", "AHK script generated successfully!")

if __name__ == "__main__":
    root = tk.Tk()
    my_gui = ColorPickerGUI(root)
    root.mainloop()
