import json
import os
import logging

# 配置日志
logger = logging.getLogger("WoWHelper")

# 常量定义
CONFIG_FILE = "wow_helper_config.json"

# 默认配置
DEFAULT_CONFIG = {
    "debug_mode": False,
    "advanced_image_recognition": False,
    "screenshot_method": "PIL",
    "input_delay": {
        "min": 30,
        "max": 100
    },
    "ui_position": {"x": 250, "y": 250},
    "ui_colors": {
        "running": "red",
        "holding": "blue",
        "debug": "green"
    },
    "sample_interval": {
        "min": 101,
        "max": 1111,
        "weights": [
            {"range": [101, 202], "weight": 0.6},
            {"range": [202, 301], "weight": 0.3},
            {"range": [701, 1111], "weight": 0.1}
        ]
    },
    "class_detection": {
        "Paladin_Retribution": {
            "position": [454, 842],
            "color": "7588",
            "nearby_offsets": [[-3, 0], [3, 0], [0, -3], [0, 3]],
            "nearby_colors": ["4A7E", "CFEB", "6E83", "1E37"]
        },
        "Shaman_Elemental": {
            "position": [455, 844],
            "color": "2DDC",
            "nearby_offsets": [[-3, 0], [3, 0], [0, -3], [0, 3]],
            "nearby_colors": ["067D", "0E89", "86B7", "0A30"]
        },
        "Shaman_Restoration": {
            "position": [455, 843],
            "color": "6F33",
            "nearby_offsets": [[-3, 0], [3, 0], [0, -3], [0, 3]],
            "nearby_colors": ["140C", "0300", "9355", "A71D"]
        },
        "Rogue_Outlaw": {
            "position": [453, 843],
            "color": "5070",
            "nearby_offsets": [[-3, 0], [3, 0], [0, -3], [0, 3]],
            "nearby_colors": ["6F7E", "8F59", "715A", "9357"]
        },
        "Deathknight_Unholy": {
            "position": [454, 843],
            "color": "AD75",
            "nearby_offsets": [[-3, 0], [3, 0], [0, -3], [0, 3]],
            "nearby_colors": ["3B22", "BF6F", "BD6B", "FBB5"]
        },
        "Rogue_Assassination": {
            "position": [454, 844],
            "color": "C0BE",
            "nearby_offsets": [[-3, 0], [3, 0], [0, -3], [0, 3]],
            "nearby_colors": ["C290", "0605", "0503", "1008"]
        },
        "Shaman_Enhancement": {
            "position": [454, 844],
            "color": "6824",
            "nearby_offsets": [[-3, 0], [3, 0], [0, -3], [0, 3]],
            "nearby_colors": ["539C", "DD98", "D7FF", "1D05"]
        },
        "Priest_Discipline": {
            "position": [454, 844],
            "color": "E3DF",
            "nearby_offsets": [[-3, 0], [3, 0], [0, -3], [0, 3]],
            "nearby_colors": ["EDEB", "6E52", "F5F2", "CDD2"]
        }
    }
}

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: str = CONFIG_FILE):
        self.config_file = config_file
        self.config = self._load_config()
    
    def _load_config(self) -> dict:
        """加载配置文件，如果不存在则创建默认配置"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    logger.info("已加载配置文件")
                    return config
            else:
                # 创建默认配置文件
                with open(self.config_file, 'w', encoding='utf-8') as f:
                    json.dump(DEFAULT_CONFIG, f, indent=4)
                    logger.info("已创建默认配置文件")
                return DEFAULT_CONFIG
        except Exception as e:
            logger.error(f"加载配置失败: {e}")
            return DEFAULT_CONFIG
    
    def save_config(self) -> None:
        """保存当前配置到文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=4)
                logger.info("配置已保存")
        except Exception as e:
            logger.error(f"保存配置失败: {e}")
    
    def update_config(self, new_config: dict) -> None:
        """更新配置"""
        self.config.update(new_config)
        self.save_config()
