import pywintypes
import json
import random
import time
import tkinter as tk
from tkinter import ttk, messagebox
import sys
import os
import logging
import queue
import importlib
import numpy as np
from threading import Timer, Thread, Lock
from dataclasses import dataclass
from typing import List, Tuple, Dict, Callable, Optional, Union, Any
from rotation_handler import ClassRotationHandler

# 设置日志系统
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("wow_helper.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("WoWHelper")

# 导入PIL进行截屏
try:
    from PIL import ImageGrab
    logger.info("成功导入PIL")
except ImportError:
    try:
        import subprocess
        logger.info("正在安装PIL...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pillow"])
        from PIL import ImageGrab
        logger.info("已安装并导入PIL")
    except Exception as e:
        logger.error(f"无法安装PIL: {e}")
        logger.error("请手动运行: pip install pillow")
        sys.exit(1)

# 导入pynput模块
try:
    from pynput import keyboard
    from pynput import mouse
    logger.info("成功导入pynput")
except ImportError:
    try:
        import subprocess
        logger.info("正在安装pynput...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "pynput"])
        from pynput import keyboard
        from pynput import mouse
        logger.info("已安装并导入pynput")
    except Exception as e:
        logger.error(f"无法安装pynput: {e}")
        logger.error("请手动运行: pip install pynput")
        sys.exit(1)

# 尝试导入OpenCV进行高级图像处理
try:
    import cv2
    HAS_OPENCV = True
    logger.info("成功导入OpenCV")
except ImportError:
    try:
        import subprocess
        logger.info("正在安装OpenCV...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "opencv-python"])
        import cv2
        HAS_OPENCV = True
        logger.info("已安装并导入OpenCV")
    except Exception as e:
        logger.warning(f"无法安装OpenCV: {e}")
        logger.warning("某些高级图像识别功能将不可用")
        HAS_OPENCV = False

# 导入配置管理器
try:
    sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))
    from config import ConfigManager, DEFAULT_CONFIG, CONFIG_FILE
    logger.info("成功导入配置管理器")
except ImportError:
    # 如果导入失败，回退到内联版本
    logger.warning("无法导入配置模块，使用内联版本")
    CONFIG_FILE = "wow_helper_config.json"
    
    # 定义默认配置
    DEFAULT_CONFIG = {
        "debug_mode": False,
        "ui_position": {"x": 100, "y": 100},
        "ui_colors": {
            "running": "#00FF00",
            "holding": "#FF9900",
            "debug": "#FF0000"
        },
        "input_delay": {
            "min": 30,
            "max": 100
        },
        "advanced_image_recognition": False,
        "sample_interval": {
            "weights": [
                {"range": [30, 50], "weight": 10},
                {"range": [50, 100], "weight": 5},
                {"range": [100, 200], "weight": 1}
            ]
        },
        "class_detection": {
            "priest_discipline": {
                "position": [1100, 600],
                "color": "FFCC",
                "nearby_offsets": [[3, 0], [-3, 0], [0, 3], [0, -3]],
                "nearby_colors": ["FFAA", "FFAA", "FFAA", "FFAA"]
            },
            "shaman_restoration": {
                "position": [1100, 601],
                "color": "5F63",
                "nearby_offsets": [[3, 0], [-3, 0], [0, 3], [0, -3]],
                "nearby_colors": ["666B", "CDD0", "8B8A", "545A"]
            }
        }
    }
    
    class ConfigManager:
        """配置管理器"""
        
        def __init__(self, config_file: str = CONFIG_FILE):
            self.config_file = config_file
            self.config = self._load_config()
        
        def _load_config(self) -> dict:
            """加载配置文件，如果不存在则创建默认配置"""
            try:
                if os.path.exists(self.config_file):
                    with open(self.config_file, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                        logger.info("已加载配置文件")
                        return config
                else:
                    # 创建默认配置文件
                    self.config = DEFAULT_CONFIG
                    self.save_config()
                    logger.info("已创建默认配置文件")
                    return DEFAULT_CONFIG
            except Exception as e:
                logger.error(f"加载配置失败: {e}")
                # 配置加载失败时，使用默认配置
                logger.info("使用默认配置")
                return DEFAULT_CONFIG
        
        def save_config(self) -> None:
            """保存当前配置到文件"""
            try:
                with open(self.config_file, 'w', encoding='utf-8') as f:
                    json.dump(self.config, f, indent=4)
                    logger.info("配置已保存")
            except Exception as e:
                logger.error(f"保存配置失败: {e}")
        
        def update_config(self, new_config: dict) -> None:
            """更新配置"""
            self.config.update(new_config)
            self.save_config()
        
        def get(self, key, default=None):
            """获取配置值"""
            return self.config.get(key, default)

# 其他常量定义
PLUGINS_DIR = "plugins"
UI_REFRESH_RATE = 60  # 每秒刷新次数
MAX_QUEUE_SIZE = 100  # 事件队列最大长度

@dataclass
class Point:
    x: int
    y: int

class Event:
    """事件类，用于在线程间传递消息"""
    
    TYPE_KEYPRESS = "keypress"
    TYPE_MOUSECLICK = "mouseclick"
    TYPE_MOUSEMOVE = "mousemove"
    TYPE_SCREENSHOT = "screenshot"
    TYPE_UI_UPDATE = "ui_update"
    
    def __init__(self, type: str, data: Any = None):
        self.type = type
        self.data = data
        self.timestamp = time.time()

class ScreenReader:
    """屏幕读取和颜色检测类，支持多种截图方法和高级图像处理"""
    
    def __init__(self, config, event_queue):
        self.config = config
        self.event_queue = event_queue
        
        # 截图缓存
        self._last_screenshot = None
        self._last_screenshot_time = 0
        self._screenshot_cache_time = 0.05  # 50ms内复用截图
        self._screenshot_lock = Lock()
        
        # 高级图像处理设置
        self.use_advanced_recognition = config.get("advanced_image_recognition", False) and HAS_OPENCV
        if self.use_advanced_recognition:
            logger.info("已启用高级图像识别功能")
        
        # 初始化模板匹配
        self._templates = {}
        self._load_templates()
    
    def _load_templates(self):
        """加载用于模板匹配的图像模板"""
        if not self.use_advanced_recognition:
            return
            
        templates_dir = "templates"
        if not os.path.exists(templates_dir):
            os.makedirs(templates_dir)
            logger.info(f"已创建模板目录: {templates_dir}")
            return
            
        try:
            for filename in os.listdir(templates_dir):
                if filename.endswith(('.png', '.jpg', '.jpeg')):
                    template_name = os.path.splitext(filename)[0]
                    template_path = os.path.join(templates_dir, filename)
                    self._templates[template_name] = cv2.imread(template_path, cv2.IMREAD_UNCHANGED)
                    logger.info(f"已加载模板: {template_name}")
        except Exception as e:
            logger.error(f"加载模板失败: {e}")
    
    def get_screenshot(self) -> Any:
        """获取当前屏幕截图"""
        with self._screenshot_lock:
            current_time = time.time()
            
            # 如果缓存过期，重新截图
            if current_time - self._last_screenshot_time > self._screenshot_cache_time or self._last_screenshot is None:
                try:
                    self._last_screenshot = ImageGrab.grab()
                    self._last_screenshot_time = current_time
                    
                    # 通知其他线程已获取新截图
                    self.event_queue.put(Event(Event.TYPE_SCREENSHOT, self._last_screenshot))
                except Exception as e:
                    logger.error(f"截图失败: {e}")
                    return None
            
            return self._last_screenshot
    
    def get_color(self, x: int, y: int) -> str:
        """获取屏幕特定位置的颜色"""
        try:
            screenshot = self.get_screenshot()
            if screenshot is None:
                return "ERROR"
                
            color = screenshot.getpixel((x, y))
            
            # 转换为十六进制并取后4位
            color_hex = ''.join([format(c, '02X') for c in color[:3]])[-4:]
            
            # 调试模式下输出像素颜色信息
            if self.config.get("debug_mode", False):
                logger.debug(f"位置 ({x}, {y}) 的颜色: RGB{color} -> HEX: {color_hex}")
                
            return color_hex
        except Exception as e:
            logger.error(f"无法获取屏幕像素颜色: {e}")
            return "ERROR"
    
    def color_check(self, x: int, y: int, ref: str) -> Union[List[int], bool]:
        """检查指定位置及其周围的颜色是否匹配参考颜色"""
        if (self.get_color(x, y) == ref and 
            self.get_color(x - 3, y) == ref and 
            self.get_color(x + 3, y) == ref and
            self.get_color(x, y - 3) == ref and 
            self.get_color(x, y + 3) == ref):
            return [x, y]
        return False
    
    def find_template(self, template_name: str, threshold: float = 0.8) -> Tuple[bool, Tuple[int, int]]:
        """使用模板匹配在屏幕上查找特定图像"""
        if not self.use_advanced_recognition or template_name not in self._templates:
            return False, (0, 0)
            
        try:
            screenshot = self.get_screenshot()
            if screenshot is None:
                return False, (0, 0)
                
            # 将PIL图像转换为OpenCV格式
            screenshot_cv = cv2.cvtColor(np.array(screenshot), cv2.COLOR_RGB2BGR)
            template = self._templates[template_name]
            
            # 执行模板匹配
            result = cv2.matchTemplate(screenshot_cv, template, cv2.TM_CCOEFF_NORMED)
            _, max_val, _, max_loc = cv2.minMaxLoc(result)
            
            if max_val >= threshold:
                # 返回中心点坐标
                h, w = template.shape[:2]
                center_x = max_loc[0] + w // 2
                center_y = max_loc[1] + h // 2
                logger.info(f"找到模板 {template_name}: 位置 {center_x}, {center_y}, 相似度 {max_val:.2f}")
                return True, (center_x, center_y)
            else:
                logger.debug(f"未找到模板 {template_name}, 最高相似度 {max_val:.2f}")
                return False, (0, 0)
        except Exception as e:
            logger.error(f"模板匹配失败: {e}")
            return False, (0, 0)
    
    def heal_raid_frames_check(self) -> Tuple[str, List, List, List]:
        """检查治疗团队框架的状态"""
        # 颜色定义
        red = '0011'
        yellow = 'FF00'
        blue = '1EFF'

        # 外层循环变量
        start_x = 100
        increment_x = 70
        iterations_x = 8

        # 内层循环变量
        start_y = 575
        increment_y = 35
        iterations_y = 5

        red_list = []
        yellow_list = []
        blue_list = []
        st_aoe = ''

        # 双层循环检查
        for i in range(iterations_x):
            x_value = start_x + i * increment_x
            for j in range(iterations_y):
                y_value = start_y + j * increment_y
                res = self.color_check(x_value, y_value, red)
                if res:
                    red_list.append([res[0] + 20, res[1]])
                res = self.color_check(x_value, y_value, yellow)
                if res:
                    yellow_list.append([res[0] + 20, res[1]])
                res = self.color_check(x_value, y_value, blue)
                if res:
                    blue_list.append([res[0] + 20, res[1]])

        # 根据检测结果决定行动类型
        length = len(red_list) + len(yellow_list) + len(blue_list)
        if length >= 3:
            st_aoe = 'AOE'
        elif length == 1:
            st_aoe = 'ST'
        elif length == 0:
            st_aoe = 'STOP'
        
        return st_aoe, red_list, yellow_list, blue_list

class InputController:
    """输入控制类，处理键盘和鼠标输入，支持人性化输入模拟"""
    
    def __init__(self, config, event_queue):
        self.keyboard_controller = keyboard.Controller()
        self.mouse_controller = mouse.Controller()
        self.config = config
        self.event_queue = event_queue
        self.input_lock = Lock()
    
    def _get_random_delay(self) -> float:
        """生成随机延迟，模拟人类操作"""
        min_delay = self.config.get("input_delay", {}).get("min", 30)
        max_delay = self.config.get("input_delay", {}).get("max", 100)
        return random.uniform(min_delay, max_delay) / 1000.0
    
    def press_key(self, key: str) -> None:
        """模拟按键，加入随机延迟使操作更像人类"""
        with self.input_lock:
            try:
                # 生成随机延迟
                delay = self._get_random_delay()
                
                # 处理函数键和特殊键
                if key.startswith('f') and key[1:].isdigit():
                    # 处理功能键 (F1-F12)
                    f_num = int(key[1:])
                    if 1 <= f_num <= 12:
                        special_key = getattr(keyboard.Key, f'f{f_num}')
                        
                        # 记录事件到队列
                        self.event_queue.put(Event(Event.TYPE_KEYPRESS, {'key': key}))
                        
                        # 按下并释放按键，加入随机延迟
                        self.keyboard_controller.press(special_key)
                        time.sleep(delay)
                        self.keyboard_controller.release(special_key)
                        logger.info(f"按下功能键: {key}, 延迟: {delay*1000:.1f}ms")
                        return
                
                # 处理其他特殊按键
                special_keys = {
                    'esc': keyboard.Key.esc,
                    'tab': keyboard.Key.tab,
                    'shift': keyboard.Key.shift,
                    'ctrl': keyboard.Key.ctrl,
                    'alt': keyboard.Key.alt,
                    'space': keyboard.Key.space,
                    'enter': keyboard.Key.enter
                }
                
                if key in special_keys:
                    # 记录事件到队列
                    self.event_queue.put(Event(Event.TYPE_KEYPRESS, {'key': key}))
                    
                    # 按下并释放按键，加入随机延迟
                    self.keyboard_controller.press(special_keys[key])
                    time.sleep(delay)
                    self.keyboard_controller.release(special_keys[key])
                    logger.info(f"按下按键: {key}, 延迟: {delay*1000:.1f}ms")
                    return
                    
                # 处理常规按键
                # 记录事件到队列
                self.event_queue.put(Event(Event.TYPE_KEYPRESS, {'key': key}))
                
                # 按下并释放按键，加入随机延迟
                self.keyboard_controller.press(key)
                time.sleep(delay)
                self.keyboard_controller.release(key)
                logger.info(f"按下按键: {key}, 延迟: {delay*1000:.1f}ms")
                
            except Exception as e:
                logger.error(f"按键模拟失败: {key}, 错误: {e}")
                # 尝试备用方法
                try:
                    # 对于单个字符的按键
                    if len(key) == 1:
                        self.keyboard_controller.press(key)
                        time.sleep(delay)
                        self.keyboard_controller.release(key)
                        logger.info(f"备用方法按下按键: {key}, 延迟: {delay*1000:.1f}ms")
                    else:
                        logger.error(f"无法按下按键: {key}")
                except Exception as e2:
                    logger.error(f"备用方法也失败: {e2}")

    def move_mouse(self, x: int, y: int, duration: float = None, human_like: bool = True) -> None:
        """
        移动鼠标到指定位置

        Args:
            x: 目标X坐标
            y: 目标Y坐标
            duration: 移动持续时间（秒）。None时会根据距离自动计算
            human_like: True为启用人性化移动模拟，False为立即移动（无延迟）
        """
        with self.input_lock:
            try:
                # 记录事件到队列
                self.event_queue.put(Event(Event.TYPE_MOUSEMOVE, {'x': x, 'y': y}))

                # 直接移动模式 - 没有延迟
                if not human_like:
                    self.mouse_controller.position = (x, y)
                    logger.debug(f"鼠标直接移动到: {x}, {y}")
                    return

                # 人性化移动逻辑
                # 如果未指定持续时间，则根据距离生成一个合理的值
                if duration is None:
                    current_pos = self.mouse_controller.position
                    distance = ((current_pos[0] - x) ** 2 + (current_pos[1] - y) ** 2) ** 0.5
                    duration = min(1.0, max(0.1, distance / 1000.0))

                # 获取当前位置
                current_x, current_y = self.mouse_controller.position

                # 生成平滑路径
                steps = int(duration * 60)  # 60fps
                if steps <= 1:
                    self.mouse_controller.position = (x, y)
                else:
                    for i in range(1, steps + 1):
                        # 使用缓动函数使移动看起来更自然
                        t = i / steps
                        ease_t = t * t * (3 - 2 * t)  # 缓动函数

                        new_x = current_x + (x - current_x) * ease_t
                        new_y = current_y + (y - current_y) * ease_t

                        self.mouse_controller.position = (int(new_x), int(new_y))
                        time.sleep(duration / steps)

                logger.info(f"鼠标移动到: {x}, {y}, 持续时间: {duration:.2f}s")

            except Exception as e:
                logger.error(f"鼠标移动失败: {e}")

    def click_mouse(self, x: int, y: int, button: str = 'left', human_like: bool = True) -> None:
        """在指定位置点击鼠标，可选择是否使用人性化移动"""
        with self.input_lock:
            try:
                # 保存当前鼠标位置
                current_pos = self.mouse_controller.position
                
                # 移动到目标位置
                self.move_mouse(x, y, human_like=human_like)
                
                # 记录事件到队列
                self.event_queue.put(Event(Event.TYPE_MOUSECLICK, {'x': x, 'y': y, 'button': button}))
                
                # 生成随机延迟
                delay = 0.01 if not human_like else self._get_random_delay()
                if human_like:
                    time.sleep(delay)
                
                # 执行点击
                if button == 'left':
                    self.mouse_controller.click(mouse.Button.left)
                elif button == 'right':
                    self.mouse_controller.click(mouse.Button.right)
                    
                # 等待一个小的随机时间
                if human_like:
                    time.sleep(delay / 2)
                
                # 移回原位
                self.move_mouse(current_pos[0], current_pos[1], human_like=human_like)
                logger.info(f"鼠标点击: {x}, {y}, 按钮: {button}, {'即时模式' if not human_like else f'延迟: {delay*1000:.1f}ms'}")
                
            except Exception as e:
                logger.error(f"鼠标点击失败: {e}")

class PluginManager:
    """插件管理器，用于加载和管理职业插件"""
    
    def __init__(self, screen_reader, input_controller):
        self.screen_reader = screen_reader
        self.input_controller = input_controller
        self.plugins = {}
        self._ensure_plugins_dir()
        
    def _ensure_plugins_dir(self):
        """确保插件目录存在"""
        if not os.path.exists(PLUGINS_DIR):
            os.makedirs(PLUGINS_DIR)
            # 创建__init__.py使其成为包
            with open(os.path.join(PLUGINS_DIR, "__init__.py"), "w") as f:
                f.write("# 插件包初始化文件\n")
            logger.info(f"已创建插件目录: {PLUGINS_DIR}")
            
            # 创建示例插件文件
            self._create_example_plugin()
    
    def _create_example_plugin(self):
        """创建示例插件文件"""
        example = """# 示例职业插件
from typing import Dict

class ExampleRotation:
    def __init__(self, screen_reader, input_controller):
        self.screen_reader = screen_reader
        self.input_controller = input_controller
        self.get_color = screen_reader.get_color
        self.press_key = input_controller.press_key
        
    def execute(self, key_states: Dict[str, int]) -> bool:
        \"\"\"执行技能循环\"\"\"
        # 检查某个技能图标颜色
        if (self.get_color(1100, 600) == "FFCC") and (self.get_color(1103, 600) == "FFAA"):
            self.press_key('1')
            return True
            
        return False  # 继续检查其他条件
"""
        with open(os.path.join(PLUGINS_DIR, "example_rotation.py"), "w") as f:
            f.write(example)
    
    def load_plugins(self):
        """加载所有插件"""
        try:
            # 确保当前路径在sys.path中，这样可以找到plugins模块
            current_dir = os.path.dirname(os.path.abspath(__file__))
            if current_dir not in sys.path:
                sys.path.insert(0, current_dir)
                
            # 检查plugins目录是否已正确创建
            self._ensure_plugins_dir()
                
            # 扫描插件目录
            for filename in os.listdir(PLUGINS_DIR):
                if filename.endswith('.py') and filename != "__init__.py":
                    module_name = os.path.splitext(filename)[0]
                    try:
                        # 动态导入模块
                        module_path = f"{PLUGINS_DIR}.{module_name}"
                        logger.info(f"尝试导入插件模块: {module_path}")
                        
                        # 使用importlib.import_module导入
                        module = importlib.import_module(module_path)
                        
                        # 查找所有以Rotation结尾的类
                        for attr_name in dir(module):
                            if attr_name.endswith('Rotation'):
                                rotation_class = getattr(module, attr_name)
                                
                                # 实例化插件
                                plugin_instance = rotation_class(self.screen_reader, self.input_controller)
                                self.plugins[attr_name] = plugin_instance
                                logger.info(f"已加载插件: {attr_name}")
                                
                    except Exception as e:
                        logger.error(f"加载插件失败 {module_name}: {e}")
                        import traceback
                        logger.error(traceback.format_exc())
                        
        except Exception as e:
            logger.error(f"扫描插件目录失败: {e}")
            import traceback
            logger.error(traceback.format_exc())
    
    def get_plugin(self, name: str):
        """获取指定插件"""
        if name in self.plugins:
            return self.plugins[name]
        
        # 尝试基于职业名称推断插件名称
        for plugin_name in self.plugins:
            if name.lower() in plugin_name.lower():
                return self.plugins[plugin_name]
                
        return None

class WoWHelperUI:
    """WoW辅助工具高级UI界面"""
    
    def __init__(self, config, event_queue):
        self.config = config
        self.event_queue = event_queue
        self.root = tk.Tk()
        self.root.title("WoW助手")
        self.root.withdraw()  # 隐藏主窗口
        
        # 创建状态显示窗口
        self._create_status_display()
        
        # 创建配置窗口
        self._create_config_window()
        
        # 创建统计数据
        self.stats = {
            "keypresses": 0,
            "mouseclicks": 0,
            "screenshots": 0,
            "class_detections": 0,
            "start_time": time.time()
        }
        
        # 界面刷新定时器
        self.update_timer = None
        self._start_ui_timer()
    
    def _create_status_display(self):
        """创建状态显示窗口"""
        # 创建透明浮动标签
        self.label = tk.Label(
            text='Holding', 
            font=('Times', '50'), 
            fg=self.config['ui_colors']['holding'], 
            bg='white'
        )
        
        # 设置窗口属性
        self.label.master.overrideredirect(True)
        self.label.master.geometry(f"+{self.config['ui_position']['x']}+{self.config['ui_position']['y']}")
        self.label.master.lift()
        self.label.master.wm_attributes("-topmost", True)
        self.label.master.wm_attributes("-disabled", True)
        self.label.master.wm_attributes("-transparentcolor", "white")
        
        self.label.pack()
    
    def _create_config_window(self):
        """创建配置窗口"""
        # 配置窗口先隐藏，可以通过右键状态显示触发
        self.config_window = tk.Toplevel(self.root)
        self.config_window.title("WoW助手 - 配置")
        self.config_window.withdraw()
        
        # 添加配置选项
        frame = ttk.Frame(self.config_window, padding="10")
        frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 调试模式切换
        debug_var = tk.BooleanVar(value=self.config.get("debug_mode", False))
        ttk.Checkbutton(frame, text="调试模式", variable=debug_var).grid(column=0, row=0, sticky=tk.W)
        
        # 保存按钮
        ttk.Button(frame, text="保存配置", 
                  command=lambda: self._save_config({"debug_mode": debug_var.get()})).grid(column=1, row=10, sticky=tk.E)
    
    def _save_config(self, new_config):
        """保存新的配置"""
        # 更新配置
        self.config.update(new_config)
        
        # 通知事件队列
        self.event_queue.put(Event(Event.TYPE_UI_UPDATE, {"config": new_config}))
        
        messagebox.showinfo("配置已保存", "新的配置已保存并应用")
        self.config_window.withdraw()
    
    def _start_ui_timer(self):
        """启动UI更新定时器"""
        def update():
            # 处理UI队列中的事件
            self._process_ui_events()
            # 重新调度定时器
            self.update_timer = Timer(1.0/UI_REFRESH_RATE, update)
            self.update_timer.daemon = True
            self.update_timer.start()
        
        # 启动首次定时器
        self.update_timer = Timer(1.0/UI_REFRESH_RATE, update)
        self.update_timer.daemon = True
        self.update_timer.start()
    
    def _process_ui_events(self):
        """处理UI事件队列"""
        # 这里可以处理特定于UI的事件
        pass
    
    def update_status(self, is_running: bool, debug_mode: bool = False, debug_info: str = "") -> None:
        """更新UI状态显示"""
        if debug_mode:
            self.label.config(
                text=f"Debug: {debug_info}", 
                fg=self.config['ui_colors']['debug']
            )
        elif is_running:
            self.label.config(
                text="Running", 
                fg=self.config['ui_colors']['running']
            )
        else:
            self.label.config(
                text="Holding", 
                fg=self.config['ui_colors']['holding']
            )
        self.label.update()
    
    def close(self):
        """关闭UI"""
        if self.update_timer:
            self.update_timer.cancel()
        self.root.quit()
        self.root.destroy()

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: str = CONFIG_FILE):
        self.config_file = config_file
        self.config = self._load_config()
    
    def _load_config(self) -> dict:
        """加载配置文件，如果不存在则创建默认配置"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                    logger.info("已加载配置文件")
                    return config
            else:
                # 创建默认配置文件
                with open(self.config_file, 'w', encoding='utf-8') as f:
                    json.dump(DEFAULT_CONFIG, f, indent=4)
                    logger.info("已创建默认配置文件")
                return DEFAULT_CONFIG
        except Exception as e:
            logger.error(f"加载配置失败: {e}")
            return DEFAULT_CONFIG
    
    def save_config(self) -> None:
        """保存当前配置到文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=4)
                logger.info("配置已保存")
        except Exception as e:
            logger.error(f"保存配置失败: {e}")
    
    def update_config(self, new_config: dict) -> None:
        """更新配置"""
        self.config.update(new_config)
        self.save_config()

class WoWHelper:
    """WoW辅助工具主类"""
    
    def __init__(self):
        # 初始化事件队列
        self.event_queue = queue.Queue(maxsize=MAX_QUEUE_SIZE)
        
        # 初始化配置
        self.config_manager = ConfigManager()
        self.config = self.config_manager.config
        
        # 初始化状态
        self.running = False
        self.debug_mode = self.config.get('debug_mode', False)
        
        # 按键状态
        self.key_states = {
            'key_2': 0,
            'key_3': 0,
            'key_q': 0,
            'key_f1': 0
        }
        
        # 初始化组件
        self.screen_reader = ScreenReader(self.config, self.event_queue)
        self.input_controller = InputController(self.config, self.event_queue)
        self.plugin_manager = PluginManager(self.screen_reader, self.input_controller)
        self.plugin_manager.load_plugins()
        self.rotation_handler = ClassRotationHandler(self.screen_reader, self.input_controller, self.plugin_manager)
        self.ui = WoWHelperUI(self.config, self.event_queue)
        
        logger.info("WoW助手已初始化完成")
    
    def _on_key_press(self, key) -> None:
        """处理键盘按下事件"""
        try:
            # 转换键值为字符串
            key_str = self._convert_key_to_str(key)
            
            # 处理调试模式切换
            if key_str == 'f12':
                self.debug_mode = not self.debug_mode
                logger.info(f"调试模式: {'开启' if self.debug_mode else '关闭'}")
                self.config['debug_mode'] = self.debug_mode
                self.config_manager.save_config()
            
            # 处理特殊键位状态
            if self.running:
                special_keys = {'2': 'key_2', '3': 'key_3', 'q': 'key_q', 'f1': 'key_f1'}
                if key_str in special_keys:
                    key_name = special_keys[key_str]
                    self.key_states[key_name] = 1
                    logger.info(f"{key_name} = 1")
        
        except AttributeError as e:
            logger.error(f'键盘事件处理错误: {e}')
    
    def _convert_key_to_str(self, key) -> str:
        """将pynput键对象转换为字符串"""
        key_str = str(key).replace('Key.', '').replace("'", '').replace("<", '')
        return key_str.split(':', 1)[0]
    
    def _on_mouse_click(self, x, y, button, pressed) -> None:
        """处理鼠标点击事件"""
        # 检查是否是中键被按下 - 控制脚本启动/停止
        if button == mouse.Button.middle and pressed:
            self.running = not self.running
            logger.info(f"运行状态: {'启动' if self.running else '停止'}")
            # 更新UI状态
            self.ui.update_status(self.running, self.debug_mode)
        
        # 在调试模式下，左键点击时采样颜色
        if self.debug_mode and button == mouse.Button.left and pressed:
            x, y = self.input_controller.mouse_controller.position
            color = self.screen_reader.get_color(x, y)
            logger.debug(f"样本位置 ({x}, {y}) 的颜色: {color}")
            
            # 生成条件检测代码模板
            condition_code = f'if (self.get_color({x}, {y})=="{color}") and (self.get_color({x-3}, {y})=="{self.screen_reader.get_color(x-3, y)}") and (self.get_color({x+3}, {y})=="{self.screen_reader.get_color(x+3, y)}") and (self.get_color({x}, {y-3})=="{self.screen_reader.get_color(x, y-3)}") and (self.get_color({x}, {y+3})=="{self.screen_reader.get_color(x, y+3)}"):'
            logger.info(f"生成的条件代码:\n{condition_code}\n    self.press_key('键位')")
    
    def weighted_random_from_range(self) -> int:
        """根据权重从多个范围中随机选择一个值"""
        weighted_ranges = self.config['sample_interval']['weights']
        # 根据权重选择一个范围
        ranges = [r['range'] for r in weighted_ranges]
        weights = [r['weight'] for r in weighted_ranges]
        selected_range = random.choices(ranges, weights=weights, k=1)[0]

        # 在选定的范围内生成随机整数
        start, end = selected_range
        return random.randint(start, end)
    
    def _setup_listeners(self):
        """设置键盘和鼠标监听器"""
        # 键盘监听器
        self.kb_listener = keyboard.Listener(on_press=self._on_key_press)
        self.kb_listener.daemon = True
        self.kb_listener.start()
        
        # 鼠标监听器
        self.mouse_listener = mouse.Listener(on_click=self._on_mouse_click)
        self.mouse_listener.daemon = True
        self.mouse_listener.start()
        
        logger.info("输入监听器已设置")
    
    def _process_events(self):
        """处理事件队列中的事件"""
        try:
            # 一次最多处理10个事件，避免阻塞主循环
            for _ in range(10):
                try:
                    event = self.event_queue.get_nowait()
                    if event.type == Event.TYPE_KEYPRESS:
                        self.ui.stats["keypresses"] += 1
                    elif event.type == Event.TYPE_MOUSECLICK:
                        self.ui.stats["mouseclicks"] += 1
                    elif event.type == Event.TYPE_SCREENSHOT:
                        self.ui.stats["screenshots"] += 1
                    
                    self.event_queue.task_done()
                except queue.Empty:
                    break
        except Exception as e:
            logger.error(f"事件处理错误: {e}")
    
    def detect_class(self) -> None:
        """检测职业并执行相应的技能循环"""
        class_config = self.config["class_detection"]
        
        # 设置监听器
        self._setup_listeners()
        
        logger.info("开始职业检测循环")
        while True:
            try:
                # 处理事件队列 - 这应该总是执行的，以便能接收中键点击事件
                self._process_events()
                
                if self.running:
                    # 运行时使用较短的随机延迟
                    random_number = self.weighted_random_from_range()
                    sleep_time = random_number / 1000
                    
                    # 更新UI显示
                    if self.debug_mode:
                        pos = self.input_controller.mouse_controller.position
                        debug_info = f"{pos[0]},{pos[1]}"
                        self.ui.update_status(self.running, self.debug_mode, debug_info)
                    else:
                        self.ui.update_status(self.running, self.debug_mode)     
                                      
                    detected = False
                    
                    # 检测职业类型
                    for class_name, detection_config in class_config.items():
                        pos = detection_config['position']
                        color = detection_config['color']
                        nearby_offsets = detection_config['nearby_offsets']
                        nearby_colors = detection_config['nearby_colors']
                        
                        # 检查当前位置及周围像素的颜色
                        if (self.screen_reader.get_color(pos[0], pos[1]) == color and
                            all(self.screen_reader.get_color(pos[0] + offset[0], pos[1] + offset[1]) == nearby_color
                                for offset, nearby_color in zip(nearby_offsets, nearby_colors))):
                                
                            # 调用相应的职业循环方法
                            handler_method = getattr(self.rotation_handler, class_name.lower(), None)
                            if handler_method:
                                handler_method(self.key_states)
                                detected = True
                                break
                    
                    if not detected and self.debug_mode:
                        logger.debug("未检测到已知职业")
                    else:
                        # 非运行状态，使用更长的休眠时间以减少资源使用
                        sleep_time = 0.1  # 100毫秒
                        # 确保UI显示为等待状态
                        self.ui.update_status(self.running, self.debug_mode)
                
                    # 无论运行与否，都需要休眠以避免过度消耗CPU
                    time.sleep(sleep_time)
                            
            except Exception as e:
                logger.error(f"主循环异常: {e}")
                if self.debug_mode:
                    import traceback
                    logger.error(traceback.format_exc())
    
    def start(self):
        """启动WoW助手"""
        logger.info("WoW助手开始运行")
        try:
            self.detect_class()
        except KeyboardInterrupt:
            logger.info("程序被用户中断")
        except Exception as e:
            logger.error(f"程序发生严重错误: {e}")
            if self.debug_mode:
                import traceback
                logger.error(traceback.format_exc())
        finally:
            logger.info("WoW助手已停止运行")

# 程序主入口
if __name__ == "__main__":
    try:
        # 启动程序
        logger.info("WoW助手开始启动...")
        wow_helper = WoWHelper()
        wow_helper.start()
    except Exception as e:
        logger.error(f"启动失败: {e}")
        import traceback
        logger.error(traceback.format_exc())
