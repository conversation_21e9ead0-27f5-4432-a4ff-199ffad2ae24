from typing import Dict

class ExampleRotation:
    def __init__(self, screen_reader, input_controller):
        self.screen_reader = screen_reader
        self.input_controller = input_controller
        self.get_color = screen_reader.get_color
        self.press_key = input_controller.press_key
        
    def execute(self, key_states: Dict[str, int]) -> bool:

        if (self.get_color(1100, 600) == "FFCC") and (self.get_color(1103, 600) == "FFAA"):
            self.press_key('1')
            return True
            
        return False  
