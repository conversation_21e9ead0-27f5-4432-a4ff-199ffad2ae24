import logging
import random
from typing import Dict, List, Tuple

logger = logging.getLogger("WoWHelper")

class ClassRotationHandler:
    """职业技能循环处理器"""
    
    def __init__(self, screen_reader: "ScreenReader", input_controller: "InputController", plugin_manager: "PluginManager" = None):
        self.screen_reader = screen_reader
        self.input_controller = input_controller
        self.get_color = screen_reader.get_color
        self.press_key = input_controller.press_key
        self.plugin_manager = plugin_manager
        
    def _try_plugin(self, class_name: str, key_states: Dict[str, int]) -> bool:
        """尝试使用插件执行技能循环"""
        if self.plugin_manager:
            plugin = self.plugin_manager.get_plugin(class_name)
            if plugin:
                try:
                    result = plugin.execute(key_states)
                    if result:  # 如果插件处理了这个请求
                        return True
                except Exception as e:
                    logger.error(f"插件 {class_name} 执行失败: {e}")
        
        return False
        
    def priest_discipline(self, key_states: Dict[str, int]) -> bool:
        """牧师戒律天赋循环"""
        # 尝试插件
        if self._try_plugin("Priest_Discipline", key_states):
            return True
            
        # 内置逻辑
        if (self.get_color(1135,623)=="E773") and (self.get_color(1132,623)=="FBAD") and (self.get_color(1138,623)=="E794") and (self.get_color(1135,620)=="ED8F") and (self.get_color(1135,626)=="ED5D"):
            self.press_key('f11')
        if (self.get_color(1140,621)=="FBFF") and (self.get_color(1137,621)=="FBFF") and (self.get_color(1143,621)=="FFFF") and (self.get_color(1140,618)=="FDFF") and (self.get_color(1140,624)=="FBFF"):
            self.press_key('f9')
        if (self.get_color(1141,622)=="9C7F") and (self.get_color(1138,622)=="D9B1") and (self.get_color(1144,622)=="A681") and (self.get_color(1141,619)=="DFBD") and (self.get_color(1141,625)=="B287"):
            self.press_key('f12')
        if (self.get_color(1135,621)=="EDE9") and (self.get_color(1132,621)=="EDE7") and (self.get_color(1138,621)=="D7D6") and (self.get_color(1135,618)=="EEE9") and (self.get_color(1135,624)=="EEE9"):
            self.press_key('f10')
        if (self.get_color(1133,619)=="FF77") and (self.get_color(1130,619)=="FF31") and (self.get_color(1136,619)=="FFC5") and (self.get_color(1133,616)=="FF29") and (self.get_color(1133,622)=="FFC1"):
            self.press_key('f8')
        return True
    
    def shaman_restoration(self, key_states: Dict[str, int]) -> bool:
        """萨满恢复天赋循环"""
        # 尝试插件
        if self._try_plugin("Shaman_Restoration", key_states):
            return True
            
        # 内置逻辑
        st_aoe, red_list, yellow_list, blue_list = self.screen_reader.heal_raid_frames_check()
        
        if st_aoe != 'STOP':
            lucky_guy = None
            if len(red_list) != 0:
                lucky_guy = random.choice(red_list)
            elif len(yellow_list) != 0:
                lucky_guy = random.choice(yellow_list)
            elif len(blue_list) != 0:
                lucky_guy = random.choice(blue_list)
                
            if lucky_guy:
                if (self.get_color(1138, 625) == "5F63") and (self.get_color(1135, 625) == "CDD0") and (self.get_color(1141, 625) == "666B") and (
                        self.get_color(1138, 622) == "8B8A") and (self.get_color(1138, 628) == "545A"):
                    curr_position = self.input_controller.mouse_controller.position
                    self.input_controller.move_mouse(lucky_guy[0], lucky_guy[1])
                    self.press_key('f2')
                    self.input_controller.move_mouse(curr_position[0], curr_position[1])
                    return True
                if st_aoe == 'ST':
                    self.input_controller.click_mouse(x=lucky_guy[0], y=lucky_guy[1], button='left')
                    return True
                if st_aoe == 'AOE':
                    self.input_controller.click_mouse(x=lucky_guy[0], y=lucky_guy[1], button='right')
                    return True
        return True

    def rogue_assassination(self, key_states: Dict[str, int]) -> bool:
        """盗贼刺杀天赋循环"""
        # 尝试插件
        if self._try_plugin("Rogue_Assassination", key_states):
            return True
            
        # 内置逻辑
        if (self.get_color(1138, 641) == "7984") and (self.get_color(1135, 641) == "7984") and (self.get_color(1141, 641) == "7584") and (
                self.get_color(1138, 638) == "697A") and (self.get_color(1138, 644) == "7581"):
            self.press_key('t')
        if (self.get_color(1140, 638) == "252F") and (self.get_color(1137, 638) == "3C3E") and (self.get_color(1143, 638) == "6C7B") and (
                self.get_color(1140, 635) == "2C31") and (self.get_color(1140, 641) == "2418"):
            self.press_key('f6')
        if (self.get_color(1142, 642) == "656B") and (self.get_color(1139, 642) == "CFCE") and (self.get_color(1145, 642) == "FBFF") and (
                self.get_color(1142, 639) == "FBF8") and (self.get_color(1142, 645) == "9C9F"):
            self.press_key('2')
        if (self.get_color(1141, 640) == "0000") and (self.get_color(1138, 640) == "0000") and (self.get_color(1144, 640) == "0000") and (
                self.get_color(1141, 637) == "0000") and (self.get_color(1141, 643) == "0000"):
            self.press_key('1')
        if (self.get_color(1143, 640) == "F3F7") and (self.get_color(1140, 640) == "5E63") and (self.get_color(1146, 640) == "565C") and (
                self.get_color(1143, 637) == "5E63") and (self.get_color(1143, 643) == "686E"):
            self.press_key('2')
        if (self.get_color(1138, 642) == "3E91") and (self.get_color(1135, 642) == "61AD") and (self.get_color(1141, 642) == "4297") and (
                self.get_color(1138, 639) == "49A8") and (self.get_color(1138, 645) == "3B99"):
            self.press_key('4')
        if (self.get_color(1141, 635) == "DCEC") and (self.get_color(1138, 635) == "F2FF") and (self.get_color(1144, 635) == "C7CE") and (
                self.get_color(1141, 632) == "C3CE") and (self.get_color(1141, 638) == "E7EF"):
            self.press_key('3')
        if (self.get_color(1137, 640) == "E2CF") and (self.get_color(1134, 640) == "DA62") and (self.get_color(1140, 640) == "A196") and (
                self.get_color(1137, 637) == "DECF") and (self.get_color(1137, 643) == "FFFF"):
            self.press_key('r')
        if (self.get_color(1139, 642) == "149C") and (self.get_color(1136, 642) == "107B") and (self.get_color(1142, 642) == "B7BD") and (
                self.get_color(1139, 639) == "1884") and (self.get_color(1139, 645) == "C5A4"):
            self.press_key('q')
        if (self.get_color(1138, 641) == "5F5E") and (self.get_color(1135, 641) == "0000") and (self.get_color(1141, 641) == "E3E7") and (
                self.get_color(1138, 638) == "5B60") and (self.get_color(1138, 644) == "EBEA"):
            self.press_key('f3')
        if (self.get_color(1141, 642) == "C39C") and (self.get_color(1138, 642) == "7F69") and (self.get_color(1144, 642) == "CDC5") and (
                self.get_color(1141, 639) == "7C6B") and (self.get_color(1141, 645) == "8252"):
            self.press_key('5')
        if (self.get_color(1139, 642) == "3018") and (self.get_color(1136, 642) == "1E0D") and (self.get_color(1142, 642) == "2F18") and (
                self.get_color(1139, 639) == "381C") and (self.get_color(1139, 645) == "3F20"):
            self.press_key('f5')
        return True
        
    def rogue_outlaw(self, key_states: Dict[str, int]) -> bool:
        """盗贼狂徒天赋循环"""
        # 尝试插件
        if self._try_plugin("Rogue_Outlaw", key_states):
            return True
            
        # 内置逻辑
        if (self.get_color(1129, 628) == "6D31") and (self.get_color(1126, 628) == "4715") and (self.get_color(1132, 628) == "2605") and (
                self.get_color(1129, 625) == "150D") and (self.get_color(1129, 631) == "5510"):
            self.press_key('f1')
        if (self.get_color(1143, 623) == "221B") and (self.get_color(1140, 623) == "2520") and (self.get_color(1146, 623) == "241B") and (
                self.get_color(1143, 620) == "A23F") and (self.get_color(1143, 626) == "3029"):
            self.press_key('f3')
        if (self.get_color(1140, 619) == "B68C") and (self.get_color(1137, 619) == "635E") and (self.get_color(1143, 619) == "C589") and (
                self.get_color(1140, 616) == "E7C6") and (self.get_color(1140, 622) == "5952"):
            self.press_key('5')
        if (self.get_color(1138, 623) == "450D") and (self.get_color(1135, 623) == "BF59") and (self.get_color(1141, 623) == "400D") and (
                self.get_color(1138, 620) == "5118") and (self.get_color(1138, 626) == "0008"):
            self.press_key('f5')
        if (self.get_color(1140, 625) == "DF77") and (self.get_color(1137, 625) == "DC81") and (self.get_color(1143, 625) == "DEB2") and (
                self.get_color(1140, 622) == "EDA4") and (self.get_color(1140, 628) == "BE56"):
            self.press_key('f4')
        if (self.get_color(1138, 622) == "7684") and (self.get_color(1135, 622) == "8690") and (self.get_color(1141, 622) == "7381") and (
                self.get_color(1138, 619) == "6973") and (self.get_color(1138, 625) == "7A84"):
            self.press_key('t')
        if (self.get_color(1140, 618) == "4D52") and (self.get_color(1137, 618) == "8481") and (self.get_color(1143, 618) == "8556") and (
                self.get_color(1140, 615) == "8B88") and (self.get_color(1140, 621) == "654C"):
            self.press_key('r')
        if (self.get_color(1138, 619) == "AEE6") and (self.get_color(1135, 619) == "AFCE") and (self.get_color(1141, 619) == "FBFF") and (
                self.get_color(1138, 616) == "FFFF") and (self.get_color(1138, 622) == "4698"):
            self.press_key('f2')
        if (self.get_color(1141, 625) == "0000") and (self.get_color(1138, 625) == "0000") and (self.get_color(1144, 625) == "0000") and (
                self.get_color(1141, 622) == "0000") and (self.get_color(1141, 628) == "0000"):
            self.press_key('f8')
        if (self.get_color(1141, 623) == "8684") and (self.get_color(1138, 623) == "ACAA") and (self.get_color(1144, 623) == "798B") and (
                self.get_color(1141, 620) == "00E7") and (self.get_color(1141, 626) == "B9B9"):
            self.press_key('f6')
        return True
    
    def shaman_enhancement(self, key_states: Dict[str, int]) -> bool:
        """萨满增强天赋循环"""
        # 尝试插件
        if self._try_plugin("Shaman_Enhancement", key_states):
            return True
            
        # 内置逻辑
        if (self.get_color(1142,623)=="3046") and (self.get_color(1139,623)=="3C6A") and (self.get_color(1145,623)=="7F90") and (self.get_color(1142,620)=="9AC6") and (self.get_color(1142,626)=="8AA8"):
            self.press_key('f3')
        if (self.get_color(1140,623)=="0C10") and (self.get_color(1137,623)=="8DB2") and (self.get_color(1143,623)=="B77E") and (self.get_color(1140,620)=="1000") and (self.get_color(1140,626)=="0608"):
            self.press_key('f4')
        if (self.get_color(1140,624)=="2A1C") and (self.get_color(1137,624)=="1002") and (self.get_color(1143,624)=="2218") and (self.get_color(1140,621)=="6546") and (self.get_color(1140,627)=="454A"):
            self.press_key('f5')
        if (self.get_color(1140,623)=="595D") and (self.get_color(1137,623)=="2A2E") and (self.get_color(1143,623)=="8EA0") and (self.get_color(1140,620)=="676F") and (self.get_color(1140,626)=="D1F3"):
            self.press_key('f6')
        if (self.get_color(1138,623)=="1B36") and (self.get_color(1135,623)=="207B") and (self.get_color(1141,623)=="2691") and (self.get_color(1138,620)=="246F") and (self.get_color(1138,626)=="3ADA"):
            self.press_key('f7')
        if (self.get_color(1138,621)=="027E") and (self.get_color(1135,621)=="0047") and (self.get_color(1141,621)=="08D6") and (self.get_color(1138,618)=="0252") and (self.get_color(1138,624)=="00D6"):
            self.press_key('f1')
        if (self.get_color(1139,621)=="CA5E") and (self.get_color(1136,621)=="C75D") and (self.get_color(1142,621)=="C656") and (self.get_color(1139,618)=="AE39") and (self.get_color(1139,624)=="CB3D"):
            self.press_key('f9')
        if (self.get_color(1136,623)=="DFE1") and (self.get_color(1133,623)=="5D48") and (self.get_color(1139,623)=="3031") and (self.get_color(1136,620)=="EFEF") and (self.get_color(1136,626)=="A1AF"):
            self.press_key('f8')
        if (self.get_color(1141,623)=="4544") and (self.get_color(1138,623)=="8870") and (self.get_color(1144,623)=="4E3F") and (self.get_color(1141,620)=="6B4F") and (self.get_color(1141,626)=="5877"):
            self.press_key('f2')
        if (self.get_color(1142,626)=="5B18") and (self.get_color(1139,626)=="C71C") and (self.get_color(1145,626)=="6F22") and (self.get_color(1142,623)=="B02E") and (self.get_color(1142,629)=="880C"):
            self.press_key('f12')
        if (self.get_color(1142,622)=="FB97") and (self.get_color(1139,622)=="FBBE") and (self.get_color(1145,622)=="FDB5") and (self.get_color(1142,619)=="FCAA") and (self.get_color(1142,625)=="ED8F"):
            self.press_key('t')
        if (self.get_color(1142,622)=="1109") and (self.get_color(1139,622)=="0808") and (self.get_color(1145,622)=="0C08") and (self.get_color(1142,619)=="0C08") and (self.get_color(1142,625)=="120B"):
            self.press_key('f10')
        return True
        
    def shaman_elemental(self, key_states: Dict[str, int]) -> bool:
        """萨满元素天赋循环"""
        # 尝试插件
        if self._try_plugin("Shaman_Elemental", key_states):
            return True
            
        # 内置逻辑
        if (self.get_color(1124, 612) == "BBEF") and (self.get_color(1121, 612) == "AAD6") and (self.get_color(1127, 612) == "EDFB") and (
                self.get_color(1124, 609) == "D4F4") and (self.get_color(1124, 615) == "F8FC"):
            self.press_key('f7')
        if (self.get_color(1136, 622) == "3A87") and (self.get_color(1133, 622) == "41A7") and (self.get_color(1139, 622) == "3981") and (
                self.get_color(1136, 619) == "6FCE") and (self.get_color(1136, 625) == "3073"):
            self.press_key('f10')
        if (self.get_color(1128, 609) == "E0E5") and (self.get_color(1125, 609) == "ECF1") and (self.get_color(1131, 609) == "D6D3") and (
                self.get_color(1128, 606) == "737B") and (self.get_color(1128, 612) == "EDF1"):
            self.press_key('f8')
        if (self.get_color(1139, 619) == "943F" and self.get_color(1136, 619) == "7D24" and self.get_color(1142, 619) == "BE6B" and
                self.get_color(1139, 616) == "862B" and self.get_color(1139, 622) == "CB7B"):
            self.press_key('r')
        if (self.get_color(1137, 623) == "7D73" and self.get_color(1134, 623) == "4D4A" and self.get_color(1140, 623) == "756B" and
                self.get_color(1137, 620) == "6D63" and self.get_color(1137, 626) == "7B73"):
            self.press_key('3')
        if (self.get_color(1138, 619) == "3C41" and self.get_color(1135, 619) == "E3DE" and self.get_color(1141, 619) == "504F" and
                self.get_color(1138, 616) == "4547" and self.get_color(1138, 622) == "323F"):
            self.press_key('f11')
        if (self.get_color(1135, 619) == "0808" and self.get_color(1132, 619) == "1818" and self.get_color(1138, 619) == "1418" and
                self.get_color(1135, 616) == "1113" and self.get_color(1135, 622) == "1C29"):
            self.press_key('f12')
        if (self.get_color(1142, 624) == "E3F7" and self.get_color(1139, 624) == "E3FF" and self.get_color(1145, 624) == "2852" and
                self.get_color(1142, 621) == "E3F7" and self.get_color(1142, 627) == "61A4"):
            self.press_key('-')
        if (self.get_color(1141, 619) == "A747" and self.get_color(1138, 619) == "903C" and self.get_color(1144, 619) == "B256" and
                self.get_color(1141, 616) == "A649" and self.get_color(1141, 622) == "AC4E"):
            self.press_key('=' )
        if (self.get_color(1143, 617) == "0094" and self.get_color(1140, 617) == "62DB" and self.get_color(1146, 617) == "49FF" and
                self.get_color(1143, 614) == "0077" and self.get_color(1143, 620) == "03EE"):
            self.press_key('q')
        if (self.get_color(1142, 619) == "6EDE" and self.get_color(1139, 619) == "9AE9" and self.get_color(1145, 619) == "FBFF" and
                self.get_color(1142, 616) == "BCF4" and self.get_color(1142, 622) == "A6FB"):
            self.press_key('f6')
        if (self.get_color(1141, 625) == "5E08" and self.get_color(1138, 625) == "7B18" and self.get_color(1144, 625) == "470A" and
                self.get_color(1141, 622) == "9221" and self.get_color(1141, 628) == "7A20"):
            self.press_key('f7')
        if (self.get_color(1138, 622) == "F7EF" and self.get_color(1135, 622) == "F3FB" and self.get_color(1141, 622) == "EDE2" and
                self.get_color(1138, 619) == "EFDA" and self.get_color(1138, 625) == "F7FF"):
            self.press_key('f8')
        if (self.get_color(1141, 621) == "3A39" and self.get_color(1138, 621) == "4449" and self.get_color(1144, 621) == "3C39" and
                self.get_color(1141, 618) == "6573" and self.get_color(1141, 624) == "4652"):
            self.press_key('f9')
        if (self.get_color(1136, 621) == "3147" and self.get_color(1133, 621) == "1829" and self.get_color(1139, 621) == "3E47" and
                self.get_color(1136, 618) == "8579" and self.get_color(1136, 624) == "2F73"):
            self.press_key('f10')
        if (self.get_color(1140, 625) == "1B05" and self.get_color(1137, 625) == "0200" and self.get_color(1143, 625) == "5614" and
                self.get_color(1140, 622) == "9225" and self.get_color(1140, 628) == "0400"):
            self.press_key('f3')
        if (self.get_color(1139, 628) == "BAF8" and self.get_color(1136, 628) == "D5F5" and self.get_color(1142, 628) == "C7E6" and
                self.get_color(1139, 625) == "30D9" and self.get_color(1139, 631) == "9DD9"):
            self.press_key('f1')
        if (self.get_color(1141, 620) == "2F17" and self.get_color(1138, 620) == "1C10" and self.get_color(1144, 620) == "EF84" and
                self.get_color(1141, 617) == "AB62" and self.get_color(1141, 623) == "833D"):
            self.press_key('f5')
        if (self.get_color(1138, 625) == "5723" and self.get_color(1135, 625) == "AE4F" and self.get_color(1141, 625) == "4518" and
                self.get_color(1138, 622) == "7536" and self.get_color(1138, 628) == "3E19"):
            self.press_key('f4')
        if (self.get_color(1140, 620) == "6B4F" and self.get_color(1137, 620) == "95B5" and self.get_color(1143, 620) == "674D" and
                self.get_color(1140, 617) == "8380" and self.get_color(1140, 623) == "8870"):
            self.press_key('f2')
        if (self.get_color(1145,611)=="6D0C") and (self.get_color(1142,611)=="0C08") and (self.get_color(1148,611)=="DF10") and (self.get_color(1145,608)=="1008") and (self.get_color(1145,614)=="620A"):
            self.press_key('f8')
        return True

    def deathknight_unholy(self, key_states: Dict[str, int]) -> bool:
        """死亡骑士邪恶天赋循环"""
        # 尝试插件
        if self._try_plugin("Deathknight_Unholy", key_states):
            return True
            
        # 内置逻辑
        if (self.get_color(1127,646)=="5DAB") and (self.get_color(1124,646)=="65C0") and (self.get_color(1130,646)=="84CC") and (self.get_color(1127,643)=="1456") and (self.get_color(1127,649)=="225D"):
            self.press_key('2')
        if (self.get_color(1127,640)=="3A39") and (self.get_color(1124,640)=="3130") and (self.get_color(1130,640)=="3A3B") and (self.get_color(1127,637)=="474A") and (self.get_color(1127,643)=="2F2F"):
            self.press_key('f')
        if (self.get_color(1128,645)=="9819") and (self.get_color(1125,645)=="5F15") and (self.get_color(1131,645)=="6A13") and (self.get_color(1128,642)=="CD15") and (self.get_color(1128,648)=="7B13"):
            self.press_key('f11')
        if (self.get_color(1130,647)=="733B") and (self.get_color(1127,647)=="9E53") and (self.get_color(1133,647)=="3316") and (self.get_color(1130,644)=="4017") and (self.get_color(1130,650)=="3A18"):
            self.press_key('f2')
        if (self.get_color(1131,647)=="4738") and (self.get_color(1128,647)=="8A70") and (self.get_color(1134,647)=="4B43") and (self.get_color(1131,644)=="967B") and (self.get_color(1131,650)=="6653"):
            self.press_key('f3')
        if (self.get_color(1129,643)=="804F") and (self.get_color(1126,643)=="955B") and (self.get_color(1132,643)=="6F43") and (self.get_color(1129,640)=="A563") and (self.get_color(1129,646)=="8A56"):
            self.press_key('f5')
        if (self.get_color(1126,648)=="3F4E") and (self.get_color(1123,648)=="83A0") and (self.get_color(1129,648)=="9AB6") and (self.get_color(1126,645)=="869C") and (self.get_color(1126,651)=="4457"):
            self.press_key('f12')
        if (self.get_color(1130,644)=="E8D7") and (self.get_color(1127,644)=="9F79") and (self.get_color(1133,644)=="F0DF") and (self.get_color(1130,641)=="A470") and (self.get_color(1130,647)=="652D"):
            self.press_key('f4')
        if (self.get_color(1129,647)=="6059") and (self.get_color(1126,647)=="7370") and (self.get_color(1132,647)=="202B") and (self.get_color(1129,644)=="3B3C") and (self.get_color(1129,650)=="534F"):
            self.press_key('f9')
        if (self.get_color(1131,646)=="1A11") and (self.get_color(1128,646)=="1C10") and (self.get_color(1134,646)=="180B") and (self.get_color(1131,643)=="3420") and (self.get_color(1131,649)=="5334"):
            self.press_key('f8')
        if (self.get_color(1127,646)=="D4DA") and (self.get_color(1124,646)=="7BA8") and (self.get_color(1130,646)=="2869") and (self.get_color(1127,643)=="87CB") and (self.get_color(1127,649)=="3A87"):
            self.press_key('6')
        if (self.get_color(1128,646)=="1E3C") and (self.get_color(1125,646)=="A6C7") and (self.get_color(1131,646)=="2336") and (self.get_color(1128,643)=="7A9C") and (self.get_color(1128,649)=="212D"):
            self.press_key('f7')
        if (self.get_color(1126,644)=="9FAB") and (self.get_color(1123,644)=="99AC") and (self.get_color(1129,644)=="5E76") and (self.get_color(1126,641)=="607E") and (self.get_color(1126,647)=="1126"):
            self.press_key('r')
        if (self.get_color(1127,649)=="220F") and (self.get_color(1124,649)=="FF7B") and (self.get_color(1130,649)=="2C10") and (self.get_color(1127,646)=="2C10") and (self.get_color(1127,652)=="2810"):
            self.press_key('f10')
        if (self.get_color(1125,644)=="7CAD") and (self.get_color(1122,644)=="BADE") and (self.get_color(1128,644)=="B7DA") and (self.get_color(1125,641)=="82AD") and (self.get_color(1125,647)=="AFD7"):
            self.press_key('e')
        return True

    def paladin_retribution(self, key_states: Dict[str, int]) -> bool:
        """圣骑士惩戒天赋循环"""
        # 尝试插件
        if self._try_plugin("Paladin_Retribution", key_states):
            return True
            
        # 检查特定键位的状态
        if key_states.get('key_2', 0) == 1:
            if ((self.get_color(530, 949) == "DB8C") and (self.get_color(527, 949) == "7E2F") and (self.get_color(533, 949) == "4C11") and (
                    self.get_color(530, 946) == "BA85") and (self.get_color(530, 952) == "6128")):
                self.press_key('2')
                return True
            else:
                key_states['key_2'] = 0
                logger.info("key_2 = 0")
                
        if key_states.get('key_3', 0) == 1:
            if ((self.get_color(575, 949) == "7D68") and (self.get_color(572, 949) == "767B") and (self.get_color(578, 949) == "C98E") and (
                    self.get_color(575, 946) == "4754") and (self.get_color(575, 952) == "4552")):
                self.press_key('3')
                return True
            else:
                key_states['key_3'] = 0
                logger.info("key_3 = 0")
                
        if key_states.get('key_q', 0) == 1:
            if ((self.get_color(561, 949) == "FFFF") and (self.get_color(558, 949) == "FFFF") and (self.get_color(564, 949) == "D508") and (
                    self.get_color(561, 946) == "BD10") and (self.get_color(561, 952) == "DC2E")):
                self.press_key('q')
                return True
            else:
                key_states['key_q'] = 0
                logger.info("key_q = 0")
                
        if key_states.get('key_f1', 0) == 1:
            if ((self.get_color(546, 950) == "542A") and (self.get_color(543, 950) == "5429") and (self.get_color(549, 950) == "829F") and (
                    self.get_color(546, 947) == "C91F") and (self.get_color(546, 953) == "8C6D")):
                self.press_key('f1')
                return True
            else:
                key_states['key_f1'] = 0
                logger.info("key_f1 = 0")
                
        # 检查战斗状态
        if (self.get_color(659, 871) == "03FF") and (self.get_color(656, 871) == "03FF") and (self.get_color(662, 871) == "03FF") and (
                self.get_color(659, 868) == "03FF") and (self.get_color(659, 874) == "03FF"):
            return True  # 不在战斗中

        # 内置逻辑 - 技能循环
        if (self.get_color(1141, 620) == "0400") and (self.get_color(1138, 620) == "7605") and (self.get_color(1144, 620) == "7F14") and (
                self.get_color(1141, 617) == "830A") and (self.get_color(1141, 623) == "9117"):
            self.press_key('f1')
            return True
            
        if (self.get_color(1141, 623) == "2505") and (self.get_color(1138, 623) == "0901") and (self.get_color(1144, 623) == "360C") and (
                self.get_color(1141, 620) == "1C05") and (self.get_color(1141, 626) == "1802"):
            self.press_key('e')
            return True
            
        if (self.get_color(1139, 621) == "7937") and (self.get_color(1136, 621) == "C87F") and (self.get_color(1142, 621) == "A252") and (
                self.get_color(1139, 618) == "8039") and (self.get_color(1139, 624) == "6E2E"):
            self.press_key('7')
            return True
            
        if (self.get_color(1137, 623) == "2770") and (self.get_color(1134, 623) == "1060") and (self.get_color(1140, 623) == "4322") and (
                self.get_color(1137, 620) == "6938") and (self.get_color(1137, 626) == "1083"):
            self.press_key('f11')
            return True
            
        if (self.get_color(1130, 621) == "2800") and (self.get_color(1127, 621) == "1000") and (self.get_color(1133, 621) == "4E06") and (
                self.get_color(1130, 618) == "4A10") and (self.get_color(1130, 624) == "2C00"):
            self.press_key('x')
            return True
            
        if (self.get_color(1142, 625) == "F3CE") and (self.get_color(1139, 625) == "EDCE") and (self.get_color(1145, 625) == "C3AC") and (
                self.get_color(1142, 622) == "F1D2") and (self.get_color(1142, 628) == "9D8F"):
            self.press_key('f4')
            return True
            
        if (self.get_color(1138, 611) == "8808") and (self.get_color(1135, 611) == "AE03") and (self.get_color(1141, 611) == "5908") and (
                self.get_color(1138, 608) == "D210") and (self.get_color(1138, 614) == "7E08"):
            self.press_key('f12')
            return True
            
        if (self.get_color(1140, 620) == "510D") and (self.get_color(1137, 620) == "A82D") and (self.get_color(1143, 620) == "C331") and (
                self.get_color(1140, 617) == "A84B") and (self.get_color(1140, 623) == "620D"):
            self.press_key('g')
            return True
            
        if (self.get_color(1140, 620) == "FBFF") and (self.get_color(1137, 620) == "C5B5") and (self.get_color(1143, 620) == "AE9F") and (
                self.get_color(1140, 617) == "BAAA") and (self.get_color(1140, 623) == "D8C8"):
            self.press_key('r')
            return True
            
        if (self.get_color(1140, 627) == "1810") and (self.get_color(1137, 627) == "2014") and (self.get_color(1143, 627) == "1810") and (
                self.get_color(1140, 624) == "1C0C") and (self.get_color(1140, 630) == "4507"):
            self.press_key('f2')
            return True
            
        if (self.get_color(1138, 624) == "6D66") and (self.get_color(1135, 624) == "8E84") and (self.get_color(1141, 624) == "6D66") and (
                self.get_color(1138, 621) == "4847") and (self.get_color(1138, 627) == "5B5D"):
            self.press_key('f')
            return True
            
        if (self.get_color(1141, 624) == "8F15") and (self.get_color(1138, 624) == "1A0A") and (self.get_color(1144, 624) == "1008") and (
                self.get_color(1141, 621) == "210A") and (self.get_color(1141, 627) == "160E"):
            self.press_key('5')
            return True
            
        if (self.get_color(1139, 622) == "D889") and (self.get_color(1136, 622) == "6C1B") and (self.get_color(1142, 622) == "A64A") and (
                self.get_color(1139, 619) == "EBA5") and (self.get_color(1139, 625) == "D486"):
            self.press_key('2')
            return True
            
        if (self.get_color(1142, 617) == "DFD6") and (self.get_color(1139, 617) == "C0B5") and (self.get_color(1145, 617) == "744C") and (
                self.get_color(1142, 614) == "CBBC") and (self.get_color(1142, 620) == "9F60"):
            self.press_key('t')
            return True
            
        if (self.get_color(1140, 623) == "CB4A") and (self.get_color(1137, 623) == "B259") and (self.get_color(1143, 623) == "DB76") and (
                self.get_color(1140, 620) == "CD4A") and (self.get_color(1140, 626) == "C239"):
            self.press_key('t')
            return True
            
        if (self.get_color(1141, 623) == "6D46") and (self.get_color(1138, 623) == "6D46") and (self.get_color(1144, 623) == "8F6E") and (
                self.get_color(1141, 620) == "7942") and (self.get_color(1141, 626) == "2014"):
            self.press_key('f3')
            return True
            
        if (self.get_color(1139, 628) == "A340") and (self.get_color(1136, 628) == "782A") and (self.get_color(1142, 628) == "D352") and (
                self.get_color(1139, 625) == "E75A") and (self.get_color(1139, 631) == "B041"):
            self.press_key('6')
            return True
            
        if (self.get_color(1141, 619) == "FFBD") and (self.get_color(1138, 619) == "B67B") and (self.get_color(1144, 619) == "F794") and (
                self.get_color(1141, 616) == "5E54") and (self.get_color(1141, 622) == "FDAC"):
            self.press_key('f6')
            return True
            
        if (self.get_color(1141, 622) == "9C7F") and (self.get_color(1138, 622) == "D9B1") and (self.get_color(1144, 622) == "A681") and (
                self.get_color(1141, 619) == "DFBD") and (self.get_color(1141, 625) == "B287"):
            self.press_key('f5')
            return True
            
        if (self.get_color(1141, 620) == "C634") and (self.get_color(1138, 620) == "4908") and (self.get_color(1144, 620) == "0000") and (
                self.get_color(1141, 617) == "FF8C") and (self.get_color(1141, 623) == "FF6B"):
            self.press_key('6')
            return True
            
        return True