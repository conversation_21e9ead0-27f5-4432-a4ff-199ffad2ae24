﻿<!DOCTYPE html>

<html>

  <head>

    <title>Welcome to SimulationCraft</title>

    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8"/>
	<meta name="color-scheme" content="light dark">

    <style type="text/css">
      * { border: none; margin: 0; padding: 0; }
      body {padding: 5px 25px 25px 25px;font-family: "Lucida Grande", Arial, sans-serif;font-size: 14px;background: url(data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEASABIAAD/4QDKRXhpZgAATU0AKgAAAAgABwESAAMAAAABAAEAAAEaAAUAAAABAAAAYgEbAAUAAAABAAAAagEoAAMAAAABAAIAAAExAAIAAAARAAAAcgEyAAIAAAAUAAAAhIdpAAQAAAABAAAAmAAAAAAAAAWfAAAAFAAABZ8AAAAUUGl4ZWxtYXRvciAyLjAuNQAAMjAxMjowNzoyMSAwODowNzo0MAAAA6ABAAMAAAAB//8AAKACAAQAAAABAAABAKADAAQAAAABAAABAAAAAAD/2wBDAAICAgICAQICAgICAgIDAwYEAwMDAwcFBQQGCAcICAgHCAgJCg0LCQkMCggICw8LDA0ODg4OCQsQEQ8OEQ0ODg7/2wBDAQICAgMDAwYEBAYOCQgJDg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg4ODg7/wAARCAEAAQADASIAAhEBAxEB/8QAHwAAAQUBAQEBAQEAAAAAAAAAAAECAwQFBgcICQoL/8QAtRAAAgEDAwIEAwUFBAQAAAF9AQIDAAQRBRIhMUEGE1FhByJxFDKBkaEII0KxwRVS0fAkM2JyggkKFhcYGRolJicoKSo0NTY3ODk6Q0RFRkdISUpTVFVWV1hZWmNkZWZnaGlqc3R1dnd4eXqDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uHi4+Tl5ufo6erx8vP09fb3+Pn6/8QAHwEAAwEBAQEBAQEBAQAAAAAAAAECAwQFBgcICQoL/8QAtREAAgECBAQDBAcFBAQAAQJ3AAECAxEEBSExBhJBUQdhcRMiMoEIFEKRobHBCSMzUvAVYnLRChYkNOEl8RcYGRomJygpKjU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq8vP09fb3+Pn6/9oADAMBAAIRAxEAPwD9LHjkk82OP97WXdySRn/Wf6utmeTzLLzfM8ry6xk8uaHy+9fj59wVXvv3P7yPzf3VWrX95NFH5kv7yqH2Xy7z/Web/wBM61IPMjh8uTmL/nrQaA/mVQ+yzyXkXlx/6utR5PM/ef8ALKr6SeZNL5n/ADy8ygzIsXH2T/nl+68v/rnVV7iSH/WfvY/+etWvtVv50skcn73/AJa1lv5d1NFJHQBK8lxHDLJGn/LKm28knnf+jauW8f2iL7RHJLVD7T/pksf+qoAvpJbxw+XJ+9iqhfR28cP7uOX/AKZRVVuJLiT/AFeZfL/8h1V/tK4j/wBZIZf+eUVAB5EkdWNStbySE/u/K/6606D/AEj/AFldG83nRfZ5OZf+mtAHOWNrJHNFH5nm+XXR/Z9n7z/v7FUrxx/2d/zy8z/llUU915MP7uOLzf8AlpFQBVuLeOGHzPMz/wA8paoQSSSeb/pHm/8ATSpX1KOSby46E8yOaL93+6koAtJ5kc3mRyeb5dRXF9H53/PWWP8A5Z1FrXmRzWEccfm+ZFJ5vl1agtdmg+X5flSyUGhjP/pEPmRx/wCrq/dTSWsEUckfm+Z/yyqXzo7GaW3jj/e1f02HzoZf3flS+VQZlXTftlx+8kk/5ZVLcXElv/q4/wDtlVDtdfZ4/K/5Zx1fgkk+xyRyR/vZKAMu0t5JLv8AeP8A9NK2vs//AE0NSPDH/Zv/ADyqK4uPJhi8uOLzf+eVAEVxbx/6w/8Af2su3kkkvP8AWebFH/y1qV9WSTyo4/8AlpQn7uY3Hl+bFQAJbyfbIpP9b+6rZt7GSQ+Z5eIv+WlCR2/7rzI/Nq+l1HDZ/wCr82KgDL/s2T/WRpzH/rKiT95+78vypY/3cUtSvcSeTLbp/wCjay/t0kc0Vv8A9NaAL7yedZ//AB2qP2aSObzJI/3UdSTzeRD/AM9f+mVSpJJcQxSRyfupP+WVAA8cn7r95+6k/wCmVX4vsfnRWdvHLJJ/y0rLuPM8mL7RHLRPb+X9m+zx/vZP+WtAGz9hkkh8z/yFWRPJcQzeXH5vlSR1pJcSRw+XJJ5ssn/LKWi08v7XL5nleb5X/LWgDl54ftHm/wDLWWOL/W1csY5I4fMk/wCWn+qrXkh8zzf9VFL5Un72m2MMf/POWWX/AK5UATpDHH5v7zy6xrq1j+2f8tf3lbzw+Tef6z/tlWHf+ZJqUv8A0zoAfBCkPmxxyfvZP9VUt1ps9xafvE/d/wDLOSrUEknleZ/mOori68yH/WUAY0FrJ9j/ANXV+4+2RzRSf8tf+Wvm1K91cfuvs8fmn/nnV/7Vb3V3FJcR/wDTOgCn9qjmhljST/ll/wA8qpfZbiOzik/1v/PKWt66tY08o28fm/8AXKokk8zyo/3sXl/89aAOce1jjvIpJJPKikrZg/1PmSUXccf2yLy4/N8uL97WoiR/Y/3kkX/XOgDLuLjy5otkeYv+WtX/ADJI4f3kn/LL91RdW6SRfu/3Xl/6qpUtvMm8xJJfN8v/AJa0AUEjjuryWST/AJ5VQXzDN5cckv8Arf8AVVqXccdvD+7k8qaShI5PtktvH+6/5aRUAUPLkk/ef9+qqv8AbI/Kkj/1v/TWr7ySQz/u/wDlpUv2iO48r7RH/q6AKqXUcn7uOT/pp5VVfstx9j8z/W1vXtrHHD5lv5Uv/XKokk/dRW/lyxSx0AcvPa+Z5UneT/W+XWzax/uZZP8AllH/AKr/AKaVLdw/6qONPNljrUgj/wBE8ySSL/plFQBVX7PJ+78zyqivpZIYYvtFx/oskv8ArYoq1E8iODzPs5lijrnnjkkl/wCWX7yXy6AKaSRw3nmW8mfM/wCmVRT/ALz/AFcf73/nrWoltHHD5f8Ay1/561E8Zh/d/aPK/wCWlAFy6j8zTvM/5a/9Nafpsf2jTfLk/wD3dSvcfaIaxk8v+2Jf9bQBqSf66WOSTzYo/wDVxy0PD9q+y/8ALWL/AJ5f88qqvDcSeVJ+6/6ZUQRyW9nL5nm/9cv+elAG8tjb/wDLx5UdULuS3kh8uOPzf+estVUvvtGneZHJ5Uvm1FfRySxSx/8AHr/y0oAlS6jk/dyeb5Un7uL/AKZ1qWscZ/1dx+8/6ZxVzlpH++ikkk/df8tal+3eXqX7vypYv+WtAFq4/d2cvmSeb/zyqK3k/wBDl/0eIy/89ZalWP7Ro/meX5X72oopPs/m2/l/9taALT/u/wB5Jz5n/PKsuWTzJoo44/8ARZP+mVbPmeXaSySfvK5x5Li3mlk8z91J+8ioAvwWtxddZIoqHjkj/wBZVW3uPL/eSSXUXmd61Luaz+xxRx/89f8AllQBQt764hvIo5I/Nq1PdeXN/wA9fM/1tULiGSb/AEhJJf3f/POqvmR+d5klvL/11oA6S3uvtEPmXFvF+7qzPIknleXH/wB+qwXuI/sf2ePzatWv2iHyreT/AJZy/vfNoA1Hkkhhik/1cVSveySQxSRSYz/z0kqKeS3km8u4k/dVlvHHHNF9n/exR0AF3NJHD5cEfneZLUr3Ukepf9NZP+Wvlf6qpX/0j93bx/8ALOqr/wDTT/W0ADxyXWpfZ4+fLoeOS382Oi1hj8mWTzPKljqWe4t49N/6a/8ATOgCgl1cQzRfuPN/66VanupPPl/1X7z/AFsVU547i6P7uSX93/zyql5n+meZJHL/ANdaAN60uvMh/eRxfu/9VV95LeSz/dweX/1yirB+0Rx2nlxmXMlWrSS4t/3cnm/9taANT959j/d/6qop7qSGWX93/pUcn/PKpXuo45/9ZVC+/wBI8rZcRSxebQBE91cXE0vmfvf+mdVXkkkm/wBX5v8A11/5Z1LPcSR/8s4vNk/55UfvP9Z5flfuqALd7HcTeVJJ/qvN8yofLzN5kkn/AG1ovbjzPK/6Z0QSW/7r95H/ANcv+edAB5nmxReX5VVZ/wB5+8+0fuv9XLV++h8u0+0R/uqy0kt/3sn7395/yyoAv/Z4JPKkjkouL6OSb95WXcXEf7r7P+6/561Vnjkj0jzPL/e/6yKgDUl+z/upP+elS281nHNLHcf+Ra5yG6kzF5kdX7i487/j3j/dSUAajzRxw+Xbyf6yhLiSTzpJI6y7eOS3m8yT/nnV977zPKj/AO/vlUARTySSWkR/e/62suf7RJ+78z/Vy1qP++tJY/Mii/651VS18z935kUsv/POgCr/AKR+9/5a/wDTWWrUEf76WT97/rfM/e/6upZ7iO383zI4v+uVS2vmSQyx+Z/q6AJZbr/pn5tZaX3+meZHH5sX/LWOtC7k8uK1t/L/AOuslYs8Mkd3+7/dRR/62KgDqHuo5Lzy/wB15v8A0yqrPqkcc3l1jW/meT5n/PT/AJ60JHcGKW4j8qgC+kdxdf6zyovetT/j3h8uOSLzaoWEkckPmR582rX9pRyQ/Z/s8X7v/lpQBLa6lJYxc+V+8/1sstZcmpW9xeeZH+6ouvLkl/dx/uqoeT5d59ygC1PNH9sljjkxLJ/y1qWD/XeZJ+98uqCfu/8AplLHWpB/pEP+rioAJLjy5pZI/wB7VD7dJ9s8yOP/AK6xVfuJPJs4o/8AprWXd28kd5/o8nlf89YqAN6e6t5PKjkji/6ZeV/yzouNWjt/KjEdc5bySf8AXXzP+etSxx3En7yPyv3dAF9PtF9+8/dRGStm3t/J/wCWn/bWsbTZLeQxf8/VWk1L99LF5ssX/PWKgCW7h+z+V+8iloT/AEryvM/561E8nmfvJI/+uVV0kkj/AHcdAFNPMvpvL/5ZVaS3jjm/efupY6pJ5kOsfaI/9V5v+qrcur6OWGX93FFLJ/zz/wCWdAGTPH5nmyeX/q6uQWskvleZJ5VMT/U+Z5lPtZI44ZZI7fypf+WtBoWnsZPJ8yP97VCeRI/+WflVag1KSTzf9b5X/tSop9N+1f8AX1/rKAMv93JeeZHJ/rKPLkjm8uT97V947i38qP8A1UUf/TKpfL8+GWSOgzBPM/s3zKq/6P8AbP3ckvm/8tYqtXEklrpsVULe1jmvIrj/AJax/vKDQ2U6y/u/Klkqm8lxH+7/AOWX/POp3t/+Wkcnlf8APX/prVUWPmQ7445f+/tBmVX8uT/ln+9q/BHH5P7uTyv+uVZd3a29vDH5kfmy/wDXWpUuJI5oo0/5aUAWp/3flXn/AD0qqlx9ovPLjt/Nre8uSTzftH73y4v9bUX2G3z5kdx5Usn+t82OgDG+zyeTL5kf72qHlxpL9n/5a+VWzb2/2f7kdWp4Y/3vmXHlf9MvKoALL95Z/wDXOhLf/lnH/wAtKLe1uJIfMjq/9nuI7OX95QBg3VrcSeZcR0eXHH5Ucccv/TX97W95nlzVFdRySQ+Z9niloAy7iP8A1slWrSOPyf8AWeVUqR/uZrOSPypZP+WVVbi3ktYf9H/1VADrv93DFcSVk+d5l55cdv5vmV0nl3FxNskkMsccVQmxt5PKuI5fKloAzvs/meb5kefLrI/1c0Ub/wDTStlI/Jm/1f72rTxx/wDLSTyqAKumxyfY/Lkk/wBXVpLX/nn/AKyT/wAh0Q2MlxD5kcf+r/1UlX7WGSPzZPM/df8ATWKgCg8ckcXmf63/ALa1VuPL8mX7RJWp9l8z95HJLL5lMe3t/sflyW/73/0VQBiQSSXE0seyLyo6l8nzLStS4sbfyZZLeT7L/wBsv9bVCHzI5oo/Mi/65UAQeZ/yz8s/6qtG3/eWfr+7onhj/wCWnmyy/wDPKpU02SSHzPLl/wC2VAESW8f/AB7xyeVFVp/+Pzy4/wDW1KkPl6d/pFxLHHJ/z1i8uieP/VeZ+6jj/wCen/LSgAnjjmmljjk8qX/lnFR9lkhm+zyR+d5n/LSqv7uT/j3j/wC/Vak8kccMXl3H72gCh/o/kf8APWiCOOOH7RH/AM9f3sVVYJPMvJY7iPyqq3Efl3n2g/6qP95QBfupLeSzlkjk8qqqf67y/MqrOJI5v+evmVft4ZMfaI/9bHQBQuLWOaXzI5P9XWvBbpJMfMkliq5PJH5MXmR/vZKoPNHDNLHHQBs+TJJZyyR/88v9VWWlx5cMvl/62T/llUqXUkn7vzP9X/y1qr9ojhm+z28f+s/6a0AWobWzupv3f7q6oeOS3s4v3nmyx1LBDJa/6R/rfMqhdyfaJpfLk8qWgDSW6k+2ReZ5vlVPdyCaz8u3/e1jfavM8r93F+7rZtI7e4hj8+Pn/WUAZbxyR3kUkf8A21lo86SO88yP/W1LfeZJefZ4/K/ef8tamtLeSOby5Lfzf3dAFyfzI4vtEkf73yv3tYcBjuJv3cktS33+u/0f/W1F/q5v3n+t/wCetAGzFDJJD5kcf+s/5ZVlpJ5MX/TWT935VSwX0n+r8z/trVV7qO3vPKjj/wBZQBat4bO6m/eR+VL/AMso6Hj8nTv9Z5svm1LaQyR/6R/raoXUnmTS+X+6loAtJNeSeV5ccv2WP/W1qPcWh82PzP3UlYME0flfZ7j/AL++bUV3NJ5Pl+ZFLH5v+t82gC/dakkc3lx3H7qOsuS6jm8r93/rKqwW/wDpn/PWtR4/+mlAEok8uz/dx+b/AMtJfNqVLezuoZZP9Vdf9MqqpN+++zx/6r/nrWokclrD5kflS+ZQBVuP3cMXlyfavL/1vlVLBJc/2lFcT+aLaP1lrGeX7RN+7kiil/551fS6jkhijn6xxf62KgDeSa0/1ckfm/8AXWKsaeP7VN+7kl+y/wDLWqr3EkcXmSSRSxf9Mv8AWVasbiOOH/V/6yKgC1aWMlv/AMe8n7qsu9kjk/dxyRRf9NfNre87/Q/Lk/dS/wDLKqH2G0kmMklx/wAtf+eVAGN5c8kXmeZ+6o/d8+Z5taj/AGeSaWOOf93/ANNaqvsjhzH5VAEr/Z5vKEn7ry6tJHbw+XJHH5vmVVtI/Mh9atPHJ5P/AE18qgBtxJ5l5FJHHWbPH/rZJKtP+8vIvL/1Un/fyi6h8yHy/MoAisY45LP95+6rZt9Njj82Py/+2tYNvJ5c0X7zyq3opJLiaKO4kzQBFdeXDZxR2/m+bJWD9l8y88zy5fN/5ayVvPJ5k3mXFv8A8s/+Wv8AyyqK4ureOHyI7eLzaAMvy/s83lx/valeae3/AO2dH263/wCWcf72ovs88n7zy/3VAGpZSR/ZP3kn+srU+0fuZZI65y1SSO88v/0bW95PmRf6z97/AM8qAMt43t5v3n+t82q88f7/AMyrt1+7m8v/AFv/ADylqK4t/wBz/rKAKtlH5kP7ytS3sU/66/8ATWs2CQxzVpJNJJ5UcklAEtxHHa2n7vzZZawfL86b/Vy+bHL/AK2Wt55PtH/Hxb/6v/nrVWe4t4YZY47eLzZP+WtAGNcQ+X+7j/ey0PH/AKH/AKv/ALZS1a+3W/nf6uX7V/zziqK4jjuv9X5v/bWgCha+Z53/ADyqW4kuLceX/wBNP+WVRLbyQ6j5f/kWWrT2/wDphk8ugDo0tY4/3fl//HKivvLtz9nSPmnJJ5l5/rPNl8qnvJ++8y4t4v8AtrFQBgpD5h8z91F/z1qJ45PO8u361qXd15f+jx29rF5f/TKqCalHJN5ccf73/lrQBK8fl2f7z/W1LpsckcP+kfuqlSSOSL/V/wDTSokvv30sgjioA2fLtzDFH/rZY/8AlrWXqUkcd55cf7oyf8s6qz6p/wAtPL/7axVL532q8ijk8r93F5n73/WUAQT2v7n/AFlV0t/+en/kWtS0k/dRxyf8s6q3H+u8uOgCW0upPO/1f7qtR/LuD+7k8rzK5yykkkvJf3n+r/5ZVqXEn2Wb7R/rf+uVAD55PLm8v/npVJ7WTyf9Z/39on/0iaWTy5YvLi/5ZVLJ5clpFH+6oALS18z95JHFF5dbMHl58z7PWW/mR6bFHHJDL/z1qhPfSRzeX5kstAHRvJ5kXl/upZf+WtYPl+ZNLHJH5XmS/uqofaLjP7yT97J/zyq/BDJcWkVx5kvmx/vJfNoAHSO31KKT/VRVfSSP/WeZ+9/55VVeOO4hi/1sVRPcRx2mfLi83/V0AXfMj/tKnPqVvHN5nmfvf+mtUEkkuLP7RH+9qJ7eOaHzP+WtAF95o5pvM8uL95UT28nk/wCsqLy/3Pl/vf3f/LWpfMjks/LoAfBb+ZN/pEcXlR1o2/l/88/9X/q6of6vTv3cnm1Qe+kjm/1ktAHRvceZDL+8i82T/lnWH+8kvJY5E8r97+6qm9xced5kkn/XKrUEf2i0i8uSXzY5fMl82gAex/0z93+68urUFv5cOEj82WhJv3MUkh8r/prQ+pfZ4f3nleb/AM8qAIvL/wBM8y8k83y/+etS/u/+/lZcEn2ib/ll/wBtavvH5c0VAGykkkcPmeX5UtE83mWcsccnm/8APWOuc+1Seb5cfmxf9Naq/aJJPNjkk82WgC/bx+ZD5ckflSyUJY+ZeSyeXmL/AJ5VL5fmQxSW+fNji/1VSvcPHD/zylkioAvpD5ekf6vzf+etZd3bxyeVHH/y0qW1uvMtP9IkMX/LOXyqPL8wyxyW/m+X/wA8paAKD2v2eKX/AJZeXUtla/aIYpI/N/6a1Paw8S/vPKp8Fj/y7yXEXm+Z/wBc6AB5o4/3kkn+lebUX2i3jmi8yT/ppRceXHqX+rili/561Vu4/Ml+0Rx+b5f/ACyoA1J/Mim8yPyv3n7ypXj8yGL935VZdp+8hl/6Z1a/eRw+ZQBKmm/Z5v8All5sn+tqK6tbeSzijjkiil/5a1Vurp5ryKS4k/1cdSwalH/z08r/ANq0ASva+T/q7fzf+mvm1QSS38mX955UtX0uvtV5/q5bWWor7y5of9Z5vl/63yqAKvl+XqXmeXF5X/PWtR/Ljhi8v97LHUr2sfmxSfZ/9X/zylqW4kj+x/6uWKWSgDLW4kuLy6kkj/e/8s/+mdVriTzJvLkrTtftkfmxyR/vfKqX93JD+8jizJQBjWklxHN5fmeV5n7yr/lxyf6R5flRR1FcQRxzReZH5sVVYE/feXcSeVFQBqJDH53mfaKLi1j8ry/+WtVcRxzeX5nm0JcfYf8Alp+6/wCutAFp7WS38r/R/Nqh5lv+98z91LV9L77VN/zyl/561Fd/vPNj+0eb5f8ArYqAKDx+XNFJHH+7/wCWstX3+z+V+7k82X/plFUv2G3ktIpPs/8Aq/8AV1acxx2f7xJYvM/6a0AZb3FxdalKfs/lfuqq3FvJJN5cdX4PtlrN/pBz5kVWkk5/eRxS/wDbWgDG8u4jtJf3n7qonh+0fvPM8ry/+Wdal3+7h8v/AFsUf/LKqHmRyXnMf2qL/nl/q6ADfb+VF/rYpPN/55UJHJHq/wDrIoopKlnkjkl8yPzZf3taj2/76KTy4paAKnmeXNF9nj82WP8A7Z1HayXEk0v2iPyo/Nq/dR2/2P7P9n8qWT/prVC0kktftUdx/wA9aAB764j82OPyvN8qov3lxF/rPKijlqq9j9o1L7R/rYo/+WsctWoJDHNLn90f+WctAFrfJbzf9Nf+WtWbW+j/ANZJH+6rM8yPyZZJP+Pqq6fbI5vMj/e/9cqANdJI/wCyJZI5P3X/ADzlrNeO4mtJbi3/ANVV1JLeM+X5nm1Fdfu4fLjoAqwRx+d+7kqq8n+mFP8AVf8APWrXl+XD/pEn+s/1VULry7eWWT/Wyyf62gAtZLfzpfMk83/nl/01rU+zx+fF+7l/7a1l2/lx2f8Ao8kXm+b5kVS3HiCSPyvLjMUUkvly0AXzB5epSyRyfupKtPHHD5sn/POqvlxzWkv+mRReX/qv+mtH2j7RL/01j/8AIlAEvmSW/wC8/wCWVW/tUkkUf7v91WVexySTf6R/rf8AplLUsEf2eDzP3Uv/AEyoAvz/APH3+7T/AFlMjmj8ny5P3v8Ayzpr30cc0Ukkf/LWj93JefaPL/1lAET7PN8yqFx9o+2S7K2f+WX7y383zKqz2tvH/q5PN8v/AJ5UGhl2n7yb95Wv9lj/AOec1MtLWOb95HJ5Uv8AyyiqrdalcWs32eSI/u6DMlePy7wSRyVa8uOP95/mSqv7uaL/AI+IoqPM8z93J/rY6AJfMkj/AHn/ACykq19qkks/3cf7qsu9jk/deZn/AL+UJb/Z4fM8yKX/AKZUAal3/rovL/dfuqoXEnkz+X9o/wC2VWri+jjsopJI6in8u4lNx9noAq/8vsX/ADyont4/O+0eXxHFVr7LHJDFJ5nlVKkcfky/89aAB/8AR/X/AK5xVVM3l/6yT91Q8knneZJH+9kqrdx/vsSUAbP2qSSaL/VSxVFcRyTalL/zyk/55VQgkjt/3kEkUv73/lrWp9ujj/dyf+QqAKH7u303y5JIv9b/AKys2e6errx/av3cfleVUFvHHJN+9j/dUGg5L5/snl/886ofbvJ/1f8Ay0/55/6ypZv9dL5dZf8ApH/LTyooqDM1ILq4uJpfLjiq08b+TFJ5n+r/ANbUUHl/bP8ArnVi4uvL8qgBj3Hl2kUnmVjT3HmQy+Z/z1q+8fl2fmSeVLWR/wAvn7z/AFVZmY9IZJP3kfm+VHV/7L/onlyeV/21qJJJI/K8v/ln/ra1E8z/AJeI/wDv7QaGX9nkjh/5ZeV/yyog+0cySVqPH5l5+7/49aL0f88/3Usf+t8qtAKs9x/11lqqklxJeeXH/qqieOSSby5JKtQR/Z5vL/e1mBauLWSTTZZI5P3tamm2/lwxfv4pZfK/e1aSPzLP955X/XKjy7iM+XHHFF/1zrQC5PH5c37usSC6kkMsccf+sq15kkf/ACzlqg919nvPM8vyqAN61uPs9pLbyR/vY/8AnlFWNqX+kTeZJH/rIqq28kk15LJH/rf+eVX/AN3/AGdFHJzL/wBNaAMHy7j/AKZVEn2iOb95WpcR+ZN/0you408nZH/raAKtxN+5i/1stUPMuJJvLj5ikoeOST935lWreH7PN/y1rMDU+y+Zpv8ArP8Av7Uum28n+sjuIpZZKvwQ+ZZ+ZJUXl3Ef+rjii/65VoBanj8uaOSPj91WMl1JcebHHmrXmSRzf8e/m1l3FxHDeRfu/KoA6hPLuLzzJP8AW/8APKsbVY086Xy46YnmSTfZ/wDpl/rP+WlQQf8ATTzZf3tAGClvJGTJHJ+6rZSSO38rzI/9ZUTxySalLH5flf8AXKpfsMf/AC0k83/plQBKn+p/dyUfu44f3nlUeXcSTfZ45Ioqi/d/bIo7i3/8i0GgP++i8uPnzKq3H+jw/wCrzWo8cdvN5n+qi/55f886lgj+0TUAY1lJ5cx/d+VF/wBNatX0fmTRR+X+6/5ZeVV/7PHNq/8Ao8f/AH9q0kMcd5+8jPmx0GZjSxyf6uSOqtxa+XafvJIvKk/56/6ytl7eT/lp5v8A8bqhcR283leX5vmx/wCt82gXsjGSP9zLb/6V+8/5a1swSXHk+ZeSeV/11/1klH2qOP8Adxxx/wDbWonuPM8r93F/21/1lAy15nmRS+XUX2W4j/eXDy1f+z+ZNFJHH5Uv/TOr/mSSeb9ot4pvL/560AYPkR+T5n73/trRBb3Ek3mf8s6E8z7Z9nkj8r/nlLWyk3l+VHHHQaEskcdvpsvl/wCt/wCmv/LKooJJ5LT95JLLUt1e29v5X2j/AJaf6r91VeCT/WyfaP3X/PKgzH3Ecnk/u5Jay/L8ybzNkX/POSt55reS0/1kVUIJLeS0ljkk/e0AVYLdLWK6kjkh83yvWokuPtWPMkiiEdVbjy/OMfmSxUfu44vLjji/7a0ASpJJJDL5f72Wj7Lcf6yTzav+XHIYpI4/Kl/6ZVf8yST93cR/6ugDBS3jkh8z975v/TWhLe4kvPM/5ZVK/wC7u/L8v91/yylq+lx5MUUcafvaANSyjgjh/wBIjqVI45ZvLjuD5X/XSovO8yHzJJIv3dUEj8z/AEiOT91QBLqUccc0X2f/ALaVza2tx+9k8uX93/z1rrv9ZpH7uSKsuCS3/wBZJ5v+toAtP5cc2ftHmy/88qoN/rovL/5aVg/vLW8tbz/W/wDPXzav/av9D8vr/wC1KAC4k+z+b/rfN/651Qikk/tGKOSP91J/y1qVJo45fLkt/wDWVqQW8ckPmR/9taAKrw/Z/wB5+6q1cSRxw+Z/y1kqKeP976+XRdSSSTf8e8sv7qgCX7RJH5Uckf7qT/W1qfZbeP8AeSSeVFJXL28nnTf88vLq/cXXmf8ALTy6ALiSdfs8flf9ta0kupI9N8uQxeb/AM9K5y3uPL/eeZ5sv+rrZe3kmi8yP/Vf8taAC6kjmh/eR+bWW8kn7qO3/dVqJYyQRf6P/qqwbq3k+2Sx8+bJQBVfTZJPN8z97Wz5kkdn+8/e/uqLWORLyJP9afK/560TzW/+r/550ASweZ9k8yPyvNqhfXUl1/q7iov+PiGWSOP91UFv9ok1I+ZHQaHQWXmTWnl3n+tkoe4kjlit/Mil/wC2VRWU3lw/89f+eVSp5n+sk/1sf+soMwuI5POi/wBH/e1QFvHJN5lxGfK/661s+ZHJ/q/3X/TKqU8f77zI/wDlnQaGc/mfbIvLkl8qr9xceZ/q4/8ASqqv+9vIv3hq09rH5Mtx5lBmFrNJ/rLiP/ll/qqiuo5Jpv8AR4/K8ui18yG8i8uTzalnmjkm/eSS0ARWsdx9ji8v/W1X1K5kuJvL8yoP+PieX7PH+6qr88mpeX5dBob2mySfY5Y7z/Vf6uOpZJJIf9Hjk8397/zyqDTZPKPl/wCt/wCutT/vJJv3n7qgzLXl/wDPSP8Ae1Elx5M37uT/AFf/AC1/56VP51vJ/q/3dZ1xH5k37v8A5Z0ARX2pXl1ef88oqtfbvs/7v915tZz/ALyaLzLg/wCt/wBbU89rHJD5nmf9cqAJUt45P9XUTx28cPl1a8vzKqyWv77/AFdBoUEuvs/+rjq/a3H+t8v/AFsn/LSqv9m/89Ljyqi8uSP7kcvlf+jKDMleS387/SLfyrqT/ll/zzqJ7j7P+6t5P3sn/LWokh8y8/ef+jKm+z/vovLjlloNDT021jkhMlx+9q+kNvJ+8j/exVQ/5bRR+XUv2qP/AFfl/vY/+mVBmDx28dnFH/rZatWknlw/vI/3slULu4jk/dyVasbf/Q/9ZQBfe48yz/ef9sq5fUo5PJ8ySuu8lPJ8uT97L/yyrKvbX7PaeZIfNi8qgDnLeTjzP3vleb+9lqW4kl/eyeX+9qWeSOSbzJD/AN+qoT/u/wB5b0AX9N/eQxR+Z5UUn+r/AOulbLxxxzeZ5nmy/wDPKuX+1eZ5scn/AGyo/eW95bXkf73/AJ60Gh0cEn2e8iqd/wB5N9nj/wCWf/LWsn+0v+Jb5fl/6uiC+t5D+8TzYqDMleTy7zy7e3lrR+z/AOhVTS4jj1L93H5X/TKrX9pQXE3lxmKgDGuLeS1m/dyVK915n7uT93+6qV7X9z5kkf72T/V09I47j95JQBkQf89KtTyeZNv/AOWtTXEnmf8AXKs94/3PmR0Gha02PzD+8k8qKStmWGOObzPtH73/AJ5Vy/2r/lnJQ8cnnWt5H/rY6AN7zvJvIpP+elWpJPMvJbdP3XmfvPNrMS+8uKXP73/nrUMV9HJ+7kj82KgzLV1JJDefu45fNq+lv+5lkqgk0cd5FJHH5UVX5NSt5Jv3Zi82gDBntZLe88xLj/Wf8s6l+1eXPaxyf8s6vva/624k/wCWn+rqrJHHJDF5n/XOgC0kn2f/AJaVa+1SSS/vI+axrWOTzvL/APIlX/8AyFQBLdR+Z/q5Iooo6y0juP7S8v8A1VD3Ennfu45fKrQgjj877R+6oNCF7UwzReZH5v8A1zq1cW8ccUXlySxS0J5k03meZUt35nneZHQZkrxyR+V+8/1lZd9HJ9s8uPyvKouri48mKOokk/c/6zzfMoAtW8f2j7nlf9tasfaEjm/56+X/ANNaZax+X/0yp88ccn+rki/ef88qAEfUo/8AWf8Aoun3GpRvDFHJ/wAs6ofZfL82Oqvl+ZD5f/PSgCW6+z3E0X7uWKXzf+WX7ui+t4/scXlx/vf+mVDx/uYqLrzI/wDSJP3X/LPyqAKCW9v5MUdx5vm/6z91V94fM/dx/ZZaxvMj/dSSeb+8l8utmD935vlyUARJb29xDLbx/wDLOovs/wBnvPL/AHUXl1aT/RbzzJP+WlSp5dxN5klAFJ/9T5n72m2VvH53mVf8vy5ovMj83zKE8v7Z/wAsqAL/AJb/APPTzfLqqkP2X95/yy/6a1qTyJHNF+7qrdyR/ZP9Z/11oAoXclvJCf3f/fqonjjkhijjjqW4jjupvMj/AOWdS2Pl+TL9ok8qgDLS3t/O8uSOX/W/8sqtfZ/+WcflVlz3EfnS3H73yvN8utRPL/5d5P8Av7QAfZbf97b/APLWqD2/2eaKP91FVr/UzRSf89KljkjmvD5n+q/5ZUAVX/eHzP3v/bKorKP995lX/wDV/vP+WUlSpH/pn7yOKKg0L/7zyfLjkil/5aVS+y/8tEjl/efvK3EheS0ik+z/ALqOsu7uP337v7ZF/wBMqDMy31b/AEP/AFcXlf8AXKpbe6jk/wBZH5tVUt476by/+WX/AE1qJ/LivPL/AOWsdAF/7VGl5LJ5cX/bKov7SjuJovLjil/65Vkzyfvv9XLVyCOOOKL935UtZgbySeZD+8/deZUqfvP3cf8Ayzlqh5ckkJjjq0n7uH93/rZK0NAu/Lk/ef8APSqHlvJN5dvb1s/vJIZY/LirGS3kt5vM8yWKKT/prQBfgmjj/ef+jal8uOSH/j3l/wC2VZd7HJJD5kdCTSf8e8clAFqCN/tcv/XKoktf9Nkkk/49Y6tQSRxzeYf+2tWkjjktP9Z/rKDMoJNb+T/q/wB1/wA9aL399N5cf7qpZ4ZI/wDVx1QuLry4ZfMkxLHQBV+w/wDPST91Vr95H5Vvbxxf9taxvtUn2yLzP9VJ/wAta2Xt48+Z/wAsqALSW/mTRR3Hmx1LcQxwwy+X/qqtJ5cn7ySSsueaP/j3j60AP/1E0UnmS/vP9VVK4vpI5opJP3Xl1K9vcXH2X/nlRPYxyQy+ZHQBf+0R3VnFJHUV1b/ufLkoSPy4oo4/+/dWjJHMYo5KAMtP9T/y1q1cR/6qOP8A5aVauLfy/uVQeTy/9ZJQBQ/s3y/N8y4/8i1Kn7mGK3t44v3n/PX/AFlUnupPO/eVqfZfMP2iP/lnQA/7P/qkuI//AI3Vq4hjhhlkj/e1Yt/LkiikkkqlPcRx+bbx/wCtkoAieO4js/tFvz/11lrUSSO6vLWOS38r/npL/wA9KoW/7yHy/wDllUtvb+XefaPM/wC/tAGzezSR/wCj2/misu4/10XmW/my+V/z1qW6uvL/AHkf+t/5a1lvdeXNFJJJ+98r/llQB//Z);color: #E2C7A3;text-align: center; }
      p { margin-top: 1em; }
      h1,h2,h3,h4,h5,h6 {width: auto;color: #27a052;margin-top: 1em;margin-bottom: 0.5em; }
      h1, h2 { margin: 0; padding: 2px 2px 0 2px; }
      h1 { font-size: 24px; }
      h2 { font-size: 18px; }
      h3 { margin: 0 0 4px 0; font-size: 16px; }
      a { color: #666688; text-decoration: none; }
      a:hover, a:active { color: #333; }
      ul,ol {padding-left: 20px; }
      ul.float,ol.float {padding: 0;margin: 0; }
      ul.float li,ol.float li {display: inline;float: left;padding-right: 6px;margin-right: 6px;list-style-type: none;border-right: 2px solid #333; }
      .clear {clear: both; }
      .hide, .charts span {display: none; }
      .center {text-align: center; }
      .float {float: left; }
      .mt {margin-top: 20px; }
      .mb {margin-bottom: 20px; }
      .force-wrap {word-wrap: break-word; }
      .mono {font-family: "Lucida Console", Monaco, monospace;font-size: 12px; }
      .toggle,.toggle-details {cursor: pointer;background-image: url(data:image/png;base64,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);background-repeat: no-repeat; }
      h2.toggle {padding-left: 18px;background-size: 16px auto;background-position: 0 4px; }
      h2.toggle:hover {color: #47c072; }
      h2.open {margin-bottom: 10px;background-position: 0 -18px; }
      #home-toc h2.open {margin-top: 20px; }
      h3.toggle {padding-left: 16px;background-size: 14px auto;background-position: 0 2px; }
      h3.toggle:hover {text-shadow: 0 0 2px #47c072; }
      h3.open {background-position: 0 -17px; }
      h4.toggle {margin: 0 0 8px 0;padding-left: 12px; }
      h4.open {background-position: 0 6px; }
      a.toggle-details { margin: 0 0 8px 0; padding-left: 12px; background: url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAkAAAAXCAYAAADZTWX7AAAABGdBTUEAANbY1E9YMgAAABl0RVh0U29mdHdhcmUAQWRvYmUgSW1hZ2VSZWFkeXHJZTwAAADiSURBVHjaYvz//z/DrFmzGBkYGLqBeG5aWtp1BjTACFIEAkCFZ4AUNxC7ARU+RlbEhMT+BMQaQLwOqEESlyIYMIEqlMenCAQsgLiakKILQNwF47AgSfyH0leA2B/o+EfYTOID4gdA7IusAK4IGk7ngNgPqOABut3I1uUDFfzA5kB4YOIDTAxEgOGtiAUY2vlA2hCIf2KRZwXie6AQPwzEFUAsgUURSGMQEzAqQHFmB8R30BS8BWJXoPw2sJuAjNug2Afi+1AFH4A4DCh+GMXhQIEboHQExKeAOAbI3weTAwgwAIZTQ9CyDvuYAAAAAElFTkSuQmCC) 0 4px no-repeat; }
      a.open { background-position: 0 -11px; }
      td.small a.toggle-details { background-position: 0 2px; }
      td.small a.open { background-position: 0 -13px; }
      .toggle-content { display: none; }
      #active-help, .help-box { display: none; }
      #active-help { position: absolute; width: auto; padding: 3px; background: #333; z-index: 10; }
      #active-help-dynamic { padding: 6px 6px 18px 6px; background: #eeeef5; outline: 1px solid #ddd; font-size: 13px; text-align: left; }
      #active-help .close { position: absolute; right: 10px; bottom: 4px; }
      .help-box h3 { margin: 0; font-size: 12px; }
      .help-box p { margin: 0 0 10px 0; }
      .help-box { border: 1px solid #ccc; background-color: #333; padding: 10px; }
      a.help { cursor: help; }
      .section {position: relative;width: 1200px;padding: 4px 8px;margin-left: auto;margin-right: auto;margin-bottom: -1px;background: #160f0b;text-align: left;-moz-box-shadow: 0px 0px 8px #160f0b;-webkit-box-shadow: 0px 0px 8px #160f0b;box-shadow: 0px 0px 8px #160f0b; }
      .section-open { margin-top: 25px; margin-bottom: 25px; -moz-border-radius: 10px; -khtml-border-radius: 10px; -webkit-border-radius: 10px; border-radius: 10px; }
      .grouped-first { -moz-border-radius-topright: 10px; -moz-border-radius-topleft: 10px; -khtml-border-top-right-radius: 10px; -khtml-border-top-left-radius: 10px; -webkit-border-top-right-radius: 10px; -webkit-border-top-left-radius: 10px; border-top-right-radius: 10px; border-top-left-radius: 10px; }
      .grouped-last { -moz-border-radius-bottomright: 10px; -moz-border-radius-bottomleft: 10px; -khtml-border-bottom-right-radius: 10px; -khtml-border-bottom-left-radius: 10px; -webkit-border-bottom-right-radius: 10px; -webkit-border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; border-bottom-left-radius: 10px; }
      .section .toggle-content { padding: 0 0 20px 14px; }
      #raid-summary .toggle-content { padding-bottom: 0px; }
      ul.params { padding: 0; }
      ul.params li { float: left; padding: 2px 10px 2px 10px; margin-right: 10px; list-style-type: none; background: #eeeef5; font-family: "Lucida Grande", Arial, sans-serif; font-size: 12px; }
      .player h2 { margin: 0; }
      .player ul.params { position: relative; top: 2px; }
      #masthead img { display: block; margin-left: auto; margin-right: auto; }
      #masthead h2 { margin: 10px 0 5px 0; }
      #notice { border: 1px solid #ddbbbb; background: #ffdddd; font-size: 12px; }
      #notice h2 { margin-bottom: 10px; }
      #masthead ul.toc { padding: 0; }
      #masthead ul.toc li { list-style-type: none; }
      #masthead ul.toc li ul { padding-left: 18px; }
      #masthead ul.toc li ul li { list-style-type: circle; font-size: 13px; }
      .charts { margin: 10px 60px 0 4px; float: left; width: 550px; text-align: center; }
      .charts img { padding: 8px; margin: 0 auto; margin-bottom: 20px; border: 1px solid #ccc; -moz-border-radius: 6px; -khtml-border-radius: 6px; -webkit-border-radius: 6px; border-radius: 6px; -moz-box-shadow: inset 1px 1px 4px #ccc; -webkit-box-shadow: inset 1px 1px 4px #ccc; box-shadow: inset 1px 1px 4px #ccc; }
      .talents div.float { width: auto; margin-right: 50px; }
      table.sc { border: 0; background-color: #eee; }
      table.sc tr { background-color: #fff; }
      table.sc tr.head { background-color: #aaa; color: #fff; }
      table.sc tr.odd { background-color: #f3f3f3; }
      table.sc th { padding: 2px 4px; text-align: center; background-color: #aaa; color: #fcfcfc; }
      table.sc th.small { padding: 2px 2px; font-size: 12px; }
      table.sc th a { color: #fff; text-decoration: underline; }
      table.sc th a:hover, table.sc th a:active { color: #f1f1ff; }
      table.sc td { padding: 2px 4px; text-align: center; font-size: 13px; }
      table.sc td.small { padding: 2px 2px; font-size: 11px; }
      table.sc th.left, table.sc td.left, table.sc tr.left th, table.sc tr.left td { text-align: left; padding-right: 6px; }
      table.sc th.right, table.sc td.right, table.sc tr.right th, table.sc tr.right td { text-align: right; padding-right: 6px; }
      table.sc tr.details td { padding: 0 0 15px 15px; text-align: left; background-color: #fff; }
      table.sc tr.details td ul { padding: 0; margin: 4px 0 8px 0; }
      table.sc tr.details td ul li { clear: both; padding: 2px; list-style-type: none; }
      table.sc tr.details td ul li span.label { display: block; float: left; width: 150px; margin-right: 4px; background: #f3f3f3; }
      table.sc tr.details td ul li span.tooltip { display: block; float: left; width: 190px; }
      table.sc tr.details td ul li span.tooltip-wider { display: block; float: left; width: 350px; }
      table.sc tr.details td div.float { width: 350px; }
      table.sc tr.details td div.float h5 { margin-top: 4px; }
      table.sc tr.details td div.float ul { margin: 0 0 12px 0; }
      table.sc td.filler { background-color: #ccc; }
      table.sc .dynamic-buffs tr.details td ul li span.label { width: 120px; }
      .sample-sequence { width: 500px; word-wrap: break-word; outline: 1px solid #ddd; background: #fcfcfc; padding: 6px; font-family: "Lucida Console", Monaco, monospace; font-size: 12px; }
    </style>

  </head>

  <body>

    <div id="active-help">
      <div id="active-help-dynamic">
        <div class="help-box">
        </div>
        <a href="#" class="close">close</a>
      </div>
    </div>
    <img src="Welcome.png" alt="SimulationCraft logo" height="347" width="800">

    <div id="masthead" class="section section-open">

      <p>SimulationCraft is a tool to explore combat mechanics in the popular MMO RPG World of Warcraft. It is a multi-player event-driven simulator written in C++ that models raid damage. Increasing class synergy and the prevalence of proc-based combat modifiers have eroded the accuracy of traditional calculators that rely upon closed-form approximations to model very complex mechanics. The goal of this simulator is to close the accuracy gap while maintaining a performance level high enough to calculate relative stat weights to aid gear selection.</p>
      <p>One of the common criticisms of theorycrafting tools is that they model <i>Patchwerk</i> style fights in which the DPS players are able to focus exclusively on generating damage without worrying about standing in molten lava or other such trivial distractions. While truly modeling individual boss fights is a near-impossible task, SimulationCraft does support a wide array of raid events. See "Modeling Raid Events" and the Examples tab for details.</p>

      <h2 class="toggle">Table of Contents</h2>
      <div class="toggle-content">
        <ul class="toc">
          <li><a href="#how-this-interface-works">How This Interface Works</a></li>
          <li><a href="#navigation">Navigation</a></li>
          <li><a href="#tips-and-tricks">Tips and Tricks</a></li>
          <li><a href="#global-parameters">Global Parameters</a></li>
          <li><a href="#scale-factors">Scale Factors</a></li>
          <li><a href="#plots">Plots</a></li>
          <li><a href="#importing-from-the-armory">Importing from the Armory</a></li>
	      <li><a href="#creating-a-custom-profile">Creating A Custom Profile</a></li>
          <li><a href="#saving-results">Saving Results</a></li>
          <li><a href="#priority-lists-not-rotations">Priority Lists, Not Rotations</a></li>
          <li><a href="#modeling-raid-events">Modeling Raid Events</a></li>
        </ul>
      </div>
    
    </div>

    <div id="how-this-interface-works" class="section grouped-first">

      <h2 class="toggle">How This Interface Works</h2>
      <div class="toggle-content">
        <p>At its core, SimulationCraft is a parameter-driven batch simulation tool. This interface is a very light-weight wrapper that simply helps you build configuration scripts, pass them to the simulator, and then evaluate the results. It relies upon existing character profile management sites to provide interactive manipulation of character talents, glyphs, gear, enchants, etc. The three key steps are:</p>
        <ol class="mt">
          <li>Generate your profile.</li>
          <li>Simulate your profile.</li>
          <li>Evaluate your results.</li>
        </ol>
      </div>
      
    </div>
    
    <div id="navigation" class="section">

      <h2 class="toggle">Navigation</h2>
      <div class="toggle-content">
        <p>In general the flow is from left to right across the main tabs at the top of the window.</p>
        <ul class="mt">
          <li>At <i>Options</i> you set the global sim options, such buffs, scaling, plots and how many targets are in the simulation.</li>
          <li>At <i>Import</i> you load your character profile from a variety of sources.</li>
          <li>At <i>Simulate</i> you make character-specific tweaks, such as gear and action list changes.</li>
          <li>At <i>Overrides</i> a super-user can specify custom parameters that will persist from run to run.</li>
          <li>At <i>Help</i> you can check out our wiki, which will give an explanation of all supporting parameters, including common usage.</li>
          <li>At <i>Log</i> you will find simple text reporting as well as any errors encountered.</li> 
          <li>At <i>Results</i> you will find HTML output generated by the simulator.</li>
		  <li>At <i>Spell Query</i> you will be able to query everything there is to know about a spell.</li>
        </ul>
        <p>Please note that the behavior of the command line and the buttons to either side are context-sensitive. Their function and labels may change as you navigate the main tabs.</p>
      </div>
    
    </div>
    
    <div id="tips-and-tricks" class="section">
    
      <h2 class="toggle">Tips and Tricks</h2>
      <div class="toggle-content">
        <ul class="mt spaced">
          <li>The label on the main button changes between <i>Simulate!</i>, <i>Import!</i> and <i>Save!</i> depending upon the active main tab. While a character profile is being imported or a simulation is running, the label will be set to <i>Cancel!</i>. Cancelling a simulation run early may still result in valuable information: The results posted are a statistical analysis of many iterations. Cancelling a simulation will simply reduce the number of iterations, increasing the reported statistical error.</li>
          <li>The progress bar is context-sensitive. When viewing a web page under <i>Import</i> or <i>Results</i> it will display the percent-completion of rendering the web page. Under all other tabs, the progress bar will report the percent-completion of the character import or the simulation run.</li>
          <li>The <i>Back</i> and <i>Forward</i> buttons to the left of the command-line allow you to cycle through the history of the main viewport. When viewing a web page under <i>Import</i> or <i>Results</i> they will cycle through the <i>URL</i> history. <b>When viewing <i>Globals</i>, <i>Simulate</i>, or <i>Overrides</i>, these buttons will cycle through previous states.</b> This history is persistent between runs.</li>
          <li>With the exception of the <i>Import</i> tab, pressing the <i>Enter</i> key while the command-line has focus is equivalent to clicking the main button.</li>
          <li><b>When the command-line has focus, the <i>Up</i> and <i>Down</i> keys cycle through the history of command-line text.</b> This history is persistent between runs.</li>
          <li>The configuration script passed to the simulator is constructed by first adding the options implied by the button-settings under <i>Options</i>, followed by the text under <i>Simulate</i>, followed by the text under <i>Overrides</i> and finally including anything on the command line. It is important to understand that the last setting <i>wins</i>. <b>Users already comfortable with earlier releases of the command-line-only application can use the the GUI command-line in the same manner.</b> For example, one could type <i>armory=us,llane,segv</i> to download and simulate the Hunter Segv from server US-Llane.</li>
          <li>Any option that does not fit the <i>parm=value</i> paradigm is assumed to be the name of a script file which is subsequently loaded. For example, typing <b>Raid_T17M.simc</b> on the command-line will load and simulate the shipped script that contains all supported class/spec combinations using T17M gear.  While the history mechanism of the <i>Back</i> and <i>Forward</i> buttons should be sufficient for most needs, it can be convenient to save complicated parameter settings in an external file.
        </ul>
      </div>
      
    </div>
    
    <div id="global-parameters" class="section">
    
      <h2 class="toggle">Global Parameters</h2>
      <div class="toggle-content">
        <p>SimulationCraft has many, many options that can be loosely categorized into two groups, those that are applied to the <i>active</i> player and those that are applied globally. The <i>Globals</i> section of the <i>Options</i> tab is dedicated to the more commonly changed global parameters.</p>
        <ul class="mt spaced">

          <li><b>Version:</b> The mechanics of <i>World of Warcraft</i> are constantly changing. SimulationCraft endeavors to model the two most recent patch levels. This corresponds to <i>Live</i> and <i>PTR</i>. The <i>Both</i> option will simulate two characters at once: One character will run with Live mechanics and a second character with the name <i>EvilTwinPTR</i> will be run with PTR mechanics. The results will shown side-by-side for easy comparison.</li>
        
          <li><b>Latency:</b> During simulation we do not attempt to model the actual latency in all internet traffic, but rather how that latency manifests itself in terms of the inevitable gaps between player actions. Rigorous testing has shown that these gaps differ greatly depending upon whether the preceding action has a cast-time (enabling queueing), is instant-cast (triggering the <i>GCD</i>), or is channeled (requiring extra care not to clip the last tick). SimulationCraft uses random values for these <i>gaps</i>, whose range is centered on user-supplied values.</li>
        
          <li><b>Iterations:</b> The largest drawback of simulation is that in order to get meaningful answers, one must run the simulation many, many times to acquire a trustworthy statistical average. More iterations means better accuracy, at the cost of longer runtimes. When calculating <i>scale factors</i> one must iterate enough such that the simulation error margin is sufficiently smaller than the change in stat value being analyzed.</li>
        
          <li><b>Length(sec):</b> It is important to understand that the length of the fight is a goal and not a hard limit. Half-way through the first iteration the health of the target (<i>boss</i>) is estimated based upon damage done up to that point. The simulation ends when the target dies and <i>not</i> when the desired fight length is reached. At the end of every proceeding iteration, the target health is re-evaluated based upon whether the fight ended early or late. The end result is that after many iterations, the average fight length matches the desired value.</li>
        
          <li><b>Vary Length:</b> In order to smooth out the effects of very powerful procs and/or abilities one can tell the simulator to vary the fight length. The sim will perform an even distribution of fight lengths from <i>length-variance</i> to <i>length+variance</i>.</li>
        
          <li><b>Adds:</b> While it is possible to specify raid events that spawn adds that are only temporary, this option specifies adds that are present the entire fight.</li>
        
          <li><b>Fight Style:</b> One of the most common criticisms of theorycrafting tools is that they only model <i>tank-n-spank</i> fights in which the player gets to stand perfectly still just hitting keys/buttons. SimulationCraft supports a wide array of <i>raid events</i> including movement, stuns, splash damage, target (in)vulnerability, etc. The <i>Helter Skelter</i> option throws in an interesting mix of these random events so that the user may see how badly DPS suffers under adverse conditions. The Hectic Cleave option puts the character into an encounter that is intended to simulate the Horridon from Throne of Thunder.</li>
                
          <li><b>Player Skill:</b> Whenever a player is considered <i>ready to do something</i> the sim walks through their prioritized list of actions and executes the very first one that is available. If a player is below 100% skill then there is a chance that it may skip an action even if it is available and make use of a lower-priority action instead. DoTs are considered available when the last tick will occur after <i>current_time+dot_cast_time</i>.
        
          <li><b>Threads:</b> The iterative nature of SimulationCraft means that parallel processing is a very effective means to increase performance. Most modern desktops have at least two CPU cores. Match the number of threads to the number of CPU cores for optimal performance.</li>
		  
		  <li><b>Thread Priority:</b> When setting Threads to max, it can leave the computer in such a state that it is unusable for anything else while the simulation is running. When this is set to 'lowest', the operating system will yield cpu time to any other application that requests it, which will allow multitasking in most situations. </li>
        
          <li><b>Deterministic RNG:</b> One of the most difficult problems associated with simulation is variance. Decreasing variance will reduce the number of iterations required to reach a confidence level in the results. Turning on <i>Deterministic</i> RNG will introduce determinism in both the cycle and the distribution of individual RNG calculations. This will increase convergence by an order of magnitude, allowing the use of far fewer iterations. However, as with formulation, introducing this kind of determinism into the RNG can interfere with the scale factor generation of non-linear stats. Use with caution.</li>
        
          <li><b>Armory Region:</b> This option controls which region (US, EU, TW, CN) the Armory Import tab uses. <li><b>Armory Spec:</b> Players may have two talent/glyph combinations, but only one may be active at a time. When a player is downloaded from the Armory, by default the <i>active</i> talent/glyph
 setup is used. This parameter allows one to access the <i>inactive</i> setup during import.</li>
        
          <li><b>Generate Debug:</b> This option provides a peek under the hood.  Running with <i>Log Only</i> will force the number of iterations to just one and generate a human-readable combat log that is stored in the <i>Log</i> tab.  Running with <i>Gory Details</i> dramatically increases the size of the combat log and makes it considerably less human-readable.  It will also provide detailed debug output during character import.  Since turning on debug allows only one iteration, scale factor and plot generation are turned off.</li>

        </ul>
      </div>

    </div>

    <div id="scale-factors" class="section">
    
      <h2 class="toggle">Scale Factors</h2>
      <div class="toggle-content">
        <p>Scale factors represent the change in DPS per change in stat value. They are helpful in evaluating incremental gear changes, and are calculated by first making a baseline simulation and then comparing it against subsequent runs in which one stat is increased or decreased. To calculate trustworthy scale factors, the simulation margin of error must be sufficiently smaller than the change in stat value. To reduce the margin of error a large number of iterations is required. Since multiple simulations must be run with a higher-than-normal number of iterations, calculating scale factors can take 50x to 100x longer than a standard DPS run.</p>
        <p class="center"><b>ScaleFactor(Stat) = ( DPS(Baseline+StatDelta) - DPS(Baseline) ) / StatDelta</b></p>
        <p>By default, SimulationCraft uses stat deltas between 150 and 300 depending upon the stat. Most stat deltas are positive, measuring the <i>increase</i> in DPS. </p>
      </div>
    
    </div>
    
    <div id="plots" class="section">

      <h2 class="toggle">Plots</h2>
      <div class="toggle-content">
        <p>SimulationCraft will optionally generate DPS-per-stat graphs using a +/- 50 stat point range given the initial gear point. It should be noted that simulation is not well suited for plot generation. Precision is not as important so fewer iterations are required. However, the sheer number of sample points needed to generate a plot make it even more cpu-intensive than scale factor generation.</p>
      </div>
    
    </div>
    
    <div id="importing-from-the-armory" class="section">
    
      <h2 class="toggle">Importing from the Armory</h2>
      <div class="toggle-content">
        <p>To download a character from the Armory, merely navigate the web view to a character profile page. The easiest way to do this is via the <i>Search</i> mechanism. The <i>Armory Spec</i> toggle under <i>Options</i> controls whether to use the <i>active</i> or the <i>inactive</i> talent setup. There is no need to wait for the web page to be fully rendered. The import process uses the URL at the command-line. Note that the URL at the command-line can be modified directly in the same manner as a normal web browswer. <b>The Armory tab defaults to US region. See the <i>Options</i> tab to change the region to EU, TW, or CN.</b></p>
      </div>
      
    </div>
        
    <div id="saving-results" class="section">
    
      <h2 class="toggle">Saving Results</h2>
      <div class="toggle-content">
        <p>To save text that may be conveniently cut-and-pasted into an email or forum, go to the <i>Log</i> tab and select the portion of interest. To save the entire log, specify the file name at the command line and press <i>Save!</i>.</p>
        <p>To save HTML, go to the <i>Results</i> tab, specify the file name at the command line, and press <i>Save!</i>.</p>
        <p>These HTML files are all-inclusive. The images are generate using GoogleCharts which means that the HTML cannot be viewed offline.</p>
      </div>
      
    </div>
    
    <div id="priority-lists-not-rotations" class="section">
    
      <h2 class="toggle">Priority Lists, Not Rotations</h2>
      <div class="toggle-content">
        <p>A key part of the simulation is the player artificial intelligence. It is important to understand that there is no <i>rotation</i> to specify. Instead, the player is given a <i>priority list</i> of actions. Whenever the player is looking for something to do (just finished an action and not waiting on the GCD), it will simply walk the list of actions and perform the first one that is <i>ready</i>. For example, <i>damage-over-time</i> actions are not ready until they are finished ticking (minus the cast-time, of course). Temporary buffs and <i>cooldowns</i> can also prevent certain actions from being considered <i>ready</i>. There are a large variety of conditionals that can be applied to each action in the priority list that can limit their execution even further. The default action list (<i>actions+=</i>) generated during character import will demonstrate this. For more details, see the <i>Help</i> tab.</p>
      </div>
      
    </div>
    
    <div id="modeling-raid-events" class="section grouped-last">
    
      <h2 class="toggle">Modeling Raid Events</h2>
      <div class="toggle-content">
        <p>Experimenting with SimulationCraft's various supported events allows one to determine how susceptible talent/gear combinations are to adverse conditions. The intervals and durations of each event are user-specified, as are the degree of randomness applied to each. Supported events include: movement, stuns, AoE damage, adds spawning, boss (in)vulnerability, boss casting requiring use interrupts.</p>
        <ul class="mt spaced">
          <li><a href="#events-movement" class="help">Movement</a>: When a player is <i>moving</i> auto-attacks are halted and only ranged instant-casts are allowed. To further control the actions taken while moving, there is a <i>moving=1</i> conditional that can be specified with actions to prevent them from being used in normal circumstances.</li>
          <li><a href="#events-stun" class="help">Stun</a>: While <i>stunned</i>, even instant-casts are forbidden.</li>
          <li>Damage: The <i>damage</i> event is used to model splash damage from AoE.</li>
          <li><a href="#events-adds" class="help">Adds</a>: Note that some modules have implemented more aoe/multi-dot capability, so some modules take more advantage of adds spawning than others.</li>
          <li>Vulnerable: Boss <i>vulnerability</i> is a 2x damage multiplier.</li>
          <li><a href="#events-invulnerable" class="help">Invulnerable</a>: Boss <i>invulnerability</i> will remove any existing player DoTs on the target, but it does not yet remove player debuffs.</li>
          <li><a href="#events-casting" class="help">Casting</a>: The <i>casting</i> event is used to model players changing their rotations to perform interrupts. Player interrupt actions are not considered <i>ready</i> unless the boss is in mid-cast. This means that it is safe to give these actions a high-priority because they will not be used unless necessary.</li>
        </ul>
      
        <p>These are only a few examples. For more details, see the <i>Help</i> tab.</p>
      </div>

    </div>

    <!-- Help Boxes -->
    <div id="events-movement">
      <div class="help-box">
        <h3>Every 30sec, all players and pets are required to move for 5sec</h3>
        <p>raid_events+=/movement,cooldown=30,duration=5</p>
        <h3>Every 30sec, all players are required to move for 5sec while pets remain stationary</h3>
        <p>raid_events+=/movement,players_only=1,cooldown=30,duration=5</p>
        <h3>Every 40sec, all players within 5yards are required to move for 7sec</h3>
        <p>raid_events+=/movement,cooldown=40,duration=7,distance<=5</p>
      </div>
    </div>
    <div id="events-stun">
      <div class="help-box">
        <h3>Every 60sec, all players are stunned for 2sec</h3>
        <p>raid_events+=/stun,cooldown=60,duration=2</p>
      </div>
    </div>
    <div id="events-adds">
      <div class="help-box">
        <h3>Every 1min, 3 additional targets are nearby the boss and remain for 20sec</h3>
        <p>raid_events+=/adds,count=3,cooldown=60,duration=20</p>
      </div>
    </div>
    <div id="events-invulnerable">
      <div class="help-box">
        <h3>Every 2min, the target becomes invulnerable, interrupting players and shedding all DoTs.</h3>
        <p>raid_events+=/invulnerable,cooldown=120,duration=3</p>
      </div>
    </div>
    <div id="events-casting">
      <div class="help-box">
        <h3>Interrupt boss's 3 second cast every 30 seconds starting 15sec into fight</h3>
        <p>raid_events+=casting,cooldown=30,duration=3,first=15</p>
      </div>
    </div>

    <script type="text/javascript" src="https://ajax.googleapis.com/ajax/libs/jquery/1.12.4/jquery.min.js"></script>

    <script type="text/javascript">
      jQuery.noConflict();
      jQuery(document).ready(function($) {
        $('a[ rel="_blank"]').each(function() {
          $(this).attr('target', '_blank');
        });
        $('.toggle-content, .help-box').hide();
        $('.open').next('.toggle-content').show();
        $('.toggle').click(function(e) {
          var img_id = '';
          var src = '';
          var target = '';
          e.preventDefault();
          $(this).toggleClass('open');
          var section = $(this).parent('.section');
          if (section.attr('id') != 'masthead') {
            section.toggleClass('section-open');
          }
          if (section.attr('id') != 'masthead' && section.hasClass('section-open')) {
            section.removeClass('grouped-first');
            section.removeClass('grouped-last');
            if (!(section.next().hasClass('section-open'))) {
              section.next().addClass('grouped-first');
            }
            if (!(section.prev().hasClass('section-open'))) {
              section.prev().addClass('grouped-last');
            }
          } else if (section.attr('id') != 'masthead') {
            if (section.attr('id') == 'auras-and-debuffs' || section.next().hasClass('section-open')) {
              section.addClass('grouped-last');
            } else {
              section.next().removeClass('grouped-first');
            }
            if (section.prev().hasClass('section-open')) {
              section.addClass('grouped-first');
            } else {
              section.prev().removeClass('grouped-last');
            }
          }
          $(this).next('.toggle-content').toggle(150);
          $(this).next('.toggle-content').find('.charts').each(function() {
            $(this).children('span').each(function() {
              img_class = $(this).attr('class');
              img_alt = $(this).attr('title');
              img_src = $(this).html().replace(/&amp;/g, "&");
              var img = new Image();
              $(img).attr('class', img_class);
              $(img).attr('src', img_src);
              $(img).attr('alt', img_alt);
              $(this).replaceWith(img);
              $(this).load();
            });
          });
        });
        $('.toggle-details').click(function(e) {
          e.preventDefault();
          $(this).toggleClass('open');
          $(this).parents().next('.details').toggleClass('hide');
        });
        $('.toggle-db-details').click(function(e) {
          e.preventDefault();
          $(this).toggleClass('open');
          $(this).parent().next('.toggle-content').toggle(150);
        });
        $('.help').click(function(e) {
          e.preventDefault();
          var target = $(this).attr('href') + ' .help-box';
          var content = $(target).html();
          $('#active-help-dynamic .help-box').html(content);
          $('#active-help .help-box').show();
          var t = e.pageY - 20;
          var l = e.pageX - 20;
          $('#active-help').css({top:t,left:l});
          $('#active-help').toggle(250);
        });
        $('#active-help a.close').click(function(e) {
          e.preventDefault();
          $('#active-help').toggle(250);
        });
      });
    </script>

  </body>

</html>
