evoker="TWW1_Evoker_Devastation"
source=default
spec=devastation
level=80
race=dracthyr
role=spell
position=back
talents=CsbBAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAzMzMYmNzgBMwYmpZMzkZbsMmZmxMmZbGwMmZbMzsMmBGYMYBWGjGLDIzAwGA

# Default consumables
potion=tempered_potion_3
flask=flask_of_alchemical_chaos_3
food=feast_of_the_divine_day
augmentation=crystallized
temporary_enchant=main_hand:algari_mana_oil_3

# This default action priority list is automatically created based on your character.
# It is a attempt to provide you with a action list that is both simple and practicable,
# while resulting in a meaningful and good simulation. It may not result in the absolutely highest possible dps.
# Feel free to edit, adapt and improve it to your own needs.
# SimulationCraft is always looking for updates and improvements to the default action lists.

# Executed before combat begins. Accepts non-harmful actions only.
actions.precombat=flask
actions.precombat+=/food
actions.precombat+=/augmentation
# Snapshot raid buffed stats before combat begins and pre-potting is done.
actions.precombat+=/snapshot_stats
actions.precombat+=/variable,name=trinket_1_buffs,value=trinket.1.has_buff.intellect|trinket.1.has_buff.mastery|trinket.1.has_buff.versatility|trinket.1.has_buff.haste|trinket.1.has_buff.crit|trinket.1.is.mirror_of_fractured_tomorrows
actions.precombat+=/variable,name=trinket_2_buffs,value=trinket.2.has_buff.intellect|trinket.2.has_buff.mastery|trinket.2.has_buff.versatility|trinket.2.has_buff.haste|trinket.2.has_buff.crit|trinket.2.is.mirror_of_fractured_tomorrows
# Decide which trinket to pair with Dragonrage, prefer 2 minute and 1 minute trinkets
actions.precombat+=/variable,name=trinket_1_sync,op=setif,value=1,value_else=0.5,condition=variable.trinket_1_buffs&(trinket.1.cooldown.duration%%cooldown.dragonrage.duration=0|cooldown.dragonrage.duration%%trinket.1.cooldown.duration=0)
actions.precombat+=/variable,name=trinket_2_sync,op=setif,value=1,value_else=0.5,condition=variable.trinket_2_buffs&(trinket.2.cooldown.duration%%cooldown.dragonrage.duration=0|cooldown.dragonrage.duration%%trinket.2.cooldown.duration=0)
# Estimates a trinkets value by comparing the cooldown of the trinket, divided by the duration of the buff it provides. Has a intellect modifier (currently 1.5x) to give a higher priority to intellect trinkets. The intellect modifier should be changed as intellect priority increases or decreases. As well as a modifier for if a trinket will or will not sync with cooldowns.
actions.precombat+=/variable,name=trinket_1_manual,value=trinket.1.is.belorrelos_the_suncaller|trinket.1.is.nymues_unraveling_spindle|trinket.1.is.spymasters_web
actions.precombat+=/variable,name=trinket_2_manual,value=trinket.2.is.belorrelos_the_suncaller|trinket.2.is.nymues_unraveling_spindle|trinket.2.is.spymasters_web
actions.precombat+=/variable,name=trinket_1_exclude,value=trinket.1.is.ruby_whelp_shell|trinket.1.is.whispering_incarnate_icon
actions.precombat+=/variable,name=trinket_2_exclude,value=trinket.2.is.ruby_whelp_shell|trinket.2.is.whispering_incarnate_icon
actions.precombat+=/variable,name=trinket_priority,op=setif,value=2,value_else=1,condition=!variable.trinket_1_buffs&variable.trinket_2_buffs|variable.trinket_2_buffs&((trinket.2.cooldown.duration%trinket.2.proc.any_dps.duration)*(1.5+trinket.2.has_buff.intellect)*(variable.trinket_2_sync))>((trinket.1.cooldown.duration%trinket.1.proc.any_dps.duration)*(1.5+trinket.1.has_buff.intellect)*(variable.trinket_1_sync))
# Rank 1 empower spell cast time
actions.precombat+=/variable,name=r1_cast_time,value=1.0*spell_haste
# Variable for when to start holding empowers for upcoming DR in AoE. - From my testing 4sec seems like the sweetspot, but it's very minor diff so far - Holding for more than 6 seconds it begins to become a loss.
actions.precombat+=/variable,name=dr_prep_time_aoe,default=4,op=reset
# Variable for when to start holding empowers for upcoming DR in ST.
actions.precombat+=/variable,name=dr_prep_time_st,default=8,op=reset
actions.precombat+=/variable,name=has_external_pi,value=cooldown.invoke_power_infusion_0.duration>0
# Get Some Scarlet Adaptation Prepull
actions.precombat+=/verdant_embrace,if=talent.scarlet_adaptation
actions.precombat+=/firestorm,if=talent.firestorm&(!talent.engulf|!talent.ruby_embers)
actions.precombat+=/living_flame,if=!talent.firestorm|talent.engulf&talent.ruby_embers

# Executed every time the actor is available.
# Delay pot in ST if you are about to SS - mostly relevant for opener where you want DR->FB->SS->rotation
actions=potion,if=buff.dragonrage.up&(!cooldown.shattering_star.up|active_enemies>=2)|fight_remains<35
# Variable that evaluates when next dragonrage is by working out the maximum between the dragonrage cd and your empowers, ignoring CDR effect estimates.
actions+=/variable,name=next_dragonrage,value=cooldown.dragonrage.remains<?((cooldown.eternity_surge.remains-8)>?(cooldown.fire_breath.remains-8))
# Invoke External Power Infusions if they're available during dragonrage
actions+=/invoke_external_buff,name=power_infusion,if=buff.dragonrage.up&!cooldown.fire_breath.up&!cooldown.shattering_star.up
actions+=/variable,name=pool_for_id,if=talent.imminent_destruction&talent.melt_armor&talent.maneuverability,default=0,op=set,value=cooldown.deep_breath.remains<8&essence.deficit>=1&!buff.essence_burst.up
# Rupt to make the raidleader happy
actions+=/quell,use_off_gcd=1,if=target.debuff.casting.react
actions+=/call_action_list,name=trinkets
actions+=/run_action_list,name=aoe,if=active_enemies>=3
actions+=/run_action_list,name=st

# AOE action list; This is kind of a mess again and should prolly be rewritten completely Open with star before DR to save a global and start with a free EB
actions.aoe=shattering_star,target_if=max:target.health.pct,if=cooldown.dragonrage.up&talent.arcane_vigor|talent.eternitys_span&active_enemies<=3
actions.aoe+=/hover,use_off_gcd=1,if=raid_event.movement.in<6&!buff.hover.up&gcd.remains>=0.5&(buff.mass_disintegrate_stacks.up&talent.mass_disintegrate|active_enemies<=4)
# Spend firestorm procs ASAP
actions.aoe+=/firestorm,if=buff.snapfire.up
# Acquire the buff
actions.aoe+=/firestorm,if=talent.feed_the_flames
# Grab Irid Red before Dragonrage without griefing extension
actions.aoe+=/call_action_list,name=fb,if=talent.dragonrage&cooldown.dragonrage.up&talent.iridescence
actions.aoe+=/deep_breath,if=talent.maneuverability&talent.melt_armor
actions.aoe+=/dragonrage,if=target.time_to_die>=32|fight_remains<30
# Use tip to get that sweet aggro
actions.aoe+=/tip_the_scales,if=buff.dragonrage.up&(active_enemies<=3+3*talent.eternitys_span|!cooldown.fire_breath.up)
# Cast Fire Breath - stagger for swell/blazing shards outside DR DS optimization: Only cast if current fight will last 8s+ or encounter ends in less than 30s
actions.aoe+=/call_action_list,name=fb,if=(!talent.dragonrage|buff.dragonrage.up|cooldown.dragonrage.remains>variable.dr_prep_time_aoe|!talent.animosity)&(target.time_to_die>=8|fight_remains<30)
# Cast Eternity Surge - stagger for swell/blazing shards outside DR DS optimization: Only cast if current fight will last 8s+ or encounter ends in less than 30s
actions.aoe+=/call_action_list,name=es,if=(!talent.dragonrage|buff.dragonrage.up|cooldown.dragonrage.remains>variable.dr_prep_time_aoe|!talent.animosity)&(target.time_to_die>=8|fight_remains<30)
# Cast DB if not in DR and not going to overflow essence.
actions.aoe+=/deep_breath,if=!buff.dragonrage.up&essence.deficit>3
# Send SS when it doesn't overflow EB, without vigor send on CD
actions.aoe+=/shattering_star,target_if=max:target.health.pct,if=buff.essence_burst.stack<buff.essence_burst.max_stack&talent.arcane_vigor|talent.eternitys_span&active_enemies<=3
actions.aoe+=/engulf,if=dot.fire_breath_damage.ticking&(!talent.shattering_star|debuff.shattering_star_debuff.up)&cooldown.dragonrage.remains>=27
actions.aoe+=/disintegrate,target_if=min:debuff.bombardments.remains,if=buff.mass_disintegrate_stacks.up&talent.mass_disintegrate
# Pyre logic Pyre 4T+ Pyre 3T+ if playing Volatility Pyre with 15 stacks of CB
actions.aoe+=/pyre,target_if=max:target.health.pct,if=active_enemies>=4&!variable.pool_for_id
actions.aoe+=/pyre,target_if=max:target.health.pct,if=active_enemies>=3&talent.volatility&!variable.pool_for_id
actions.aoe+=/pyre,target_if=max:target.health.pct,if=buff.charged_blast.stack>=15
# Cast LF with leaping flames up if: not playing burnout, burnout is up or the next firebreath is soon.
actions.aoe+=/living_flame,target_if=max:target.health.pct,if=(!talent.burnout|buff.burnout.up|cooldown.fire_breath.remains<=gcd.max*5|buff.scarlet_adaptation.up|buff.ancient_flame.up)&buff.leaping_flames.up&!buff.essence_burst.up&essence.deficit>1
# Yoinked the disintegrate logic from ST
actions.aoe+=/disintegrate,target_if=max:target.health.pct,chain=1,early_chain_if=evoker.use_early_chaining&ticks>=2&(raid_event.movement.in>2|buff.hover.up),interrupt_if=evoker.use_clipping&buff.dragonrage.up&ticks>=2&(raid_event.movement.in>2|buff.hover.up),if=(raid_event.movement.in>2|buff.hover.up)&&!variable.pool_for_id
# Cast LF with burnout and snapfire proc for those juicy insta firestorms
actions.aoe+=/living_flame,target_if=max:target.health.pct,if=talent.snapfire&buff.burnout.up
actions.aoe+=/firestorm
# Get Ancient Flame as Filler
actions.aoe+=/call_action_list,name=green,if=talent.ancient_flame&!buff.ancient_flame.up&!buff.dragonrage.up
# Fallback filler
actions.aoe+=/azure_strike,target_if=max:target.health.pct

# Eternity Surge, use rank most applicable to targets.
actions.es=eternity_surge,empower_to=1,target_if=max:target.health.pct,if=active_enemies<=1+talent.eternitys_span|buff.dragonrage.remains<1.75*spell_haste&buff.dragonrage.remains>=1*spell_haste|buff.dragonrage.up&(active_enemies==5&!talent.font_of_magic|active_enemies>(3+talent.font_of_magic)*(1+talent.eternitys_span))|active_enemies>=6&!talent.eternitys_span
actions.es+=/eternity_surge,empower_to=2,target_if=max:target.health.pct,if=active_enemies<=2+2*talent.eternitys_span|buff.dragonrage.remains<2.5*spell_haste&buff.dragonrage.remains>=1.75*spell_haste
actions.es+=/eternity_surge,empower_to=3,target_if=max:target.health.pct,if=active_enemies<=3+3*talent.eternitys_span|!talent.font_of_magic|buff.dragonrage.remains<=3.25*spell_haste&buff.dragonrage.remains>=2.5*spell_haste
actions.es+=/eternity_surge,empower_to=4,target_if=max:target.health.pct

# Fire Breath, use rank appropriate to target count/talents.
actions.fb=fire_breath,empower_to=1,target_if=max:target.health.pct,if=(buff.dragonrage.remains<1.75*spell_haste&buff.dragonrage.remains>=1*spell_haste)|active_enemies=1|talent.scorching_embers&!dot.fire_breath_damage.ticking
actions.fb+=/fire_breath,empower_to=2,target_if=max:target.health.pct,if=active_enemies=2|(buff.dragonrage.remains<2.5*spell_haste&buff.dragonrage.remains>=1.75*spell_haste)|talent.scorching_embers
actions.fb+=/fire_breath,empower_to=3,target_if=max:target.health.pct,if=!talent.font_of_magic|(buff.dragonrage.remains<=3.25*spell_haste&buff.dragonrage.remains>=2.5*spell_haste)|talent.scorching_embers
actions.fb+=/fire_breath,empower_to=4,target_if=max:target.health.pct

# Green Spells used to trigger Ancient Flame
actions.green=emerald_blossom
actions.green+=/verdant_embrace

# ST Action List, it's a mess, but it's getting better!
actions.st=use_item,name=kharnalex_the_first_light,if=!buff.dragonrage.up&debuff.shattering_star_debuff.down&raid_event.movement.in>6
# Movement Logic, Time spiral logic might need some tweaking actions.st+=/time_spiral,if=raid_event.movement.in<3&cooldown.hover.remains>=3&!buff.hover.up
actions.st+=/hover,use_off_gcd=1,if=raid_event.movement.in<6&!buff.hover.up&gcd.remains>=0.5
actions.st+=/deep_breath,if=talent.maneuverability&talent.melt_armor
# &(talent.onyx_legacy|cooldown.dragonrage.remains<=2&((cooldown.fire_breath.remains<6|cooldown.eternity_surge.remains<6&(!set_bonus.tww1_4pc|!talent.mass_disintegrate))&(cooldown.fire_breath.remains<10&(cooldown.eternity_surge.remains<10|set_bonus.tww1_4pc&talent.mass_disintegrate))&(cooldown.deep_breath.remains>10|!talent.melt_armor)&target.time_to_die>=32|fight_remains<32))  Relaxed Dragonrage Entry Requirements, cannot reliably reach third extend under normal conditions (Bloodlust + Power Infusion/Very high haste gear) DS optimization: Only cast if current fight will last 32s+ or encounter ends in less than 30s
actions.st+=/dragonrage,if=(cooldown.fire_breath.remains<4|cooldown.eternity_surge.remains<4&(!set_bonus.tww1_4pc|!talent.mass_disintegrate))&(cooldown.fire_breath.remains<8&(cooldown.eternity_surge.remains<8|set_bonus.tww1_4pc&talent.mass_disintegrate))&target.time_to_die>=32|fight_remains<32
# Tip second FB if not playing font of magic or if playing EBF, otherwise tip ES.
actions.st+=/tip_the_scales,if=(!talent.dragonrage|buff.dragonrage.up)&(cooldown.fire_breath.remains<cooldown.eternity_surge.remains|(cooldown.eternity_surge.remains<cooldown.fire_breath.remains&talent.font_of_magic))
# Throw Star on CD, Don't overcap with Arcane Vigor.
actions.st+=/shattering_star,if=(buff.essence_burst.stack<buff.essence_burst.max_stack|!talent.arcane_vigor)&(!cooldown.eternity_surge.up|!buff.dragonrage.up|talent.mass_disintegrate|!talent.event_horizon&(!talent.traveling_flame|!cooldown.engulf.up))&(cooldown.dragonrage.remains>=15|cooldown.fire_breath.remains>=8|buff.dragonrage.up&(cooldown.fire_breath.remains<=gcd&buff.tip_the_scales.up|cooldown.tip_the_scales.remains>=15&!buff.tip_the_scales.up)|!talent.traveling_flame)&(!cooldown.fire_breath.up|buff.tip_the_scales.up)
# Fire breath logic. Play around blazing shards if outside of DR. DS optimization: Only cast if current fight will last 8s+ or encounter ends in less than 30s
actions.st+=/call_action_list,name=fb,if=(!talent.dragonrage|variable.next_dragonrage>variable.dr_prep_time_st|!talent.animosity)&(!cooldown.eternity_surge.up|!talent.event_horizon&!talent.traveling_flame|talent.mass_disintegrate|!buff.dragonrage.up)&(target.time_to_die>=8|fight_remains<30)
# Eternity Surge logic. Play around blazing shards if outside of DR. DS optimization: Only cast if current fight will last 8s+ or encounter ends in less than 30s
actions.st+=/call_action_list,name=es,if=(!talent.dragonrage|variable.next_dragonrage>variable.dr_prep_time_st|!talent.animosity|set_bonus.tww1_4pc&talent.mass_disintegrate)&(target.time_to_die>=8|fight_remains<30)
# Wait for FB/ES to be ready if spending another GCD would result in the cast no longer fitting inside of DR
actions.st+=/wait,sec=cooldown.fire_breath.remains,if=talent.animosity&buff.dragonrage.up&buff.dragonrage.remains<gcd.max+variable.r1_cast_time*buff.tip_the_scales.down&buff.dragonrage.remains-cooldown.fire_breath.remains>=variable.r1_cast_time*buff.tip_the_scales.down
actions.st+=/wait,sec=cooldown.eternity_surge.remains,if=talent.animosity&buff.dragonrage.up&buff.dragonrage.remains<gcd.max+variable.r1_cast_time&buff.dragonrage.remains-cooldown.eternity_surge.remains>variable.r1_cast_time*buff.tip_the_scales.down
# Spend the last 1 or 2 GCDs of DR on fillers to exit with 2 EBs
actions.st+=/living_flame,if=buff.dragonrage.up&buff.dragonrage.remains<(buff.essence_burst.max_stack-buff.essence_burst.stack)*gcd.max&buff.burnout.up
actions.st+=/azure_strike,if=buff.dragonrage.up&buff.dragonrage.remains<(buff.essence_burst.max_stack-buff.essence_burst.stack)*gcd.max
actions.st+=/engulf,if=dot.fire_breath_damage.ticking&(!talent.enkindle|dot.enkindle.ticking&(prev_gcd.1.disintegrate|prev_gcd.1.engulf|prev_gcd.2.disintegrate|!talent.fan_the_flames|active_enemies>1))&(!talent.ruby_embers|dot.living_flame_damage.ticking)&(!talent.shattering_star|debuff.shattering_star_debuff.up)&cooldown.dragonrage.remains>=27
# Spend burnout procs without overcapping resources
actions.st+=/living_flame,if=buff.burnout.up&buff.leaping_flames.up&!buff.essence_burst.up&buff.dragonrage.up
# Hard cast only outside of SS and DR windows
actions.st+=/firestorm,if=!buff.dragonrage.up&debuff.shattering_star_debuff.down&talent.feed_the_flames&((!talent.dragonrage|cooldown.dragonrage.remains>=10)&(essence>=3|buff.essence_burst.up|talent.shattering_star&cooldown.shattering_star.remains<=6)|talent.dragonrage&cooldown.dragonrage.remains<=cast_time&cooldown.fire_breath.remains<6&cooldown.eternity_surge.remains<12)&!debuff.in_firestorm.up
actions.st+=/deep_breath,if=!buff.dragonrage.up&(talent.imminent_destruction&!debuff.shattering_star_debuff.up|talent.melt_armor&talent.maneuverability)
# Spend pyre if raging inferno debuff is active and you have 20 stacks of CB on 2T
actions.st+=/pyre,if=debuff.in_firestorm.up&talent.feed_the_flames&buff.charged_blast.stack==20&active_enemies>=2
# Mass Disintegrates
actions.st+=/disintegrate,target_if=min:buff.bombardments.remains,early_chain_if=ticks_remain<=1&buff.mass_disintegrate_stacks.up,if=(raid_event.movement.in>2|buff.hover.up)&buff.mass_disintegrate_stacks.up&talent.mass_disintegrate
# Dis logic Early Chain if needed for resources management. Clip after in DR after third tick for more important buttons.
actions.st+=/disintegrate,target_if=min:buff.bombardments.remains,chain=1,early_chain_if=evoker.use_early_chaining&ticks>=2&(raid_event.movement.in>2|buff.hover.up),interrupt_if=evoker.use_clipping&ticks>=2&(raid_event.movement.in>2|buff.hover.up),if=(raid_event.movement.in>2|buff.hover.up)&!variable.pool_for_id
# Spend firestorm procs ASAP
actions.st+=/firestorm,if=buff.snapfire.up|!debuff.in_firestorm.up&talent.feed_the_flames
# Use Deep Breath on 2T, unless adds will come before it'll be ready again or if talented ID.
actions.st+=/deep_breath,if=!buff.dragonrage.up&active_enemies>=2&((raid_event.adds.in>=120&!talent.onyx_legacy)|(raid_event.adds.in>=60&talent.onyx_legacy))
actions.st+=/deep_breath,if=!buff.dragonrage.up&(talent.imminent_destruction&!debuff.shattering_star_debuff.up|talent.melt_armor|talent.maneuverability)
# Get Ancient Flame as Filler
actions.st+=/call_action_list,name=green,if=talent.ancient_flame&!buff.ancient_flame.up&!buff.shattering_star_debuff.up&talent.scarlet_adaptation&!buff.dragonrage.up&!buff.burnout.up
# Cast LF outside of DR, In DR only cast with Iridescence.
actions.st+=/living_flame,if=!buff.dragonrage.up|(buff.iridescence_red.remains>execute_time|!talent.engulfing_blaze|buff.iridescence_blue.up|buff.burnout.up|buff.leaping_flames.up&cooldown.fire_breath.remains<=5)&active_enemies==1
# Fallback for movement
actions.st+=/azure_strike

# Use Dreambinder on CD
actions.trinkets=use_item,name=dreambinder_loom_of_the_great_cycle,use_off_gcd=1,if=gcd.remains>0.5
actions.trinkets+=/use_item,target_if=min:target.health.pct,name=iridal_the_earths_master,use_off_gcd=1,if=gcd.remains>0.5
actions.trinkets+=/use_item,name=spymasters_web,if=buff.spymasters_report.stack=1&buff.dragonrage.up&!buff.spymasters_web.up|buff.dragonrage.up&(fight_remains<130)|(fight_remains<=20|cooldown.engulf.up&talent.engulf&fight_remains<=40&cooldown.dragonrage.remains>=40)
# Nymues is used before Dragonrage unless you have PI and Event Horizon, then it is used on 2 Stacks of Emerald Trance. In AoE it is used before DR.
actions.trinkets+=/use_item,name=nymues_unraveling_spindle,if=(cooldown.dragonrage.remains<=3&(cooldown.fire_breath.remains<4|cooldown.eternity_surge.remains<4)&(cooldown.fire_breath.remains<8&cooldown.eternity_surge.remains<8)&target.time_to_die>=32)|cooldown.dragonrage.remains<=3&active_enemies>=3|fight_remains<=20
# Belorrelos is used on CD if not playing Nymues, it is used after Nymues if it is played. On AoE use after other use trinket.
actions.trinkets+=/use_item,name=belorrelos_the_suncaller,if=(((trinket.2.cooldown.remains|!variable.trinket_2_buffs)&(trinket.1.cooldown.remains|!variable.trinket_1_buffs)|active_enemies<=2)&(trinket.nymues_unraveling_spindle.cooldown.remains|!equipped.nymues_unraveling_spindle))|fight_remains<=20
# The trinket with the highest estimated value, will be used first and paired with Dragonrage. Trinkets are used on 4 stacks of Emerald Trance, unless playing double buff trinket, then one is used after SS/FB and the next on CD. Or with DR in AoE
actions.trinkets+=/use_item,slot=trinket1,if=buff.dragonrage.up&((variable.trinket_2_buffs&!cooldown.fire_breath.up&!cooldown.shattering_star.up&!equipped.nymues_unraveling_spindle&trinket.2.cooldown.remains)|(!cooldown.fire_breath.up&!cooldown.shattering_star.up)|active_enemies>=3)&(!trinket.2.has_cooldown|trinket.2.cooldown.remains|variable.trinket_priority=1|variable.trinket_2_exclude)&!variable.trinket_1_manual|trinket.1.proc.any_dps.duration>=fight_remains|trinket.1.cooldown.duration<=60&(variable.next_dragonrage>20|!talent.dragonrage)&(!buff.dragonrage.up|variable.trinket_priority=1)&!variable.trinket_1_manual
actions.trinkets+=/use_item,slot=trinket2,if=buff.dragonrage.up&((variable.trinket_1_buffs&!cooldown.fire_breath.up&!cooldown.shattering_star.up&!equipped.nymues_unraveling_spindle&trinket.1.cooldown.remains)|(!cooldown.fire_breath.up&!cooldown.shattering_star.up)|active_enemies>=3)&(!trinket.1.has_cooldown|trinket.1.cooldown.remains|variable.trinket_priority=2|variable.trinket_1_exclude)&!variable.trinket_2_manual|trinket.2.proc.any_dps.duration>=fight_remains|trinket.2.cooldown.duration<=60&(variable.next_dragonrage>20|!talent.dragonrage)&(!buff.dragonrage.up|variable.trinket_priority=2)&!variable.trinket_2_manual
# If only one on use trinket provides a buff, use the other on cooldown. Or if neither trinket provides a buff, use both on cooldown.
actions.trinkets+=/use_item,slot=trinket1,if=!variable.trinket_1_buffs&(trinket.2.cooldown.remains|!variable.trinket_2_buffs)&(variable.next_dragonrage>20|!talent.dragonrage)&!variable.trinket_1_manual
actions.trinkets+=/use_item,slot=trinket2,if=!variable.trinket_2_buffs&(trinket.1.cooldown.remains|!variable.trinket_1_buffs)&(variable.next_dragonrage>20|!talent.dragonrage)&!variable.trinket_2_manual

head=horns_of_the_destroyer,id=212029,bonus_id=6652/10876/10376/10354/10273/1498/10255,ilevel=639
neck=amulet_of_earthen_craftsmanship,id=215136,bonus_id=10421/9633/8902/10394/10879/9627/10222/8795/11144,ilevel=636,gem_id=213743/213467,crafted_stats=32/36
shoulders=fumaroles_of_the_destroyer,id=212027,bonus_id=6652/10376/10354/10273/1498/10255,ilevel=639
back=wings_of_shattered_sorrow,id=225574,bonus_id=6652/10376/10354/10269/1511/10255,ilevel=639,enchant_id=7403
chest=scales_of_the_destroyer,id=212032,bonus_id=6652/10376/10354/10273/1498/10255,ilevel=639,enchant_id=7364
wrists=glyphetched_vambraces,id=219342,bonus_id=10421/9633/8902/9627/11144/8790/10222,ilevel=636,enchant_id=7385,crafted_stats=36/49
hands=rippers_of_the_destroyer,id=212030,bonus_id=6652/10376/10354/10273/1498/10255,ilevel=639
waist=glyphetched_binding,id=219339,bonus_id=10421/9633/8902/9627/11144/11303/8960/8790/10222,ilevel=639,crafted_stats=32/49
legs=glyphetched_cuisses,id=219340,bonus_id=10421/9633/8902/9627/11144/11303/8960/8791/10222,ilevel=639,enchant_id=7534,crafted_stats=40/32
feet=glyphetched_stompers,id=219335,bonus_id=10421/9633/8902/9627/11144/8795/10222,ilevel=636,enchant_id=7424,crafted_stats=49/36
finger1=seal_of_the_poisoned_pact,id=225578,bonus_id=6652/10354/10268/1514/10255/10394/10879,ilevel=639,gem_id=213479/213491,enchant_id=7470
finger2=ring_of_earthen_craftsmanship,id=215135,bonus_id=10421/9633/8902/10395/10879/9627/10222/8790/11144,ilevel=636,gem_id=213455/213503,enchant_id=7470,crafted_stats=49/40
trinket1=arakara_sacbrood,id=219314,bonus_id=10273/10390/6652/10383/1632/10255,ilevel=639
trinket2=spymasters_web,id=220202,bonus_id=6652/10354/10269/1511/10255,ilevel=639
main_hand=blue_winglords_staff,id=194522,bonus_id=10289/9988,ilevel=639,enchant_id=7463

# Gear Summary
# gear_ilvl=638.20
# gear_stamina=236458
# gear_intellect=45439
# gear_crit_rating=19697
# gear_haste_rating=8529
# gear_mastery_rating=5149
# gear_versatility_rating=5907
# gear_avoidance_rating=1635
# gear_armor=37943
# set_bonus=thewarwithin_season_1_2pc=1
# set_bonus=thewarwithin_season_1_4pc=1
