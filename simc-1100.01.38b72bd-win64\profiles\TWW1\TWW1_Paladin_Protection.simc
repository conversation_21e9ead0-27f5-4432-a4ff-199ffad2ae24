paladin="TWW1_Paladin_Protection"
source=default
spec=protection
level=80
race=draenei
role=tank
position=front
talents=CIEAAAAAAAAAAAAAAAAAAAAAAsMmZMjHYegFmZmZssxMjZwwMAAADAAAAAAAphZmhBmBzMLGmZGAwYgZAYwMADAwMTbzysNDQgNzA

# Default consumables
potion=tempered_potion_3
flask=flask_of_alchemical_chaos_3
food=feast_of_the_divine_day
augmentation=crystallized
temporary_enchant=main_hand:algari_mana_oil_3,if=!(talent.rite_of_adjuration.enabled|talent.rite_of_sanctification.enabled)

# This default action priority list is automatically created based on your character.
# It is a attempt to provide you with a action list that is both simple and practicable,
# while resulting in a meaningful and good simulation. It may not result in the absolutely highest possible dps.
# Feel free to edit, adapt and improve it to your own needs.
# SimulationCraft is always looking for updates and improvements to the default action lists.

# Executed before combat begins. Accepts non-harmful actions only.
actions.precombat=flask
actions.precombat+=/food
actions.precombat+=/augmentation
actions.precombat+=/rite_of_sanctification
actions.precombat+=/rite_of_adjuration
actions.precombat+=/snapshot_stats
actions.precombat+=/devotion_aura
actions.precombat+=/lights_judgment
actions.precombat+=/arcane_torrent
actions.precombat+=/consecration
actions.precombat+=/variable,name=trinket_sync_slot,value=1,if=trinket.1.has_cooldown&trinket.1.has_stat.any_dps&(!trinket.2.has_stat.any_dps|trinket.1.cooldown.duration>=trinket.2.cooldown.duration)|!trinket.2.has_cooldown
actions.precombat+=/variable,name=trinket_sync_slot,value=2,if=trinket.2.has_cooldown&trinket.2.has_stat.any_dps&(!trinket.1.has_stat.any_dps|trinket.2.cooldown.duration>trinket.1.cooldown.duration)|!trinket.1.has_cooldown

# Executed every time the actor is available.
actions=auto_attack
actions+=/call_action_list,name=cooldowns
actions+=/call_action_list,name=trinkets
actions+=/call_action_list,name=standard

actions.cooldowns=lights_judgment,if=spell_targets.lights_judgment>=2|!raid_event.adds.exists|raid_event.adds.in>75|raid_event.adds.up
actions.cooldowns+=/avenging_wrath
actions.cooldowns+=/potion,if=buff.avenging_wrath.up
actions.cooldowns+=/moment_of_glory,if=(buff.avenging_wrath.remains<15|(time>10))
actions.cooldowns+=/divine_toll,if=spell_targets.shield_of_the_righteous>=3
actions.cooldowns+=/bastion_of_light,if=buff.avenging_wrath.up|cooldown.avenging_wrath.remains<=30
actions.cooldowns+=/invoke_external_buff,name=power_infusion,if=buff.avenging_wrath.up
actions.cooldowns+=/fireblood,if=buff.avenging_wrath.remains>8

actions.hammer_of_light=hammer_of_light,if=buff.blessing_of_dawn.stack>0|spell_targets.shield_of_the_righteous>=5
actions.hammer_of_light+=/eye_of_tyr,if=hpg_to_2dawn=5
actions.hammer_of_light+=/shield_of_the_righteous,if=hpg_to_2dawn=4
actions.hammer_of_light+=/eye_of_tyr,if=hpg_to_2dawn=1|buff.blessing_of_dawn.stack>0
actions.hammer_of_light+=/hammer_of_wrath
actions.hammer_of_light+=/judgment
actions.hammer_of_light+=/blessed_hammer
actions.hammer_of_light+=/hammer_of_the_righteous
actions.hammer_of_light+=/crusader_strike
actions.hammer_of_light+=/divine_toll

actions.standard=call_action_list,name=hammer_of_light,if=talent.lights_guidance.enabled&(cooldown.eye_of_tyr.remains<2|buff.hammer_of_light_ready.up)&(!talent.redoubt.enabled|buff.redoubt.stack>=2|!talent.bastion_of_light.enabled)&talent.of_dusk_and_dawn.enabled
actions.standard+=/hammer_of_light,if=(!talent.redoubt.enabled|buff.redoubt.stack=3)&(buff.blessing_of_dawn.stack>=1|!talent.of_dusk_and_dawn.enabled)
actions.standard+=/shield_of_the_righteous,if=(((!talent.righteous_protector.enabled|cooldown.righteous_protector_icd.remains=0)&holy_power>2)|buff.bastion_of_light.up|buff.divine_purpose.up)&!((buff.hammer_of_light_ready.up|buff.hammer_of_light_free.up))
actions.standard+=/holy_armaments,if=next_armament=sacred_weapon&(!buff.sacred_weapon.up|(buff.sacred_weapon.remains<6&!buff.avenging_wrath.up&cooldown.avenging_wrath.remains<=30))
actions.standard+=/judgment,target_if=min:debuff.judgment.remains,if=spell_targets.shield_of_the_righteous>3&buff.bulwark_of_righteous_fury.stack>=3&holy_power<3
# The following line has been edited for your own sanity, it is technically a dps gain to only use AS to refresh barricade of faith and strength in adversary as templar in large aoe However, it is very cursed, and nobody should actually do this, but if you REALLY wanted to, uncomment this line and comment out the next avenger's shield line. actions.standard+=/avengers_shield,if=!buff.bulwark_of_righteous_fury.up&talent.bulwark_of_righteous_fury.enabled&spell_targets.shield_of_the_righteous>=3&!((talent.lights_guidance.enabled&spell_targets.shield_of_the_righteous>=10)|!buff.barricade_of_faith.up)
actions.standard+=/avengers_shield,if=!buff.bulwark_of_righteous_fury.up&talent.bulwark_of_righteous_fury.enabled&spell_targets.shield_of_the_righteous>=3
actions.standard+=/hammer_of_wrath
actions.standard+=/judgment,target_if=min:debuff.judgment.remains,if=charges>=2|full_recharge_time<=gcd.max
actions.standard+=/holy_armaments,if=next_armament=holy_bulwark&charges=2
actions.standard+=/divine_toll,if=(!raid_event.adds.exists|raid_event.adds.in>10)
actions.standard+=/judgment,target_if=min:debuff.judgment.remains
actions.standard+=/blessed_hammer,if=buff.blessed_assurance.up&spell_targets.shield_of_the_righteous<3
actions.standard+=/hammer_of_the_righteous,if=buff.blessed_assurance.up&spell_targets.shield_of_the_righteous<3
actions.standard+=/crusader_strike,if=buff.blessed_assurance.up&spell_targets.shield_of_the_righteous<2
# In single target, Templar should prioritize maintaining Shake the Heavens by casting spells listed in Higher Calling.
actions.standard+=/avengers_shield,if=!talent.lights_guidance.enabled
actions.standard+=/consecration,if=!consecration.up
actions.standard+=/eye_of_tyr,if=(talent.inmost_light.enabled&raid_event.adds.in>=45|spell_targets.shield_of_the_righteous>=3)&!talent.lights_deliverance.enabled
actions.standard+=/holy_armaments,if=next_armament=holy_bulwark
actions.standard+=/blessed_hammer
actions.standard+=/hammer_of_the_righteous
actions.standard+=/crusader_strike
actions.standard+=/word_of_glory,if=buff.shining_light_free.up&talent.lights_guidance.enabled&cooldown.hammerfall_icd.remains=0
actions.standard+=/avengers_shield
actions.standard+=/eye_of_tyr,if=!talent.lights_deliverance.enabled
actions.standard+=/word_of_glory,if=buff.shining_light_free.up
actions.standard+=/arcane_torrent,if=holy_power<5
actions.standard+=/consecration

actions.trinkets=use_items,slots=trinket1,if=(variable.trinket_sync_slot=1&(buff.avenging_wrath.up|fight_remains<=40)|(variable.trinket_sync_slot=2&(!trinket.2.cooldown.ready|!buff.avenging_wrath.up))|!variable.trinket_sync_slot)
actions.trinkets+=/use_items,slots=trinket2,if=(variable.trinket_sync_slot=2&(buff.avenging_wrath.up|fight_remains<=40)|(variable.trinket_sync_slot=1&(!trinket.1.cooldown.ready|!buff.avenging_wrath.up))|!variable.trinket_sync_slot)

head=visor_of_the_ascended_captain,id=212427,bonus_id=10878,ilevel=639,gem_id=213743
neck=sureki_zealots_insignia,id=225577,bonus_id=10879,ilevel=639,gem_id=213485/213485
shoulders=entombed_seraphs_plumes,id=211991,ilevel=639
back=anvilhide_cape,id=221088,ilevel=639,enchant_id=7409
chest=entombed_seraphs_breastplate,id=211996,ilevel=639,enchant_id=7364
wrists=everforged_vambraces,id=222435,bonus_id=10520/10878,ilevel=636,gem_id=213485,enchant_id=7391,crafted_stats=36/40
hands=entombed_seraphs_castigation,id=211994,ilevel=639
waist=greatbelt_of_the_hungerer,id=212442,bonus_id=10878,ilevel=639,gem_id=213485
legs=entombed_seraphs_greaves,id=211992,ilevel=639,enchant_id=7601
feet=everforged_sabatons,id=222429,bonus_id=10520,ilevel=636,enchant_id=7418,crafted_stats=36/40
finger1=key_to_the_unseeming,id=212447,bonus_id=10879,ilevel=639,gem_id=213470/213455,enchant_id=7340
finger2=seal_of_the_poisoned_pact,id=225578,bonus_id=10879,ilevel=639,gem_id=213494/213485,enchant_id=7340
trinket1=arakara_sacbrood,id=219314,ilevel=639
trinket2=spare_meat_hook,id=178751,ilevel=639
main_hand=engorged_worm_smasher,id=178730,ilevel=639,enchant_id=7460
off_hand=oldblood_hielaman,id=221177,ilevel=639

# Gear Summary
# gear_ilvl=638.63
# gear_strength=36154
# gear_stamina=237111
# gear_crit_rating=7503
# gear_haste_rating=21131
# gear_mastery_rating=4850
# gear_versatility_rating=5420
# gear_leech_rating=3060
# gear_speed_rating=250
# gear_armor=84953
# set_bonus=thewarwithin_season_1_2pc=1
# set_bonus=thewarwithin_season_1_4pc=1
