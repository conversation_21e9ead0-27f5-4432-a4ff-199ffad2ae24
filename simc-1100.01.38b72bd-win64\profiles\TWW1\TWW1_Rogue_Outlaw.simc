rogue="TWW1_Rogue_Outlaw_KIR"
source=default
spec=outlaw
level=80
race=dwarf
role=attack
position=back
talents=CQQAAAAAAAAAAAAAAAAAAAAAAAAMWmxMjZMmZmZYGGjZMMzMz0yMmtZAAAAAAw2yMzwDMzMWglZbAAAAYmBYwsNLmZmRzMbMLssNtwiZA

# Default consumables
potion=tempered_potion_3
flask=flask_of_tempered_versatility_3
food=feast_of_the_divine_day
augmentation=crystallized
temporary_enchant=main_hand:ironclaw_whetstone_3/off_hand:ironclaw_whetstone_3

# This default action priority list is automatically created based on your character.
# It is a attempt to provide you with a action list that is both simple and practicable,
# while resulting in a meaningful and good simulation. It may not result in the absolutely highest possible dps.
# Feel free to edit, adapt and improve it to your own needs.
# SimulationCraft is always looking for updates and improvements to the default action lists.

# Executed before combat begins. Accepts non-harmful actions only.
actions.precombat=apply_poison,nonlethal=none,lethal=instant
actions.precombat+=/flask
actions.precombat+=/augmentation
actions.precombat+=/food
# Snapshot raid buffed stats before combat begins and pre-potting is done.
actions.precombat+=/snapshot_stats
actions.precombat+=/use_item,name=imperfect_ascendancy_serum
actions.precombat+=/stealth,precombat_seconds=2
# Builds with Keep it Rolling prepull Adrenaline Rush before Roll the Bones to consume Loaded Dice immediately instead of on the next pandemic roll
actions.precombat+=/adrenaline_rush,precombat_seconds=2,if=talent.improved_adrenaline_rush&talent.keep_it_rolling&talent.loaded_dice
actions.precombat+=/roll_the_bones,precombat_seconds=2
actions.precombat+=/adrenaline_rush,precombat_seconds=1,if=talent.improved_adrenaline_rush
actions.precombat+=/slice_and_dice,precombat_seconds=1

# Executed every time the actor is available.
# Restealth if possible (no vulnerable enemies in combat)
actions=stealth
# Interrupt on cooldown to allow simming interactions with that
actions+=/kick
# Variables that define the reroll rules for Roll the Bones  Default rule: reroll if the only buff that will be rolled away is Buried Treasure, or Grand Melee in single target without upcoming adds
actions+=/variable,name=rtb_reroll,value=rtb_buffs.will_lose=(rtb_buffs.will_lose.buried_treasure+rtb_buffs.will_lose.grand_melee&spell_targets.blade_flurry<2&raid_event.adds.in>12&raid_event.adds.count<2)
# If Loaded Dice is talented, then keep any 1 buff from Roll the Bones but roll it into 2 buffs when Loaded Dice is active
actions+=/variable,name=rtb_reroll,if=talent.loaded_dice,value=rtb_buffs.will_lose=buff.loaded_dice.up
# If all active Roll the Bones buffs are ahead of its container buff and have under 40s remaining, then reroll again with Loaded Dice active in an attempt to get even more buffs
actions+=/variable,name=rtb_reroll,value=variable.rtb_reroll&rtb_buffs.longer=0|rtb_buffs.normal=0&rtb_buffs.longer>=1&rtb_buffs<6&rtb_buffs.max_remains<=39&!stealthed.all&buff.loaded_dice.up
# Avoid rerolls when we will not have time remaining on the fight or add wave to recoup the opportunity cost of the global
actions+=/variable,name=rtb_reroll,op=reset,if=!(raid_event.adds.remains>12|raid_event.adds.up&(raid_event.adds.in-raid_event.adds.remains)<6|target.time_to_die>12)|fight_remains<12
actions+=/variable,name=ambush_condition,value=(talent.hidden_opportunity|combo_points.deficit>=2+talent.improved_ambush+buff.broadside.up)&energy>=50
# Use finishers if at -1 from max combo points, or -2 in Stealth with Crackshot. With the hero trees, Hidden Opportunity builds also finish at -2 if Audacity or Opportunity is active
actions+=/variable,name=finish_condition,value=effective_combo_points>=cp_max_spend-1-(stealthed.all&talent.crackshot|(talent.hand_of_fate|talent.flawless_form)&talent.hidden_opportunity&(buff.audacity.up|buff.opportunity.up))
# With multiple targets, this variable is checked to decide whether some CDs should be synced with Blade Flurry
actions+=/variable,name=blade_flurry_sync,value=spell_targets.blade_flurry<2&raid_event.adds.in>20|buff.blade_flurry.remains>gcd
actions+=/call_action_list,name=cds
# High priority stealth list, will fall through if no conditions are met
actions+=/call_action_list,name=stealth,if=stealthed.all
actions+=/run_action_list,name=finish,if=variable.finish_condition
actions+=/call_action_list,name=build
actions+=/arcane_torrent,if=energy.base_deficit>=15+energy.regen
actions+=/arcane_pulse
actions+=/lights_judgment
actions+=/bag_of_tricks

# Builders
actions.build=echoing_reprimand
# High priority Ambush for Hidden Opportunity builds
actions.build+=/ambush,if=talent.hidden_opportunity&buff.audacity.up
# With Audacity + Hidden Opportunity + Fan the Hammer, consume Opportunity to proc Audacity any time Ambush is not available
actions.build+=/pistol_shot,if=talent.fan_the_hammer&talent.audacity&talent.hidden_opportunity&buff.opportunity.up&!buff.audacity.up
# With Fan the Hammer, consume Opportunity as a higher priority if at max stacks or if it will expire
actions.build+=/pistol_shot,if=talent.fan_the_hammer&buff.opportunity.up&(buff.opportunity.stack>=buff.opportunity.max_stack|buff.opportunity.remains<2)
# With Fan the Hammer, consume Opportunity if it will not overcap CPs, or with 1 CP at minimum
actions.build+=/pistol_shot,if=talent.fan_the_hammer&buff.opportunity.up&(combo_points.deficit>=(1+(talent.quick_draw+buff.broadside.up)*(talent.fan_the_hammer.rank+1))|combo_points<=talent.ruthlessness)
# If not using Fan the Hammer, then consume Opportunity based on energy, when it will exactly cap CPs, or when using Quick Draw
actions.build+=/pistol_shot,if=!talent.fan_the_hammer&buff.opportunity.up&(energy.base_deficit>energy.regen*1.5|combo_points.deficit<=1+buff.broadside.up|talent.quick_draw.enabled|talent.audacity.enabled&!buff.audacity.up)
# Fallback pooling just so Sinister Strike is never casted if Ambush is available for Hidden Opportunity builds
actions.build+=/pool_resource,for_next=1
actions.build+=/ambush,if=talent.hidden_opportunity
actions.build+=/sinister_strike

# Cooldowns  Use Adrenaline Rush if it is not active and the finisher condition is not met, but Crackshot builds can refresh it with 2cp or lower inside stealth
actions.cds=adrenaline_rush,if=!buff.adrenaline_rush.up&(!variable.finish_condition|!talent.improved_adrenaline_rush)|stealthed.all&talent.crackshot&talent.improved_adrenaline_rush&combo_points<=2
# Maintain Blade Flurry on 2+ targets
actions.cds+=/blade_flurry,if=spell_targets>=2&buff.blade_flurry.remains<gcd
# With Deft Maneuvers, use Blade Flurry on cooldown at 5+ targets, or at 3-4 targets if missing combo points equal to the amount given
actions.cds+=/blade_flurry,if=talent.deft_maneuvers&!variable.finish_condition&(spell_targets>=3&combo_points.deficit=spell_targets+buff.broadside.up|spell_targets>=5)
# Use Roll the Bones if reroll conditions are met, or with no buffs
actions.cds+=/roll_the_bones,if=variable.rtb_reroll|rtb_buffs=0
# Use Keep it Rolling with any 4 buffs. If Broadside is not active, then wait until just before the lowest buff expires in an attempt to obtain it from Count the Odds.
actions.cds+=/keep_it_rolling,if=rtb_buffs>=4&(rtb_buffs.min_remains<2|buff.broadside.up)
actions.cds+=/ghostly_strike,if=combo_points<cp_max_spend
# Trinkets that should not be used during stealth and have higher priority than entering stealth
actions.cds+=/use_item,name=manic_grieftorch,if=!stealthed.all&buff.between_the_eyes.up|fight_remains<=5
actions.cds+=/use_item,name=beacon_to_the_beyond,if=!stealthed.all&buff.between_the_eyes.up|fight_remains<=5
actions.cds+=/use_item,name=imperfect_ascendancy_serum,if=!stealthed.all|fight_remains<=22
# Killing Spree has higher priority than stealth cooldowns
actions.cds+=/killing_spree,if=variable.finish_condition&!stealthed.all
# Crackshot builds use stealth cooldowns if Between the Eyes is ready
actions.cds+=/call_action_list,name=stealth_cds,if=!stealthed.all&(!talent.crackshot|cooldown.between_the_eyes.ready)
actions.cds+=/thistle_tea,if=!buff.thistle_tea.up&(energy.base_deficit>=150|fight_remains<charges*6)
# Use Blade Rush at minimal energy outside of stealth
actions.cds+=/blade_rush,if=energy.base_time_to_max>4&!stealthed.all
actions.cds+=/potion,if=buff.bloodlust.react|fight_remains<30|buff.adrenaline_rush.up
actions.cds+=/blood_fury
actions.cds+=/berserking
actions.cds+=/fireblood
actions.cds+=/ancestral_call
# Default conditions for usable items.
actions.cds+=/use_item,name=elementium_pocket_anvil,use_off_gcd=1,if=gcd.remains<=action.sinister_strike.gcd%2
# Use Bomb Dispenser on cooldown, but hold if 2nd trinket is nearly off cooldown, unless at max charges or sim duration ends soon
actions.cds+=/use_item,name=dragonfire_bomb_dispenser,use_off_gcd=1,if=gcd.remains<=action.sinister_strike.gcd%2&((!trinket.1.is.dragonfire_bomb_dispenser&trinket.1.cooldown.remains>10|trinket.2.cooldown.remains>10)|cooldown.dragonfire_bomb_dispenser.charges>2|fight_remains<20|!trinket.2.has_cooldown|!trinket.1.has_cooldown)
actions.cds+=/use_item,name=stormeaters_boon,if=spell_targets.blade_flurry>desired_targets|raid_event.adds.in>60|raid_event.adds.count<2|fight_remains<10
actions.cds+=/use_item,name=enduring_dreadplate,if=spell_targets.blade_flurry>desired_targets|raid_event.adds.in>60|raid_event.adds.count<2|fight_remains<16
actions.cds+=/use_item,name=windscar_whetstone,if=spell_targets.blade_flurry>desired_targets|raid_event.adds.in>60|raid_event.adds.count<2|fight_remains<7
actions.cds+=/use_items,slots=trinket1,if=buff.between_the_eyes.up|trinket.1.has_stat.any_dps|fight_remains<=20
actions.cds+=/use_items,slots=trinket2,if=buff.between_the_eyes.up|trinket.2.has_stat.any_dps|fight_remains<=20

# Finishers  Use Between the Eyes to keep the crit buff up, but on cooldown if Improved/Greenskins, and avoid overriding Greenskins
actions.finish=between_the_eyes,if=!talent.crackshot&(buff.between_the_eyes.remains<4|talent.improved_between_the_eyes|talent.greenskins_wickers)&!buff.greenskins_wickers.up
# Crackshot builds use Between the Eyes outside of Stealth if we are unlikely to enter a Stealth window before the next BtE cast or if we are unlikely to lose Adrenaline Rush uptime by hitting BtE before the next cast of Vanish
actions.finish+=/between_the_eyes,if=talent.crackshot&(cooldown.vanish.remains>45|talent.underhanded_upper_hand&talent.without_a_trace&(buff.adrenaline_rush.remains>10|buff.adrenaline_rush.down&cooldown.adrenaline_rush.remains>45))&(raid_event.adds.remains>8|raid_event.adds.in<raid_event.adds.remains|!raid_event.adds.up)
actions.finish+=/slice_and_dice,if=buff.slice_and_dice.remains<fight_remains&refreshable
actions.finish+=/cold_blood
actions.finish+=/coup_de_grace
actions.finish+=/dispatch

# Stealth
actions.stealth=cold_blood,if=variable.finish_condition
# Ensure Crackshot BtE is not skipped because of low energy
actions.stealth+=/pool_resource,for_next=1
# High priority Between the Eyes for Crackshot, except not directly out of Shadowmeld
actions.stealth+=/between_the_eyes,if=variable.finish_condition&talent.crackshot&(!buff.shadowmeld.up|stealthed.rogue)
actions.stealth+=/dispatch,if=variable.finish_condition
# 2 Fan the Hammer Crackshot builds can consume Opportunity in stealth with max stacks, Broadside, and low CPs, or with Greenskins active
actions.stealth+=/pistol_shot,if=talent.crackshot&talent.fan_the_hammer.rank>=2&buff.opportunity.stack>=6&(buff.broadside.up&combo_points<=1|buff.greenskins_wickers.up)
actions.stealth+=/ambush,if=talent.hidden_opportunity

# Stealth Cooldowns  Builds with Underhanded Upper Hand and Subterfuge (and Without a Trace for Crackshot) must use Vanish while Adrenaline Rush is active
actions.stealth_cds=vanish,if=talent.underhanded_upper_hand&talent.subterfuge&(buff.adrenaline_rush.up|!talent.without_a_trace&talent.crackshot)&(variable.finish_condition|!talent.crackshot&(variable.ambush_condition|!talent.hidden_opportunity))
# Builds without Underhanded Upper Hand but with Crackshot must still use Vanish into Between the Eyes on cooldown
actions.stealth_cds+=/vanish,if=!talent.underhanded_upper_hand&talent.crackshot&variable.finish_condition
# Builds without Underhanded Upper Hand and Crackshot but still Hidden Opportunity use Vanish into Ambush when Audacity is not active and under max Opportunity stacks
actions.stealth_cds+=/vanish,if=!talent.underhanded_upper_hand&!talent.crackshot&talent.hidden_opportunity&!buff.audacity.up&buff.opportunity.stack<buff.opportunity.max_stack&variable.ambush_condition
# Builds without Underhanded Upper Hand, Crackshot, and Hidden Opportunity but with Fatebound use Vanish at five stacks of either Fatebound coin in order to proc the Lucky Coin if it's not already active, and otherwise continue to Vanish into a Dispatch to proc Double Jeopardy on a biased coin
actions.stealth_cds+=/vanish,if=!talent.underhanded_upper_hand&!talent.crackshot&!talent.hidden_opportunity&talent.fateful_ending&(!buff.fatebound_lucky_coin.up&(buff.fatebound_coin_tails.stack>=5|buff.fatebound_coin_heads.stack>=5)|buff.fatebound_lucky_coin.up&!cooldown.between_the_eyes.ready)
# Builds with none of the above can use Vanish to maintain Take 'em By Surprise
actions.stealth_cds+=/vanish,if=!talent.underhanded_upper_hand&!talent.crackshot&!talent.hidden_opportunity&!talent.fateful_ending&talent.take_em_by_surprise&!buff.take_em_by_surprise.up
actions.stealth_cds+=/shadowmeld,if=variable.finish_condition&!cooldown.vanish.ready

head=kareshi_phantoms_emptiness,id=212038,bonus_id=1540/10299
neck=elders_hemolymphal_periapt,id=221181,bonus_id=3131/10299,gem_id=213743/213467
shoulders=kareshi_phantoms_shoulderpads,id=212036,bonus_id=1540/10299
back=wings_of_shattered_sorrow,id=225574,bonus_id=1540/10299,enchant=chant_of_leeching_fangs_3
chest=kareshi_phantoms_nexus_wraps,id=212041,bonus_id=1540/10299,enchant=crystalline_radiance_3
wrists=runebranded_armbands,id=219334,bonus_id=10421/9633/8902/11144/10222/1485/10520/8960/8792,enchant=chant_of_armored_leech_3,crafted_stats=32/40
hands=kareshi_phantoms_grips,id=212039,bonus_id=1540/10299
waist=sapgorger_belt,id=178699,bonus_id=9948/10299
legs=runebranded_legwraps,id=219332,bonus_id=10421/9633/8902/1515/10520/8960/8792,enchant=stormbound_armor_kit_3,crafted_stats=36/40
feet=viscerastitched_footpads,id=178731,bonus_id=9948/10299,enchant=defenders_march_3
finger1=stitchfleshs_misplaced_signet,id=178736,bonus_id=9948/10299,gem_id=213467/213467,enchant=cursed_versatility_3
finger2=experiment_08752s_band,id=221189,bonus_id=3131/10299,gem_id=213467/213467,enchant=cursed_versatility_3
trinket1=void_reapers_contract,id=212456,bonus_id=1540/10299
trinket2=arakara_sacbrood,id=219314,bonus_id=3131/10299
main_hand=void_reapers_warp_blade,id=219877,bonus_id=1540/10299,enchant=authority_of_the_depths_3
off_hand=bloodkissed_kukri,id=212395,bonus_id=1540/10299,enchant=authority_of_the_depths_3

# Gear Summary
# gear_ilvl=638.63
# gear_agility=36129
# gear_stamina=237765
# gear_attack_power=938
# gear_crit_rating=7412
# gear_haste_rating=12653
# gear_mastery_rating=880
# gear_versatility_rating=16819
# gear_leech_rating=3060
# gear_armor=26282
# set_bonus=kyevezzas_cruel_implements_2pc=1
# set_bonus=thewarwithin_season_1_2pc=1
# set_bonus=thewarwithin_season_1_4pc=1
