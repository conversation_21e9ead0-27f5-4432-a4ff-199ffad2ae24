shaman="TWW1_Shaman_Elemental"
source=default
spec=elemental
level=80
race=tauren
role=spell
position=ranged_back
talents=CYQAAAAAAAAAAAAAAAAAAAAAAAAAAAAMbzy2MjZGmZjxMDmZGAAAAAsYmNYADYWYCZWAAjpZgZbhZmYMzMbDLzYGMLDzyMzMzMGmhB

# Default consumables
potion=tempered_potion_3
flask=flask_of_tempered_swiftness_3
food=feast_of_the_divine_day
augmentation=crystallized
temporary_enchant=main_hand:algari_mana_oil_3,if=!talent.improved_flametongue_weapon

# This default action priority list is automatically created based on your character.
# It is a attempt to provide you with a action list that is both simple and practicable,
# while resulting in a meaningful and good simulation. It may not result in the absolutely highest possible dps.
# Feel free to edit, adapt and improve it to your own needs.
# SimulationCraft is always looking for updates and improvements to the default action lists.

# Executed before combat begins. Accepts non-harmful actions only.
actions.precombat=flask
actions.precombat+=/food
actions.precombat+=/augmentation
# Snapshot raid buffed stats before combat begins and pre-potting is done.
actions.precombat+=/snapshot_stats
# Ensure weapon enchant is applied if you've selected Improved Flametongue Weapon.
actions.precombat+=/flametongue_weapon,if=talent.improved_flametongue_weapon.enabled
actions.precombat+=/potion
actions.precombat+=/stormkeeper
actions.precombat+=/lightning_shield
actions.precombat+=/thunderstrike_ward
actions.precombat+=/variable,name=mael_cap,value=100+50*talent.swelling_maelstrom.enabled+25*talent.primordial_capacity.enabled,op=set
actions.precombat+=/variable,name=spymaster_in_1st,value=trinket.1.is.spymasters_web
actions.precombat+=/variable,name=spymaster_in_2nd,value=trinket.2.is.spymasters_web

# Executed every time the actor is available.
# Enable more movement.
actions=spiritwalkers_grace,moving=1,if=movement.distance>6
# Interrupt of casts.
actions+=/wind_shear
actions+=/blood_fury,if=!talent.ascendance.enabled|buff.ascendance.up|cooldown.ascendance.remains>50
actions+=/berserking,if=!talent.ascendance.enabled|buff.ascendance.up
actions+=/fireblood,if=!talent.ascendance.enabled|buff.ascendance.up|cooldown.ascendance.remains>50
actions+=/ancestral_call,if=!talent.ascendance.enabled|buff.ascendance.up|cooldown.ascendance.remains>50
actions+=/use_item,slot=trinket1,if=!variable.spymaster_in_1st|target.time_to_die<45&cooldown.stormkeeper.remains<5|fight_remains<22
actions+=/use_item,slot=trinket2,if=!variable.spymaster_in_2nd|target.time_to_die<45&cooldown.stormkeeper.remains<5|fight_remains<22
actions+=/use_item,slot=main_hand
actions+=/lightning_shield,if=buff.lightning_shield.down
actions+=/natures_swiftness
# Use Power Infusion on Cooldown.
actions+=/invoke_external_buff,name=power_infusion
actions+=/potion
actions+=/run_action_list,name=aoe,if=spell_targets.chain_lightning>2
actions+=/run_action_list,name=single_target

actions.aoe=fire_elemental,if=!buff.fire_elemental.up
actions.aoe+=/storm_elemental,if=!buff.storm_elemental.up
actions.aoe+=/stormkeeper,if=!buff.stormkeeper.up
# {Fire} Reset LMT CD as early as possible.
actions.aoe+=/totemic_recall,if=cooldown.liquid_magma_totem.remains>15&talent.fire_elemental.enabled
actions.aoe+=/liquid_magma_totem
# Spread Flame Shock via Primordial Wave using Surge of Power if possible.
actions.aoe+=/primordial_wave,target_if=min:dot.flame_shock.remains,if=buff.surge_of_power.up|!talent.surge_of_power.enabled|maelstrom<60-5*talent.eye_of_the_storm.enabled
actions.aoe+=/ancestral_swiftness
# {Lightning} Spread Flame Shock using Surge of Power if LMT is not picked.
actions.aoe+=/flame_shock,target_if=refreshable,if=buff.surge_of_power.up&talent.lightning_rod.enabled&dot.flame_shock.remains<target.time_to_die-16&active_dot.flame_shock<(spell_targets.chain_lightning>?6)&!talent.liquid_magma_totem.enabled
# {Lightning} Cast extra Flame Shock to help getting to next spender for SK+SoP on 6+ targets. Mostly opener.
actions.aoe+=/flame_shock,target_if=min:dot.flame_shock.remains,if=buff.primordial_wave.up&buff.stormkeeper.up&maelstrom<60-5*talent.eye_of_the_storm.enabled-(8+2*talent.flow_of_power.enabled)*active_dot.flame_shock&spell_targets.chain_lightning>=6&active_dot.flame_shock<6
# {Fire} Spread and refresh Flame Shock using Surge of Power (if talented) up to 6.
actions.aoe+=/flame_shock,target_if=refreshable,if=talent.fire_elemental.enabled&(buff.surge_of_power.up|!talent.surge_of_power.enabled)&dot.flame_shock.remains<target.time_to_die-5&(active_dot.flame_shock<6|dot.flame_shock.remains>0)
actions.aoe+=/tempest,target_if=min:debuff.lightning_rod.remains,if=!buff.arc_discharge.up
# JUST DO IT! https://i.kym-cdn.com/entries/icons/mobile/000/018/147/Shia_LaBeouf__Just_Do_It__Motivational_Speech_(Original_Video_by_LaBeouf__R%C3%B6nkk%C3%B6___Turner)_0-4_screenshot.jpg
actions.aoe+=/ascendance
# Against 6 targets or more Surge of Power should be used with Lava Beam rather than Lava Burst.
actions.aoe+=/lava_beam,if=active_enemies>=6&buff.surge_of_power.up&buff.ascendance.remains>cast_time
# Against 6 targets or more Surge of Power should be used with Chain Lightning rather than Lava Burst.
actions.aoe+=/chain_lightning,if=active_enemies>=6&buff.surge_of_power.up
# Consume Primordial Wave buff immediately if you have Stormkeeper buff, fighting 6+ enemies and need maelstrom to spend.
actions.aoe+=/lava_burst,target_if=dot.flame_shock.remains>2,if=buff.primordial_wave.up&buff.stormkeeper.up&maelstrom<60-5*talent.eye_of_the_storm.enabled&spell_targets.chain_lightning>=6&talent.surge_of_power.enabled
# Cast Lava burst to consume Primordial Wave proc. Wait for Lava Surge proc if possible.
actions.aoe+=/lava_burst,target_if=dot.flame_shock.remains>2,if=buff.primordial_wave.up&(buff.primordial_wave.remains<4|buff.lava_surge.up)
# {Fire} Use Lava Surge proc to buff <anything> with Master of the Elements.
actions.aoe+=/lava_burst,target_if=dot.flame_shock.remains,if=cooldown_react&buff.lava_surge.up&!buff.master_of_the_elements.up&talent.master_of_the_elements.enabled&talent.fire_elemental.enabled
# Activate Surge of Power if next global is Primordial wave. Respect Echoes of Great Sundering.
actions.aoe+=/earthquake,if=cooldown.primordial_wave.remains<gcd&talent.surge_of_power.enabled&(buff.echoes_of_great_sundering_es.up|buff.echoes_of_great_sundering_eb.up|!talent.echoes_of_great_sundering.enabled)
# Spend if all Lightning Rods ran out or you are close to overcaping. Respect Echoes of Great Sundering.
actions.aoe+=/earthquake,target_if=max:debuff.lightning_rod.remains,if=(debuff.lightning_rod.remains=0&talent.lightning_rod.enabled|maelstrom>variable.mael_cap-30)&(buff.echoes_of_great_sundering_es.up|buff.echoes_of_great_sundering_eb.up|!talent.echoes_of_great_sundering.enabled)
# Spend to buff your follow-up Stormkeeper with Surge of Power on 6+ targets. Respect Echoes of Great Sundering.
actions.aoe+=/earthquake,if=buff.stormkeeper.up&spell_targets.chain_lightning>=6&talent.surge_of_power.enabled&(buff.echoes_of_great_sundering_es.up|buff.echoes_of_great_sundering_eb.up|!talent.echoes_of_great_sundering.enabled)
# {Fire} Spend if you have Master of the elements buff or fighting 5+ enemies. Bank maelstrom during the end of Ascendance. Respect Echoes of Great Sundering.
actions.aoe+=/earthquake,if=(buff.master_of_the_elements.up|spell_targets.chain_lightning>=5)&(buff.fusion_of_elements_nature.up|buff.ascendance.remains>9|!buff.ascendance.up)&(buff.echoes_of_great_sundering_es.up|buff.echoes_of_great_sundering_eb.up|!talent.echoes_of_great_sundering.enabled)&talent.fire_elemental.enabled
# Use the talents you selected. Spread Lightning Rod to as many targets as possible.
actions.aoe+=/elemental_blast,target_if=min:debuff.lightning_rod.remains,if=talent.echoes_of_great_sundering.enabled&!buff.echoes_of_great_sundering_eb.up&(!buff.maelstrom_surge.up&set_bonus.tww1_4pc|maelstrom>variable.mael_cap-30)
# Use the talents you selected. Spread Lightning Rod to as many targets as possible.
actions.aoe+=/earth_shock,target_if=min:debuff.lightning_rod.remains,if=talent.echoes_of_great_sundering.enabled&!buff.echoes_of_great_sundering_es.up&(!buff.maelstrom_surge.up&set_bonus.tww1_4pc|maelstrom>variable.mael_cap-30)
# Use Icefury for Fusion f Elements proc.
actions.aoe+=/icefury,if=talent.fusion_of_elements.enabled&!(buff.fusion_of_elements_nature.up|buff.fusion_of_elements_fire.up)
# {Fire} Proc Master of the Elements outside Ascendance.
actions.aoe+=/lava_burst,target_if=dot.flame_shock.remains>2,if=talent.master_of_the_elements.enabled&!buff.master_of_the_elements.up&!buff.ascendance.up&talent.fire_elemental.enabled
# Stormkeeper is strong and should be used.
actions.aoe+=/lava_beam,if=buff.stormkeeper.up&(buff.surge_of_power.up|spell_targets.lava_beam<6)
# Stormkeeper is strong and should be used.
actions.aoe+=/chain_lightning,if=buff.stormkeeper.up&(buff.surge_of_power.up|spell_targets.chain_lightning<6)
# Power of the Maelstrom is strong and should be used.
actions.aoe+=/lava_beam,if=buff.power_of_the_maelstrom.up&buff.ascendance.remains>cast_time&!buff.stormkeeper.up
# Power of the Maelstrom is strong and should be used.
actions.aoe+=/chain_lightning,if=buff.power_of_the_maelstrom.up&!buff.stormkeeper.up
# Consume Master of the Elements with Lava Beam on 4+ targets. Just spam it over hardcasted Lava Burst on 5+ targets.
actions.aoe+=/lava_beam,if=(buff.master_of_the_elements.up&spell_targets.lava_beam>=4|spell_targets.lava_beam>=5)&buff.ascendance.remains>cast_time&!buff.stormkeeper.up
# Gamble away for Deeply Rooted Elements procs.
actions.aoe+=/lava_burst,target_if=dot.flame_shock.remains>2,if=talent.deeply_rooted_elements.enabled
actions.aoe+=/lava_beam,if=buff.ascendance.remains>cast_time
actions.aoe+=/chain_lightning
actions.aoe+=/flame_shock,moving=1,target_if=refreshable
actions.aoe+=/frost_shock,moving=1

actions.single_target=fire_elemental,if=!buff.fire_elemental.up
actions.single_target+=/storm_elemental,if=!buff.storm_elemental.up
# Just use Stormkeeper.
actions.single_target+=/stormkeeper,if=!buff.ascendance.up&!buff.stormkeeper.up
# {Fire} Reset LMT CD as early as possible.
actions.single_target+=/totemic_recall,if=cooldown.liquid_magma_totem.remains>15&spell_targets.chain_lightning>1&talent.fire_elemental.enabled
# Use LMT outside Ascendance in fire builds and on 2 targets for lightning.
actions.single_target+=/liquid_magma_totem,if=!buff.ascendance.up&(talent.fire_elemental.enabled|spell_targets.chain_lightning>1)
# Use Primordial Wave as much as possible.
actions.single_target+=/primordial_wave,target_if=min:dot.flame_shock.remains
actions.single_target+=/ancestral_swiftness
# {Fire} Manually refresh Flame shock if better options are not available.
actions.single_target+=/flame_shock,target_if=min:dot.flame_shock.remains,if=active_enemies=1&(dot.flame_shock.remains<2|active_dot.flame_shock=0)&(dot.flame_shock.remains<cooldown.primordial_wave.remains|!talent.primordial_wave.enabled)&(dot.flame_shock.remains<cooldown.liquid_magma_totem.remains|!talent.liquid_magma_totem.enabled)&!buff.surge_of_power.up&talent.fire_elemental.enabled
# Use Flame shock without Surge of Power if you can't spread it with SoP because it is going to be used on Stormkeeper or Surge of Power is not talented.
actions.single_target+=/flame_shock,target_if=min:dot.flame_shock.remains,if=active_dot.flame_shock<active_enemies&spell_targets.chain_lightning>1&(talent.deeply_rooted_elements.enabled|talent.ascendance.enabled|talent.primordial_wave.enabled|talent.searing_flames.enabled|talent.magma_chamber.enabled)&(!buff.surge_of_power.up&buff.stormkeeper.up|!talent.surge_of_power.enabled|cooldown.ascendance.remains=0)
# Spread Flame Shock to multiple targets only if talents were selected that benefit from it.
actions.single_target+=/flame_shock,target_if=min:dot.flame_shock.remains,if=spell_targets.chain_lightning>1&(talent.deeply_rooted_elements.enabled|talent.ascendance.enabled|talent.primordial_wave.enabled|talent.searing_flames.enabled|talent.magma_chamber.enabled)&(buff.surge_of_power.up&!buff.stormkeeper.up|!talent.surge_of_power.enabled)&dot.flame_shock.remains<6,cycle_targets=1
actions.single_target+=/tempest
# Stormkeeper is strong and should be used.
actions.single_target+=/lightning_bolt,if=buff.stormkeeper.up&buff.surge_of_power.up
# Buff stormkeeper with MotE when not using Surge of Power.
actions.single_target+=/lava_burst,target_if=dot.flame_shock.remains>2,if=buff.stormkeeper.up&!buff.master_of_the_elements.up&!talent.surge_of_power.enabled&talent.master_of_the_elements.enabled
# Buff Stormkeeper with at least something if you can.
actions.single_target+=/lightning_bolt,if=buff.stormkeeper.up&!talent.surge_of_power.enabled&(buff.master_of_the_elements.up|!talent.master_of_the_elements.enabled)
# Surge of Power is strong and should be used.
actions.single_target+=/lightning_bolt,if=buff.surge_of_power.up&!buff.ascendance.up&talent.echo_chamber.enabled
actions.single_target+=/ascendance,if=cooldown.lava_burst.charges_fractional<1.0
# {Fire} Lava Surge is neat. Utilize it.
actions.single_target+=/lava_burst,if=cooldown_react&buff.lava_surge.up&talent.fire_elemental.enabled
# Consume Primordial wave buff.
actions.single_target+=/lava_burst,target_if=dot.flame_shock.remains>2,if=buff.primordial_wave.up
# {Fire} Spend if you have MotE buff and: not in Ascendance OR Ascendance gona last so long you will need to spend anyway OR nature fusion buff up OR close to maelstrom cap. Respect Echoes of Great Sundering.
actions.single_target+=/earthquake,if=buff.master_of_the_elements.up&(buff.echoes_of_great_sundering_es.up|buff.echoes_of_great_sundering_eb.up|spell_targets.chain_lightning>1&!talent.echoes_of_great_sundering.enabled&!talent.elemental_blast.enabled)&(buff.fusion_of_elements_nature.up|maelstrom>variable.mael_cap-15|buff.ascendance.remains>9|!buff.ascendance.up)&talent.fire_elemental.enabled
# {Fire} Spend if you have MotE buff and: not in Ascendance OR Ascendance gona last so long you will need to spend anyway OR any fusion buff up OR close to maelstrom cap.
actions.single_target+=/elemental_blast,if=buff.master_of_the_elements.up&(buff.fusion_of_elements_nature.up|buff.fusion_of_elements_fire.up|maelstrom>variable.mael_cap-15|buff.ascendance.remains>6|!buff.ascendance.up)&talent.fire_elemental.enabled
# {Fire} Spend if you have MotE buff and: not in Ascendance OR Ascendance gona last so long you will need to spend anyway OR nature fusion buff up OR close to maelstrom cap.
actions.single_target+=/earth_shock,if=buff.master_of_the_elements.up&(buff.fusion_of_elements_nature.up|maelstrom>variable.mael_cap-15|buff.ascendance.remains>9|!buff.ascendance.up)&talent.fire_elemental.enabled
# {Lightning} Spend if you have Master of the Elements buff and Stormkeeper is not coming up soon OR Stormkeeper is active OR you are close to maelstrom cap. Respect Echoes of Great Sundering.
actions.single_target+=/earthquake,if=(buff.echoes_of_great_sundering_es.up|buff.echoes_of_great_sundering_eb.up|spell_targets.chain_lightning>1&!talent.echoes_of_great_sundering.enabled&!talent.elemental_blast.enabled)&(buff.master_of_the_elements.up&cooldown.stormkeeper.remains>10|maelstrom>variable.mael_cap-15|buff.stormkeeper.up)&talent.storm_elemental.enabled
# {Lightning} Spend if you have Master of the Elements buff and Stormkeeper is not coming up soon OR Stormkeeper is active OR you are close to maelstrom cap. Spread Lightning Rod to as many targets as possible.
actions.single_target+=/elemental_blast,target_if=min:debuff.lightning_rod.remains,if=(buff.master_of_the_elements.up&cooldown.stormkeeper.remains>10|maelstrom>variable.mael_cap-15|buff.stormkeeper.up)&talent.storm_elemental.enabled
# {Lightning} Spend if you have Master of the Elements buff and Stormkeeper is not coming up soon OR Stormkeeper is active OR you are close to maelstrom cap. Spread Lightning Rod to as many targets as possible.
actions.single_target+=/earth_shock,target_if=min:debuff.lightning_rod.remains,if=(buff.master_of_the_elements.up&cooldown.stormkeeper.remains>10|maelstrom>variable.mael_cap-15|buff.stormkeeper.up)&talent.storm_elemental.enabled
# Don't waste Icefury stacks even during Ascendance.
actions.single_target+=/icefury,if=!(buff.fusion_of_elements_nature.up|buff.fusion_of_elements_fire.up)&buff.icefury.stack=2&(talent.fusion_of_elements.enabled|!buff.ascendance.up)
# Spam Lava burst in Ascendance.
actions.single_target+=/lava_burst,target_if=dot.flame_shock.remains>2,if=buff.ascendance.up
# {Fire} Buff your next <anything> with MotE.
actions.single_target+=/lava_burst,target_if=dot.flame_shock.remains>2,if=talent.master_of_the_elements.enabled&!buff.master_of_the_elements.up&talent.fire_elemental.enabled
# Spend all Lava Burst charges in opener to get one Stormkeeper buffed with Surge of Power. Lava Surge can be used as emergency generator in combat to help with buffing Stormkeeper.
actions.single_target+=/lava_burst,target_if=dot.flame_shock.remains>2,if=buff.stormkeeper.up&(buff.lava_surge.up|time<10)
# Spend if close to overcaping or all Lightning Rods ran out. Respect Echoes of Great Sundering.
actions.single_target+=/earthquake,target_if=max:debuff.lightning_rod.remains,if=(buff.echoes_of_great_sundering_es.up|buff.echoes_of_great_sundering_eb.up|spell_targets.chain_lightning>1&!talent.echoes_of_great_sundering.enabled&!talent.elemental_blast.enabled)&(maelstrom>variable.mael_cap-15|debuff.lightning_rod.remains<gcd|fight_remains<5)
# Spend if close to overcaping or all Lightning Rods ran out.
actions.single_target+=/elemental_blast,target_if=max:debuff.lightning_rod.remains,if=maelstrom>variable.mael_cap-15|debuff.lightning_rod.remains<gcd|fight_remains<5
# Spend if close to overcaping or all Lightning Rods ran out.
actions.single_target+=/earth_shock,target_if=max:debuff.lightning_rod.remains,if=maelstrom>variable.mael_cap-15|debuff.lightning_rod.remains<gcd|fight_remains<5
actions.single_target+=/lightning_bolt,if=buff.surge_of_power.up
# Use Icefury if you won't overwrite Fusion of Elements buffs.
actions.single_target+=/icefury,if=!(buff.fusion_of_elements_nature.up|buff.fusion_of_elements_fire.up)
# Use Icefury-buffed Frost Shock against 1 target or if you need to generate for SoP buff on Stormkeeper.
actions.single_target+=/frost_shock,if=buff.icefury_dmg.up&(spell_targets.chain_lightning=1|buff.stormkeeper.up)&talent.surge_of_power.enabled
# Utilize the Power of the Maelstrom buff.
actions.single_target+=/chain_lightning,if=buff.power_of_the_maelstrom.up&spell_targets.chain_lightning>1&!buff.stormkeeper.up
# Utilize the Power of the Maelstrom buff.
actions.single_target+=/lightning_bolt,if=buff.power_of_the_maelstrom.up&!buff.stormkeeper.up
# Fish for DRE procs.
actions.single_target+=/lava_burst,target_if=dot.flame_shock.remains>2,if=talent.deeply_rooted_elements.enabled
# Casting Chain Lightning at two targets is more efficient than Lightning Bolt.
actions.single_target+=/chain_lightning,if=spell_targets.chain_lightning>1
# Filler spell. Always available. Always the bottom line.
actions.single_target+=/lightning_bolt
actions.single_target+=/flame_shock,moving=1,target_if=refreshable
actions.single_target+=/flame_shock,moving=1,if=movement.distance>6
# Frost Shock is our movement filler.
actions.single_target+=/frost_shock,moving=1

head=final_meals_horns,id=212428,ilevel=639,gem_id=213479
neck=amulet_of_earthen_craftsmanship,id=215136,bonus_id=10421/9633/8902/11144/10222/1485/10520/8960/8781,gem_id=213743/213455,crafted_stats=36/40
shoulders=concourse_of_the_forgotten_reservoir,id=212009,ilevel=639
back=wings_of_shattered_sorrow,id=225574,bonus_id=1540/10299,enchant_id=7403
chest=vestments_of_the_forgotten_reservoir,id=212014,ilevel=639,enchant_id=7364
wrists=kyvezas_covert_clasps,id=225581,ilevel=639,gem_id=213479,enchant_id=7385
hands=covenant_of_the_forgotten_reservoir,id=212012,ilevel=639
waist=lost_watchers_remains,id=212414,ilevel=639,gem_id=213479
legs=sarong_of_the_forgotten_reservoir,id=212010,ilevel=639,enchant_id=7534
feet=red_scale_boots,id=133293,ilevel=639,enchant_id=7424
finger1=stitchfleshs_misplaced_signet,id=178736,ilevel=639,gem_id=213479/213479,enchant_id=7340
finger2=band_of_the_ancient_dredger,id=159461,bonus_id=10047/10299/8781,gem_id=213455/213455,enchant_id=7340
trinket1=spymasters_web,id=220202,ilevel=639
trinket2=arakara_sacbrood,id=219314,ilevel=639
main_hand=everforged_dagger,id=222439,bonus_id=10421/9633/8902/11144/10222/1485/11301/8960/8790,enchant_id=7460,crafted_stats=36/40
off_hand=crest_of_the_caustic_despot,id=225579,ilevel=639

# Gear Summary
# gear_ilvl=638.63
# gear_strength=1897
# gear_stamina=237584
# gear_intellect=45272
# gear_crit_rating=9572
# gear_haste_rating=20138
# gear_mastery_rating=2841
# gear_versatility_rating=7694
# gear_avoidance_rating=1635
# gear_armor=66844
# set_bonus=thewarwithin_season_1_2pc=1
# set_bonus=thewarwithin_season_1_4pc=1
