# hunter="PR_Hunter_Beast_Mastery"
# level=70
# race=orc
# role=attack
# position=ranged_back
# spec=beast_mastery
# talents=2102012

# head=boneshatter_helm,id=172325,bonus_id=6649/6650/8122/1588/6935,gem_id=173128
# neck=worldkiller_iris,id=189859,bonus_id=1524/7187/6935,gem_id=173128
# shoulders=godstalkers_pauldrons,id=188856,bonus_id=1505/7187
# back=shroud_of_the_sires_chosen,id=189847,bonus_id=1524/7187
# chest=godstalkers_hauberk,id=188858,bonus_id=1505/7187,enchant=eternal_skirmish
# wrists=interdimensional_manica,id=189849,bonus_id=1524/7187/6935,gem_id=173128
# hands=godstalkers_gauntlets,id=188861,bonus_id=1505/7187
# waist=godstalkers_fauld,id=188857,bonus_id=1498/7187/6935,gem_id=173128
# legs=godstalkers_tassets,id=188860,bonus_id=1505/7187
# feet=boneshatter_treads,id=172323,bonus_id=7010/6649/6650/1588,enchant=eternal_agility
# finger1=rygelons_heraldric_ring,id=189854,bonus_id=1524/7187/6935,gem_id=173128,enchant=tenet_of_haste
# finger2=loquacious_keepers_peridot,id=189802,bonus_id=1524/7187/6935,gem_id=173128,enchant=tenet_of_haste
# trinket1=the_first_sigil,id=188271,bonus_id=1524/7187
# trinket2=cache_of_acquired_treasures,id=188265,bonus_id=1524/7187
# main_hand=astral_verdict,id=189853,bonus_id=1524/7187,enchant=optical_target_embiggener

# save=PR_Hunter_Beast_Mastery.simc


# hunter="PR_Hunter_Marksmanship"
# level=70
# race=troll
# spec=marksmanship
# role=attack
# position=ranged_back
# talents=1102032

# head=boneshatter_helm,id=172325,bonus_id=6647/6648/8122/1588/6935,gem_id=173130
# neck=worldkiller_iris,id=189859,bonus_id=1524/7187/6935,gem_id=173130
# shoulders=godstalkers_pauldrons,id=188856,bonus_id=1505/7187
# back=shroud_of_the_sires_chosen,id=189847,bonus_id=1524/7187
# chest=godstalkers_hauberk,id=188858,bonus_id=1505/7187,enchant=eternal_skirmish
# wrists=boneshatter_armguards,id=172329,bonus_id=6647/6648/7014/1588/6935,gem_id=173130
# hands=godstalkers_gauntlets,id=188861,bonus_id=1505/7187
# waist=kings_wolfheart_waistband,id=189837,bonus_id=1524/7187/6935,gem_id=173130
# legs=godstalkers_tassets,id=188860,bonus_id=1505/7187
# feet=godstalkers_sabatons,id=188862,bonus_id=1498/7187,enchant=eternal_agility
# finger1=rygelons_heraldric_ring,id=189854,bonus_id=1524/7187/6935,gem_id=173130,enchant=tenet_of_mastery
# finger2=modified_defense_grid,id=189772,bonus_id=1524/7187/6935,gem_id=173130,enchant=tenet_of_mastery
# trinket1=the_first_sigil,id=188271,bonus_id=1524/7187
# trinket2=cache_of_acquired_treasures,id=188265,bonus_id=1524/7187
# main_hand=astral_verdict,id=189853,bonus_id=1524/7187,enchant=sinful_revelation

# save=PR_Hunter_Marksmanship.simc


# hunter="PR_Hunter_Survival"
# level=70
# race=troll
# spec=survival
# role=attack
# position=back
# talents=3101012

# head=godstalkers_sallet,id=188859,bonus_id=1498/7187/6935,gem_id=173128
# neck=cabochon_of_the_infinite_flight,id=185820,bonus_id=1595/6536/6646/6935,gem_id=173128
# shoulders=godstalkers_pauldrons,id=188856,bonus_id=1505/7187
# back=shroud_of_the_sires_chosen,id=189847,bonus_id=1524/7187
# chest=godstalkers_hauberk,id=188858,bonus_id=1505/7187,enchant=eternal_stats
# wrists=interdimensional_manica,id=189849,bonus_id=1524/7187/6935,gem_id=173128
# hands=godstalkers_gauntlets,id=188861,bonus_id=1505/7187
# waist=boneshatter_waistguard,id=172328,bonus_id=6649/6650/7015/1588/6935,gem_id=173128
# legs=epochal_oppressors_greaves,id=189857,bonus_id=1524/7187
# feet=boneshatter_treads,id=172323,bonus_id=6649/6650/8122/1588,enchant=eternal_agility
# finger1=loquacious_keepers_peridot,id=189802,bonus_id=1524/7187/6935,gem_id=173128,enchant=tenet_of_haste
# finger2=bloodoath_signet,id=178871,bonus_id=1592/6536/6646/6935,gem_id=173128,enchant=tenet_of_haste
# trinket1=shadowgrasp_totem,id=179356,bonus_id=1592/6536/6646
# trinket2=the_first_sigil,id=188271,bonus_id=1524/7187
# main_hand=zovastrum_the_unmaking,id=189861,bonus_id=1524/7187,enchant=sinful_revelation

# save=PR_Hunter_Survival.simc
