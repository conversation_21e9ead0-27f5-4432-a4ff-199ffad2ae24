# TWW1
# TWW Season 1
# * Set pieces are permitted up to the item level cap (639). ["bonus_id=?/?"]
# * Raid gear is permitted up to the Mythic item level cap (639). ["bonus_id=?/?"]
# * Mythic+ gear is permitted up to the Mythic+ item level cap (639).
# * Delve gear is permitted up to the Vault cap (626)
# * Honor PvP gear is permitted up to the item level cap (?). ["bonus_id=?"]
# * Conquest PvP gear is permitted up to the item level cap (Hero Track, 626). ["bonus_id=?"]
# * Crafted gear is permitted up to the item level cap (636), with a maximum of two embellished pieces, and without profession requirements. ["bonus_id=?/?"]
#   * NOTE: You may use however many crafted pieces you wish, just as long as its possible to wear it all in-game.
# * World Bosses gear is permitted.
# * World Drop gear is permitted.
# * World Quests gear is permitted as long as item level is only scaled from Renown / Average Character Item Level.
# * Use sockets on the slots head, neck (x2), wrist, waist, finger1 (x2), finger2 (x2).
# * Must use gem ids (ex: ",gem_id=173127") instead of hardcoded stats.
#    * 192985: Primary Stat & Haste
#    * 192964: Mastery > Vers
# * No Tertiary Stats.
# * No Hardcoded Stats (generally if an item gives wrong stats: either bonus_id is wrong, or the item no longer exists in-game).
# * Must use enchant name (ex: ",enchant=devotion_of_haste_3") instead of ids/hardcoded stats if possible. Rank 3 enchants are permitted.
# * Use ,enchant=incandescent_essence on head.

# Death Knight
TWW1_Generate_Death_Knight.simc

# Demon Hunter
TWW1_Generate_Demon_Hunter.simc

# Druid
# TWW1_Generate_Druid.simc

# Evoker
TWW1_Generate_Evoker.simc

# Hunter
# TWW1_Generate_Hunter.simc

# Mage
# TWW1_Generate_Mage.simc

# Monk
# TWW1_Generate_Monk.simc

# Paladin
TWW1_Generate_Paladin.simc

# Priest
TWW1_Generate_Priest.simc

# Rogue
TWW1_Generate_Rogue.simc

# Shaman
TWW1_Generate_Shaman.simc

# Warlock
# TWW1_Generate_Warlock.simc

# Warrior
# TWW1_Generate_Warrior.simc
