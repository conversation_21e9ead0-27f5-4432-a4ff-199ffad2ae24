
 * Beta! Beta! Beta! Beta! Beta! Beta! 
 * Not All classes are yet supported. 
 * Some class models still need tweaking. 
 * Some class action lists need tweaking. 
 * Some class BiS gear setups need tweaking. 
 * Some trinkets not yet implemented. 
 * Constructive feedback regarding our output will shorten the Beta phase dramatically. 
 * Beta! Beta! Beta! Beta! Beta! Beta! 

DPS Ranking:
 672348 100.0%  Raid
 672348  100.0%  煤气罐超人

HPS Ranking:
      0 100.0%%  Raid

Player: 煤气罐超人 gnome mage arcane 80
  DPS=672348.********** DPS-Error=1325.4401556232858/0.20% DPS-Range=87297.17053514614/12.98%
  DPR=3.4680994724384977 RPS-Out=179104.60899529204 RPS-In=176468.59506464313 Resource=mana Waiting=0.007274822794162918 ApM=48.1121627662194
  Talents: C4DArhxZfsv/vllYUrQS3iw2nPzgZzCmZMzsAjZmxYaMmZAAAAAAMQAzMTLz2yMAQsBAAAAAwGALzYYmZGDDzMzYMzMzMzMMG
  Covenant: invalid
  Core Stats:    strength=7762|7762(7762)  agility=12001|12001(12001)  stamina=193558|193558(193558)  intellect=50805|48556(46244)  spirit=0|0(0)  health=3871160|3871160  mana=3760680|3262650
  Generic Stats: mastery=30.24%|24.29%(8570)  versatility=6.64%|6.64%(5180)  leech=0.00%|0.00%(0)  runspeed=7.00%|7.00%(0)
  Spell Stats:   power=50805|48556(0)  hit=15.00%|15.00%(0)  crit=16.10%|16.51%(6657)  haste=8.45%|8.17%(3301)  speed=8.45%|8.17%  manareg=65120|62146(0)
  Attack Stats:  power=0|0(0)  hit=7.50%|7.50%(0)  crit=16.10%|16.51%(6657)  expertise=7.50%|7.50%(0)  haste=8.45%|8.17%(3301)  speed=8.45%|8.17%
  Defense Stats: armor=11883|11883(11883) miss=3.00%|3.00%  dodge=3.00%|3.00%(0)  parry=0.00%|0.00%(0)  block=0.00%|0.00%(0) crit=0.00%|0.00%  versatility=3.32%|3.32%(5180)
  Priorities (actions.precombat):
    flask/food/augmentation/arcane_intellect/variable,name=aoe_target_count,op=reset,default=2
    variable,name=aoe_target_count,op=set,value=9,if=!talent.arcing_cleave/variable,name=opener,op=set,value=1
    variable,name=sunfury_aoe_list,default=0,op=reset
    variable,name=steroid_trinket_equipped,op=set,value=equipped.gladiators_badge|equipped.signet_of_the_priory|equipped.high_speakers_accretion|equipped.spymasters_web|equipped.treacherous_transmitter|equipped.imperfect_ascendancy_serum
    snapshot_stats/mirror_image/arcane_blast,if=!talent.evocation/evocation,if=talent.evocation
  Priorities (actions.default):
    counterspell/potion,if=buff.siphon_storm.up|(!talent.evocation&cooldown.arcane_surge.ready)
    lights_judgment,if=(buff.arcane_surge.down&debuff.touch_of_the_magi.down&active_enemies>=2)
    berserking,if=(prev_gcd.1.arcane_surge&variable.opener)|((prev_gcd.1.arcane_surge&(fight_remains<80|target.health.pct<35|!talent.arcane_bombardment))|(prev_gcd.1.arcane_surge&!equipped.spymasters_web))
    blood_fury,if=(prev_gcd.1.arcane_surge&variable.opener)|((prev_gcd.1.arcane_surge&(fight_remains<80|target.health.pct<35|!talent.arcane_bombardment))|(prev_gcd.1.arcane_surge&!equipped.spymasters_web))
    fireblood,if=(prev_gcd.1.arcane_surge&variable.opener)|((prev_gcd.1.arcane_surge&(fight_remains<80|target.health.pct<35|!talent.arcane_bombardment))|(prev_gcd.1.arcane_surge&!equipped.spymasters_web))
    ancestral_call,if=(prev_gcd.1.arcane_surge&variable.opener)|((prev_gcd.1.arcane_surge&(fight_remains<80|target.health.pct<35|!talent.arcane_bombardment))|(prev_gcd.1.arcane_surge&!equipped.spymasters_web))
    invoke_external_buff,name=power_infusion,if=prev_gcd.1.arcane_surge/invoke_external_buff,name=blessing_of_summer,if=prev_gcd.1.arcane_surge
    invoke_external_buff,name=blessing_of_autumn,if=cooldown.touch_of_the_magi.remains>5
    use_items,if=prev_gcd.1.arcane_surge|prev_gcd.1.evocation|fight_remains<20|!variable.steroid_trinket_equipped
    use_item,name=spymasters_web,if=(prev_gcd.1.arcane_surge|prev_gcd.1.evocation)&(fight_remains<80|target.health.pct<35|!talent.arcane_bombardment)|fight_remains<20
    use_item,name=high_speakers_accretion,if=(prev_gcd.1.arcane_surge|prev_gcd.1.evocation)|cooldown.evocation.remains<4|fight_remains<20
    use_item,name=imperfect_ascendancy_serum,if=cooldown.evocation.ready|cooldown.arcane_surge.ready|fight_remains<20
    use_item,name=treacherous_transmitter,if=buff.arcane_surge.remains>13|prev_gcd.1.evocation|cooldown.arcane_surge.remains<10|fight_remains<20
    do_treacherous_transmitter_task,use_off_gcd=1,if=buff.arcane_surge.remains>13|fight_remains<20
    use_item,name=aberrant_spellforge,if=!variable.steroid_trinket_equipped|buff.siphon_storm.down|(equipped.spymasters_web&target.health.pct>35)
    use_item,name=mad_queens_mandate,if=!variable.steroid_trinket_equipped|buff.siphon_storm.down
    use_item,name=fearbreakers_echo,if=!variable.steroid_trinket_equipped|buff.siphon_storm.down
    use_item,name=mereldars_toll,if=!variable.steroid_trinket_equipped|buff.siphon_storm.down
    variable,name=opener,op=set,if=debuff.touch_of_the_magi.up&variable.opener,value=0/arcane_barrage,if=fight_remains<2
    call_action_list,name=cd_opener/call_action_list,name=sunfury_aoe,if=talent.spellfire_spheres&variable.sunfury_aoe_list
    call_action_list,name=spellslinger_aoe,if=active_enemies>=(variable.aoe_target_count+talent.impetus)&!talent.spellfire_spheres
    call_action_list,name=sunfury,if=talent.spellfire_spheres/call_action_list,name=spellslinger,if=!talent.spellfire_spheres/arcane_barrage
  Priorities (actions.cd_opener):
    touch_of_the_magi,use_off_gcd=1,if=prev_gcd.1.arcane_barrage&(action.arcane_barrage.in_flight_remains<=0.5|gcd.remains<=0.5)&(buff.arcane_surge.up|cooldown.arcane_surge.remains>30)|(prev_gcd.1.arcane_surge&buff.arcane_charge.stack<4)
    wait,sec=0.05,if=prev_gcd.1.arcane_surge&time-action.touch_of_the_magi.last_used<0.015,line_cd=15/arcane_blast,if=buff.presence_of_mind.up
    arcane_orb,if=talent.high_voltage&variable.opener,line_cd=10
    evocation,if=cooldown.arcane_surge.remains<(gcd.max*3)&cooldown.touch_of_the_magi.remains<(gcd.max*6)
    arcane_missiles,if=variable.opener,interrupt_if=tick_time>gcd.remains,interrupt_immediate=1,interrupt_global=1,chain=1,line_cd=10
    arcane_surge,if=cooldown.touch_of_the_magi.remains<gcd.max*3
  Priorities (actions.sunfury):
    shifting_power,if=((buff.arcane_surge.down&buff.siphon_storm.down&debuff.touch_of_the_magi.down&cooldown.evocation.remains>15&cooldown.touch_of_the_magi.remains>10)&fight_remains>10)&buff.arcane_soul.down
    cancel_buff,name=presence_of_mind,use_off_gcd=1,if=(debuff.magis_spark_arcane_blast.up&time-action.arcane_blast.last_used>0.015)|((prev_gcd.1.arcane_blast&buff.presence_of_mind.stack=1)|active_enemies<4)
    presence_of_mind,if=debuff.touch_of_the_magi.remains<=gcd.max&buff.nether_precision.up&active_enemies<4
    wait,sec=0.05,if=buff.presence_of_mind.up&prev_gcd.1.arcane_blast,line_cd=15
    arcane_barrage,if=((buff.arcane_charge.stack=4&(time-action.arcane_blast.last_used<0.015&buff.nether_precision.stack=1)&active_enemies>=(5-(2*(talent.arcane_bombardment&target.health.pct<35)))&talent.arcing_cleave&((talent.high_voltage&buff.clearcasting.react)|(cooldown.arcane_orb.remains<gcd.max|action.arcane_orb.charges>0))))|(buff.aether_attunement.up&talent.high_voltage&buff.clearcasting.react&buff.arcane_charge.stack>1&active_enemies>1)
    arcane_orb,if=buff.arcane_charge.stack<2&buff.arcane_soul.down&(!talent.high_voltage|buff.clearcasting.react=0)
    arcane_barrage,if=((buff.glorious_incandescence.up|buff.intuition.react)&((time-action.arcane_blast.last_used<0.015&buff.nether_precision.stack=1)|(buff.nether_precision.down&buff.clearcasting.react=0)))|(buff.arcane_soul.up&((buff.clearcasting.react<3)|buff.arcane_soul.remains<gcd.max))|(buff.arcane_charge.stack=4&cooldown.touch_of_the_magi.ready)
    arcane_missiles,if=buff.clearcasting.react&((buff.nether_precision.down|(buff.clearcasting.react=3)|(talent.high_voltage&buff.arcane_charge.stack<3)|(buff.nether_precision.stack=1&time-action.arcane_blast.last_used<0.015))),interrupt_if=tick_time>gcd.remains&buff.aether_attunement.down,interrupt_immediate=1,interrupt_global=1,chain=1
    presence_of_mind,if=(buff.arcane_charge.stack=3|buff.arcane_charge.stack=2)&active_enemies>=3
    arcane_explosion,if=(talent.reverberate|buff.arcane_charge.stack<1)&active_enemies>=4/arcane_blast/arcane_barrage
  Actions:
    arcane_assault                   Count= 128.5|  2.327sec  DPE=  15112| 0.96%  DPET=     0  DPR=  0  pDPS=  6456  Miss= 0.00%  Hit= 12060|  8756| 20626  Crit= 25438| 18388| 43285|22.99%
    arcane_barrage                   Count=  47.1|  6.449sec  DPE= 643643|13.95%  DPET=551469  DPR=  0  pDPS= 93843  Miss= 0.00%  Hit=478875| 26662|2655539  Crit=1007970|115259|5496973|23.48%
    arcane_blast                     Count= 117.3|  2.501sec  DPE= 448427|26.01%  DPET=374889  DPR=  1  pDPS=174862  Miss= 0.00%  Hit=357533| 47129|960976  Crit=756589|100333|1891684|22.76%
    arcane_echo                      Count= 211.9|  1.329sec  DPE=  14343| 1.50%  DPET=     0  DPR=  0  pDPS= 10108  Miss= 0.00%  Hit= 11373|  6981| 17321  Crit= 24030| 14740| 36373|23.44%
    arcane_missiles                  Count=  52.9|  5.550sec  DPE= 840318|22.00%  DPET=612360  DPR=  0  pDPS=147841  TickCount=   352  MissTick= 0.00%  Tick=100548| 45291|290057  CritTick=211430| 95112|592205|23.41%  UpTime= 22.13%
    arcane_orb                       Count=   3.2| 58.693sec  DPE=  99884| 0.00%  DPET= 84134  DPR=  4  pDPS=     0
    arcane_orb_bolt                  Count=   3.2| 81.175sec  DPE=  99884| 0.16%  DPET=     0  DPR=  0  pDPS=  1072  Miss= 0.00%  Hit= 80132| 66638|119045  Crit=168373|139939|247568|22.36%
    arcane_surge                     Count=   4.2| 80.445sec  DPE= 758875| 1.59%  DPET=374227  DPR=  0  pDPS= 10679  Miss= 0.00%  Hit=602049|527998|725936  Crit=1260321|1147802|1516928|23.82%
    dematerialize                    Count=  98.1|  2.981sec  DPE=  47351| 2.30%  DPET=     0  DPR=  0  pDPS= 15440  TickCount=   255  MissTick= 0.00%  Tick= 18239|  1825|105174  UpTime= 84.75%
    evocation                        Count=   4.3| 80.404sec  DPE=      0| 0.00%  DPET=     0  DPR=  0  pDPS=     0  TickCount=    23  UpTime=  2.55%
    magis_spark                      Count=   7.6| 40.406sec  DPE= 446375| 1.68%  DPET=     0  DPR=  0  pDPS= 11290  Miss= 0.00%  Hit=353934|238169|543493  Crit=750512|504596|1086219|23.30%
    magis_spark_echo                 Count=  23.4| 12.336sec  DPE= 453469| 5.24%  DPET=     0  DPR=  0  pDPS= 35248  Miss= 0.00%  Hit=453517| 46063|4663431
    meteorite                        Count= 106.2|  2.659sec  DPE= 108776| 0.00%  DPET=     0  DPR=  0  pDPS=     0
    meteorite_impact                 Count= 104.9|  2.656sec  DPE= 110129| 5.71%  DPET=     0  DPR=  0  pDPS= 38385  Miss= 0.00%  Hit= 88459| 68968|141899  Crit=186170|144832|297988|22.17%
    mirror_image                     Count=   1.0|  0.000sec  DPE=  96983| 0.00%  DPET=     0  DPR=  0  pDPS=     0
    orb_barrage_arcane_orb           Count=  18.6| 15.760sec  DPE= 114006| 0.00%  DPET=     0  DPR=  0  pDPS=     0
    orb_barrage_arcane_orb_bolt      Count=  18.5| 15.791sec  DPE= 114445| 1.05%  DPET=     0  DPR=  0  pDPS=  7049  Miss= 0.00%  Hit= 90984| 65083|154613  Crit=191363|135321|325135|23.32%
    potion                           Count=   1.3|  0.000sec  DPE=      0| 0.00%  DPET=     0  DPR=  0  pDPS=     0
    shadowed_essence                 Count=  39.4|  7.454sec  DPE= 102148| 0.00%  DPET=     0  DPR=  0  pDPS=     0
    shadowed_essence_damage          Count=  39.3|  7.454sec  DPE= 102383| 1.99%  DPET=     0  DPR=  0  pDPS= 13394  Miss= 0.00%  Hit= 86144| 84485| 92624  Crit=172319|168970|185247|19.11%
    shifting_power                   Count=   3.7| 80.730sec  DPE= 229141| 0.42%  DPET= 69961  DPR=  2  pDPS=  2850  TickCount=    15  MissTick= 0.00%  Tick= 45999| 36682| 72727  CritTick= 96857| 77031|152727|22.21%  UpTime=  3.76%
    touch_of_the_magi                Count=   7.9| 39.711sec  DPE=1984324| 0.00%  DPET=     0  DPR= 16  pDPS=     0
    touch_of_the_magi_explosion      Count=   7.9| 39.506sec  DPE=1984324| 7.77%  DPET=     0  DPR=  0  pDPS= 52222  Miss= 0.00%  Hit=2066664|796047|4538130
   mirror_image (DPS=2424.5688785941693)
    frostbolt                        Count=  74.0|  1.581sec  DPE=   1311| 0.05%  DPET=   824  DPR=  0  pDPS=  2425  Miss= 0.00%  Hit=  1075|   965|  1228  Crit=  2157|  1930|  2455|21.78%
   arcane_phoenix (DPS=250168.5766174207)
    arcane_barrage                   Count=  14.5| 19.651sec  DPE=  85287| 0.61%  DPET=     0  DPR=  0  pDPS= 20105  Miss= 0.00%  Hit= 85462| 80015| 94655
    arcane_surge                     Count=   6.2| 40.141sec  DPE= 660604| 2.03%  DPET=     0  DPR=  0  pDPS= 66472  Miss= 0.00%  Hit=660527|581974|766179
    flamestrike                      Count=  14.3| 19.308sec  DPE= 148465| 1.05%  DPET=     0  DPR=  0  pDPS= 34457  Miss= 0.00%  Hit=148448|118605|244760
    greater_pyroblast                Count=   6.1| 43.518sec  DPE= 510614| 1.55%  DPET=     0  DPR=  0  pDPS= 50668  Miss= 0.00%  Hit=512667|480088|567928
    meteorite                        Count=  14.4| 19.409sec  DPE=      0| 0.00%  DPET=     0  DPR=  0  pDPS=     0
    meteorite_exceptional            Count=  24.9|  9.449sec  DPE=      0| 0.00%  DPET=     0  DPR=  0  pDPS=     0
    meteorite_exceptional_impact     Count=  23.2|  9.648sec  DPE= 122896| 1.41%  DPET=     0  DPR=  0  pDPS= 46104  Miss= 0.00%  Hit=122896|115098|136157
    meteorite_impact                 Count=  13.4| 20.673sec  DPE= 148609| 0.99%  DPET=     0  DPR=  0  pDPS= 32364  Miss= 0.00%  Hit=121539| 79538|142932  Crit=254711|167827|301451|20.33%

  Constant Buffs:
    arcane_familiar/arcane_intellect/crystallization/everything_stew/flask_of_alchemical_chaos
  Dynamic Buffs:
    aether_attunement                 : start= 17.3 refresh=  0.0 interval= 17.0 trigger= 17.0 duration=  5.8 uptime= 33.44%
    aether_attunement_counter         : start= 18.0 refresh= 34.9 interval= 16.9 trigger=  5.7 duration= 12.5 uptime= 74.86%
    arcane_charge                     : start= 47.2 refresh=259.8 interval=  6.5 trigger=  1.0 duration=  6.3 uptime= 98.52%
    arcane_harmony                    : start= 34.4 refresh=317.2 interval=  8.8 trigger=  0.8 duration=  5.7 uptime= 65.55%
    arcane_soul                       : start=  4.9 refresh=  0.1 interval= 71.7 trigger= 70.5 duration=  4.2 uptime=  5.57%
    arcane_surge                      : start=  6.9 refresh=  0.0 interval= 45.8 trigger= 45.8 duration= 10.4 uptime= 23.85%
    arcane_tempo                      : start=  5.1 refresh= 42.0 interval= 54.1 trigger=  6.4 duration= 56.3 uptime= 95.88%
    big_brained                       : start= 54.7 refresh=  0.0 interval= 15.3 trigger=  5.6 duration= 18.4 uptime= 79.55%
    burden_of_power                   : start= 27.0 refresh=  0.0 interval= 10.8 trigger= 10.8 duration=  1.9 uptime= 17.46%
    clearcasting                      : start= 23.6 refresh= 35.7 interval= 12.7 trigger=  5.1 duration=  8.3 uptime= 65.17%
    dark_embrace                      : start= 10.4 refresh=  1.0 interval= 30.5 trigger= 30.0 duration= 27.2 uptime= 93.77%
    enlightened_damage                : start= 15.0 refresh=  0.0 interval= 20.3 trigger= 21.2 duration= 11.5 uptime= 56.01%
    enlightened_mana                  : start= 14.3 refresh=  0.0 interval= 21.0 trigger= 21.0 duration=  9.3 uptime= 43.99%
    evocation                         : start=  3.3 refresh=  0.0 interval= 80.4 trigger= 80.4 duration=  2.3 uptime=  2.54%
    flask_of_alchemical_chaos_crit    : start=  2.1 refresh=  0.6 interval=114.5 trigger= 77.6 duration= 35.2 uptime= 24.40%
    flask_of_alchemical_chaos_haste   : start=  2.1 refresh=  0.7 interval=110.5 trigger= 74.2 duration= 36.2 uptime= 25.57%
    flask_of_alchemical_chaos_mastery : start=  2.1 refresh=  0.6 interval=111.1 trigger= 75.9 duration= 35.3 uptime= 24.96%
    flask_of_alchemical_chaos_vers    : start=  2.1 refresh=  0.6 interval=111.5 trigger= 77.2 duration= 35.1 uptime= 25.07%
    glorious_incandescence            : start= 26.8 refresh=  0.0 interval= 10.8 trigger= 10.8 duration=  1.2 uptime= 11.06%
    high_voltage                      : start= 86.8 refresh=169.3 interval=  3.4 trigger=  1.2 duration=  2.5 uptime= 72.49%
    impetus                           : start=  7.9 refresh=  3.7 interval= 34.9 trigger= 23.0 duration= 12.1 uptime= 31.82%
    incanters_flow                    : start=  1.0 refresh=  0.0 interval=  0.0 trigger=  0.0 duration=300.6 uptime=100.00%
    lingering_embers                  : start=  4.3 refresh= 14.2 interval= 77.3 trigger= 15.1 duration= 18.7 uptime= 26.81%
    mana_cascade                      : start=  1.1 refresh=153.1 interval=160.8 trigger=  1.9 duration=267.3 uptime= 98.32%
    nether_precision                  : start= 47.5 refresh=  5.4 interval=  6.3 trigger=  5.7 duration=  4.2 uptime= 65.78%
    overflowing_energy                : start=113.1 refresh=281.3 interval=  2.6 trigger=  0.8 duration=  1.8 uptime= 69.46%
    shadowed_essence                  : start=  9.9 refresh=  0.0 interval= 30.0 trigger= 30.0 duration= 26.7 uptime= 87.35%
    siphon_storm                      : start=  4.3 refresh= 25.7 interval= 80.3 trigger=  8.5 duration= 21.0 uptime= 29.92%
    spellfire_sphere                  : start=  1.6 refresh= 26.4 interval= 90.3 trigger= 10.8 duration=185.0 uptime= 99.56%
    spellfire_spheres                 : start= 27.8 refresh=136.6 interval= 10.9 trigger=  1.8 duration=  8.7 uptime= 80.89%
    tempered_potion                   : start=  1.3 refresh=  0.0 interval=323.9 trigger=  0.0 duration= 27.0 uptime= 11.58%
    time_warp                         : start=  3.6 refresh=  0.0 interval= 65.0 trigger= 65.0 duration=  5.9 uptime=  7.14%
    unstable_power_suit_core_crit     : start=  4.0 refresh=  0.0 interval= 60.8 trigger= 59.9 duration= 19.2 uptime= 25.39%
    unstable_power_suit_core_haste    : start=  3.8 refresh=  0.0 interval= 60.0 trigger= 58.5 duration= 19.1 uptime= 24.37%
    unstable_power_suit_core_mastery  : start=  3.9 refresh=  0.0 interval= 60.7 trigger= 59.1 duration= 19.4 uptime= 24.99%
    unstable_power_suit_core_vers     : start=  3.9 refresh=  0.0 interval= 60.1 trigger= 58.9 duration= 19.3 uptime= 25.26%
  Benefits:
      0.62% : Arcane Barrage Arcane Charge 0
      0.38% : Arcane Barrage Arcane Charge 1
      0.45% : Arcane Barrage Arcane Charge 2
      0.74% : Arcane Barrage Arcane Charge 3
     97.81% : Arcane Barrage Arcane Charge 4
      0.05% : Arcane Blast Arcane Charge 0  
      0.05% : Arcane Blast Arcane Charge 1  
      0.88% : Arcane Blast Arcane Charge 2  
      1.36% : Arcane Blast Arcane Charge 3  
     97.67% : Arcane Blast Arcane Charge 4  
  Up-Times:
     18.12% : Mana Cap                      
  Gains:
    8286513.8 : Arcane Barrage (mana)           (overflow=18.66%)
    18644652.1 : Arcane Surge   (mana)           (overflow=24.94%)
    26099281.5 : Mana Regen     (mana)           (overflow=22.15%)
    Pet "mirror_image" Gains:
    Pet "arcane_phoenix" Gains:
  Waiting:  0.01%


 *** Targets *** 

Target: Fluffy_Pillow humanoid tank_dummy unknown 83
  DTPS=672348.********** DTPS-Error=1325.4401556232874/0.20% DTPS-Range=87297.17053514661/12.98%
  DPR=0 RPS-Out=622208.********** RPS-In=0 Resource=health Waiting=0 ApM=0
  Core Stats:    strength=0|0(0)  agility=0|0(0)  stamina=0|0(0)  intellect=0|0(0)  spirit=0|0(0)  health=0|215470652  mana=0|0
  Generic Stats: mastery=0.00%|0.00%(0)  versatility=0.00%|0.00%(0)  leech=0.00%|0.00%(0)  runspeed=7.00%|7.00%(0)
  Spell Stats:   power=0|0(0)  hit=0.00%|0.00%(0)  crit=0.00%|0.00%(0)  haste=0.00%|0.00%(0)  speed=0.00%|0.00%  manareg=0|0(0)
  Attack Stats:  power=0|0(0)  hit=0.00%|0.00%(0)  crit=5.00%|5.00%(0)  expertise=0.00%|0.00%(0)  haste=0.00%|0.00%(0)  speed=0.00%|0.00%
  Defense Stats: armor=42857|42857(42857) miss=3.00%|3.00%  dodge=3.00%|3.00%(0)  parry=3.00%|3.00%(0)  block=3.00%|3.00%(0) crit=0.00%|0.00%  versatility=0.00%|0.00%(0)
  Priorities (actions.precombat):
    snapshot_stats
  Priorities (actions.default):

  Actions:

  Constant Buffs:
    arcane_intellect
  Dynamic Buffs:
    arcane_debilitation         : start=351.6 refresh=  0.0 interval= 18.7 trigger=  0.8 duration= 24.8 uptime= 85.87%
    magis_spark                 : start=  7.9 refresh=  0.0 interval= 39.7 trigger= 39.8 duration=  1.9 uptime=  4.99%
    magis_spark_arcane_barrage  : start=  7.9 refresh=  0.0 interval= 39.7 trigger= 39.8 duration=  1.0 uptime=  2.73%
    magis_spark_arcane_blast    : start=  7.9 refresh=  0.0 interval= 39.7 trigger= 39.8 duration=  2.5 uptime=  6.48%
    magis_spark_arcane_missiles : start=  7.9 refresh=  0.0 interval= 39.7 trigger= 39.8 duration=  1.9 uptime=  4.99%
    touch_of_the_magi           : start=  7.9 refresh=  0.0 interval= 39.7 trigger= 39.8 duration= 11.8 uptime= 31.00%
  Waiting:  0.00%


Baseline Performance:
  Networking    = enabled
  RNG Engine    = xoshiro256+
  Iterations    = 1445 (93, 90, 90, 92, 93, 89, 92, 90, 86, 87, 91, 85, 92, 91, 90, 94)
  TotalEvents   = 13091655
  MaxEventQueue = 136
  TargetHealth  = 215470653
  SimSeconds    = 429563.728
  CpuSeconds    = 7.578125
  WallSeconds   = 0.5188589
  InitSeconds   = 0.0178161
  MergeSeconds  = 0.003792
  AnalyzeSeconds= 0.0004317
  SpeedUp       = 57319
  EndTime       = 2024-09-08 06:32:03+0800 (**********)


Waiting:
     0.01% : 煤气罐超人

