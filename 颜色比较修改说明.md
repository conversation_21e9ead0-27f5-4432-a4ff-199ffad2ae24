# 颜色比较修改说明

## 修改概述
将所有 `GetColor` 后的颜色比较从完全相等（`==`）改为允许 ±5 误差的模糊匹配。

## 主要修改内容

### 1. 新增 `ColorMatch` 函数
```python
def ColorMatch(actual_color, expected_color, tolerance=5):
    """
    比较两个颜色是否在指定的误差范围内匹配
    actual_color: 实际获取的颜色 (4位十六进制字符串)
    expected_color: 期望的颜色 (4位十六进制字符串)
    tolerance: 允许的误差值 (默认为5)
    """
```

### 2. 函数工作原理
- 将4位十六进制颜色字符串分解为两个字节
- 分别比较每个字节的数值差异
- 如果所有字节的差异都在误差范围内，则认为颜色匹配

### 3. 修改的函数列表
以下函数中的所有颜色比较都已修改：

1. **ColorCheck** - 基础颜色检查函数
2. **Priest_Discipline** - 牧师戒律专精
3. **Rogue_Assasination** - 盗贼刺杀专精
4. **Rogue_Outlaw** - 盗贼狂徒专精
5. **Shaman_Enhancement** - 萨满增强专精
6. **Shaman_Elemental** - 萨满元素专精
7. **Deathknight_Unholy** - 死亡骑士邪恶专精
8. **Mage_Arcane** - 法师奥术专精
9. **Paladin_Retribution** - 圣骑士惩戒专精
10. **主循环中的职业检测** - 用于识别当前职业的颜色检测

### 4. 修改示例
**修改前：**
```python
if (GetColor(1135,623)=="E773") and (GetColor(1132,623)=="FBAD"):
    press_key('f11')
```

**修改后：**
```python
if (ColorMatch(GetColor(1135,623),"E773") and ColorMatch(GetColor(1132,623),"FBAD")):
    press_key('f11')
```

### 5. 优势
- **提高稳定性**：减少因屏幕亮度、显示器差异等导致的颜色检测失败
- **增强兼容性**：适应不同显示设备的颜色差异
- **保持精度**：±5的误差范围既提供了灵活性，又保持了足够的精确度

### 6. 测试验证
创建了 `test_color_match.py` 测试文件，验证了 `ColorMatch` 函数的正确性：
- 完全匹配测试
- 误差范围内测试
- 超出误差范围测试
- 边界情况测试
- 自定义误差范围测试

## 使用说明
- 默认误差范围为 ±5
- 如需调整误差范围，可以修改 `ColorMatch` 函数中的 `tolerance` 参数
- 所有原有功能保持不变，只是颜色匹配更加宽松和稳定

## 注意事项
- 修改后的代码向后兼容
- 不会影响程序的其他功能
- 建议在实际使用中测试效果，如有需要可以调整误差范围
